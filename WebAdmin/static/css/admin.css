/* NGC660 Bot Star Admin CSS */

:root {
    /* 主题变量 - 简约风格 */
    --primaryColor: #3498db;
    --secondaryColor: #2980b9;
    --accentColor: #3498db;
    --sidebarBg: #263445;
    --sidebarText: #ffffff;
    --contentBg: #ffffff;
    --contentText: #333333;
    --borderColor: #e0e0e0;
    --darkBorderColor: #2c3e50;
    --cardBg: #ffffff;
    --cardShadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    
    /* 插件状态相关颜色 */
    --enabled-color: #28a745;
    --disabled-color: #6c757d;
    --warning-color: #ffc107;
    
    /* 通用变量 */
    --sidebar-width: 220px;
    --header-height: 60px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    transition: all 0.3s ease;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    background-color: var(--contentBg);
    color: var(--contentText);
}

/* 侧边栏样式 */
#sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: var(--sidebar-width);
    z-index: 1000;
    transition: all 0.3s;
    overflow-y: auto;
    background-color: var(--sidebarBg);
    border-right: 1px solid var(--darkBorderColor);
    color: var(--sidebarText);
}

#sidebar.collapsed {
    left: calc(-1 * var(--sidebar-width) + 60px);
}

/* 内容区域 */
#content {
    position: relative;
    margin-left: var(--sidebar-width);
    padding-top: var(--header-height);
    min-height: 100vh;
    transition: all 0.3s;
}

#content.expanded {
    margin-left: 60px;
}

/* 顶部导航栏 */
#header {
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: var(--sidebar-width);
    right: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    padding: 0 20px;
    transition: all 0.3s;
    background-color: var(--contentBg);
    border-bottom: 1px solid var(--borderColor);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

#header.expanded {
    left: 60px;
}

/* 品牌容器 */
.brand-container {
    padding: 20px 15px;
    text-align: center;
    border-bottom: 1px solid var(--darkBorderColor);
}

/* Logo文字 */
.brand-logo {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--sidebarText);
}

.logo-version {
    font-size: 12px;
    opacity: 0.7;
}

/* 导航链接 */
.nav-links {
    padding: 0;
    margin: 15px 0;
    list-style: none;
}

.nav-item {
    margin: 5px 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
    color: var(--sidebarText);
}

.nav-link i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
    font-size: 16px;
}

.nav-link:hover {
    background-color: rgba(52, 152, 219, 0.2);
}

.nav-link.active {
    background-color: rgba(52, 152, 219, 0.3);
    border-left: 3px solid var(--primaryColor);
}

/* 分隔线 */
.nav-divider {
    height: 1px;
    margin: 15px 15px;
    background-color: var(--darkBorderColor);
}

/* 底部版权信息 */
.sidebar-footer {
    padding: 15px;
    text-align: center;
    font-size: 12px;
    opacity: 0.6;
    margin-top: 20px;
    border-top: 1px solid var(--darkBorderColor);
}

/* 侧边栏切换按钮 */
#toggle-sidebar {
    background: transparent;
    border: none;
    font-size: 20px;
    cursor: pointer;
    margin-right: 15px;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s;
    color: var(--contentText);
}

#toggle-sidebar:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

/* 页面标题 */
.header-title {
    flex-grow: 1;
    font-size: 18px;
    font-weight: 500;
}

/* 卡片样式 */
.card {
    border-radius: 8px;
    margin-bottom: 20px;
    transition: all 0.3s;
    border: none;
    background-color: var(--cardBg);
    box-shadow: var(--cardShadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 15px 20px;
    font-weight: 500;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    background-color: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid var(--borderColor);
}

.card-body {
    padding: 20px;
}

/* 状态卡片 */
.status-card {
    height: 100%;
    border-radius: 8px;
}

.status-icon {
    float: left;
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primaryColor);
}

.status-icon i {
    font-size: 20px;
    transition: transform 0.3s ease;
}

.card:hover .status-icon i {
    transform: scale(1.2);
}

.status-info {
    overflow: hidden;
}

.status-title {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
    opacity: 0.8;
    text-transform: uppercase;
}

.status-count {
    font-size: 24px;
    font-weight: 700;
    display: block;
    margin-bottom: 5px;
    color: var(--primaryColor);
}

.status-detail {
    font-size: 13px;
    opacity: 0.7;
}

/* 状态标签 */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 5px;
}

.status-badge.online {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.status-badge.offline {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

/* 图表卡片 */
.chart-card {
    border-radius: 8px;
}

.chart-container {
    min-height: 250px;
    width: 100%;
    position: relative;
}

/* 系统信息表格 */
.system-info table {
    margin-bottom: 0;
    width: 100%;
}

.system-info td {
    padding: 12px 15px;
    vertical-align: middle;
    border-bottom: 1px solid var(--borderColor);
}

.system-info td:first-child {
    opacity: 0.7;
    font-weight: 600;
    width: 35%;
}

.system-info tr:last-child td {
    border-bottom: none;
}

/* 插件统计卡片 */
.plugin-stat-card {
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    background-color: rgba(52, 152, 219, 0.05);
    border: 1px solid rgba(52, 152, 219, 0.1);
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--primaryColor);
}

.stat-label {
    font-size: 14px;
    opacity: 0.7;
}

/* 按钮样式 */
.btn-primary {
    background-color: var(--primaryColor);
    border-color: var(--primaryColor);
    padding: 8px 16px;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--secondaryColor);
    border-color: var(--secondaryColor);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

/* 动态按钮效果 */
button, .btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

button:active, .btn:active {
    transform: scale(0.95);
}

/* 动态涟漪效果 */
.ripple {
    position: absolute;
    border-radius: 50%;
    transform: scale(0);
    animation: ripple 0.6s linear;
    background-color: rgba(255, 255, 255, 0.4);
}

@keyframes ripple {
    to {
        transform: scale(2.5);
        opacity: 0;
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    #sidebar {
        left: -220px;
    }
    
    #content, #header {
        margin-left: 0;
        left: 0;
    }
    
    #sidebar.show-mobile {
        left: 0;
    }
}

/* 机器人头像样式 */
#botAvatarImg {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border: 3px solid var(--primaryColor);
    border-radius: 50%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

#botAvatarImg:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 增强机器人详情卡片 */
.bot-info {
    text-align: center;
    min-height: 250px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.bot-details {
    margin-top: 15px;
    width: 100%;
}

.bot-details h4 {
    margin-bottom: 15px;
    font-weight: 600;
    color: var(--primaryColor);
}

.bot-details p {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bot-details p i {
    width: 20px;
    margin-right: 10px;
    color: var(--primaryColor);
}

/* 增强的淡入动画 */
.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--contentBg);
}

::-webkit-scrollbar-thumb {
    background-color: #c2c9d6;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    opacity: 0.8;
}

/* Toast通知样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
    min-width: 280px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 表格样式 */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: inherit;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--borderColor);
}

/* 表格行悬停效果 */
.table-hover tbody tr {
    transition: all 0.3s ease;
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
    transform: translateX(5px);
}

/* 插件状态相关样式 */
.plugin-enabled {
    background-color: rgba(40, 167, 69, 0.05);
}

.plugin-enabled:hover {
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.plugin-enabled-not-loaded {
    background-color: rgba(255, 193, 7, 0.05);
}

.plugin-enabled-not-loaded:hover {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

.plugin-disabled {
    opacity: 0.85;
}

/* 分页样式 */
.pagination-container {
    background-color: #f8f9fa;
    border-top: 1px solid var(--borderColor);
}

.pagination-info {
    font-size: 0.9rem;
    color: #6c757d;
}

.pagination .page-link {
    color: var(--primaryColor);
    border-color: var(--borderColor);
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--secondaryColor);
    transform: translateY(-2px);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
    border-color: #dee2e6;
}

/* 页面加载指示器 */
.loader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: var(--primaryColor);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 快速操作卡片样式 */
.action-card {
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: var(--cardBg);
    border: 1px solid var(--borderColor);
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.action-icon {
    font-size: 24px;
    margin-bottom: 15px;
    color: var(--primaryColor);
    transition: transform 0.3s ease;
}

.action-card:hover .action-icon {
    transform: scale(1.2);
}

.action-title {
    font-size: 14px;
    font-weight: 500;
}

/* 表单元素动画 */
.form-control, .form-select {
    transition: all 0.3s ease;
    border: 1px solid var(--borderColor);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primaryColor);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    transform: translateY(-2px);
}

/* 按钮悬浮动画 */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.btn:hover::after {
    animation: ripple-effect 1s ease-out;
}

@keyframes ripple-effect {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.3;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

/* 插件详情样式 */
.plugin-header {
    border-bottom: 1px solid var(--borderColor);
    padding-bottom: 15px;
}

.plugin-header h3 {
    color: var(--primaryColor);
    transition: color 0.3s ease;
}

.plugin-meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.plugin-description {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    background-color: rgba(52, 152, 219, 0.05);
    padding: 10px;
    border-radius: 6px;
    margin-top: 10px;
    border-left: 3px solid var(--primaryColor);
}

/* 模态框动画效果 */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: scale(0.9);
}

.modal.show .modal-dialog {
    transform: scale(1);
}

/* 列表项动画 */
.list-group-item {
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: rgba(52, 152, 219, 0.05);
    transform: translateX(5px);
}

/* 任务和处理器列表样式 */
#detail-scheduled-tasks .list-group-item,
#detail-event-handlers .list-group-item {
    border-radius: 0;
    border-left: none;
    border-right: none;
}

/* 事件处理器和任务样式 */
.list-group-item h6 {
    color: var(--primaryColor);
    margin-bottom: 4px;
    font-size: 14px;
    font-weight: 600;
}

.list-group-item small {
    font-size: 12px;
    color: #666;
}

.list-group-item .badge {
    padding: 5px 8px;
    font-weight: normal;
    letter-spacing: 0.3px;
}

/* 模态框中空状态的样式 */
.list-group-item .text-center {
    padding: 10px 0;
    color: #888;
}

.list-group-item .text-center i {
    font-size: 24px;
    margin-bottom: 10px;
    opacity: 0.5;
} 
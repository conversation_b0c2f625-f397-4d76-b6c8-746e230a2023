/**
 * NGC660 Bot Star Admin JavaScript
 */

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化科技感动画
    initCyberEffects();
    
    // 初始化数字动画
    initNumberAnimations();
    
    // 添加卡片霓虹光效果
    initNeonEffects();
    
    // 为状态指示器添加闪烁效果
    initStatusIndicators();
});

/**
 * 初始化科技感动画效果
 */
function initCyberEffects() {
    // 为特定元素添加悬停光晕效果
    const glowElements = document.querySelectorAll('.card, .btn-primary');
    glowElements.forEach(element => {
        element.classList.add('glow-effect');
    });
    
    // 为重要统计数字添加霓虹文字效果
    const importantNumbers = document.querySelectorAll('.status-count, .stat-value');
    importantNumbers.forEach(element => {
        element.classList.add('neon-text');
    });
    
    // 为状态卡片添加脉冲阴影效果
    const statusCards = document.querySelectorAll('.status-card');
    statusCards.forEach((card, index) => {
        // 错开动画
        setTimeout(() => {
            card.classList.add('pulse-glow');
        }, index * 200);
    });
}

/**
 * 初始化数字动画效果
 */
function initNumberAnimations() {
    // 获取所有需要动画的数字元素
    const animatedNumbers = document.querySelectorAll('.status-count, .stat-value');
    
    // 存储原始值用于比较
    animatedNumbers.forEach(element => {
        element.dataset.prevValue = element.textContent;
        element.classList.add('number-animate');
    });
    
    // 设置观察器来检测数字变化
    setInterval(() => {
        animatedNumbers.forEach(element => {
            if (element.textContent !== element.dataset.prevValue) {
                // 数字变化了，触发动画
                element.classList.add('changed');
                
                // 更新保存的值
                element.dataset.prevValue = element.textContent;
                
                // 移除动画类，以便下次使用
                setTimeout(() => {
                    element.classList.remove('changed');
                }, 500);
            }
        });
    }, 1000);
}

/**
 * 初始化霓虹灯效果
 */
function initNeonEffects() {
    // 为标题添加霓虹文字效果
    const titles = document.querySelectorAll('.card-header h5, .section-title');
    titles.forEach(title => {
        const icon = title.querySelector('i');
        if (icon) {
            icon.classList.add('neon-text');
        }
    });
}

/**
 * 初始化状态指示器效果
 */
function initStatusIndicators() {
    // 为在线状态徽章添加脉冲动画
    const onlineIndicators = document.querySelectorAll('.status-badge.online');
    onlineIndicators.forEach(indicator => {
        // 创建脉冲效果
        const pulse = document.createElement('span');
        pulse.className = 'pulse-effect';
        indicator.prepend(pulse);
    });
}

/**
 * 显示带科技感的通知
 * @param {string} title - 通知标题
 * @param {string} message - 通知内容
 * @param {string} type - 通知类型 (success, danger, warning, info)
 */
function showCyberToast(title, message, type = 'primary') {
    // 检查是否已有全局toast函数
    if (typeof window.showToast === 'function') {
        window.showToast(title, message, type);
        return;
    }
    
    // 自定义toast实现
    const container = document.querySelector('.toast-container') || (() => {
        const newContainer = document.createElement('div');
        newContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(newContainer);
        return newContainer;
    })();
    
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast cyber-bg border-${type}`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    // 设置颜色
    let iconClass = 'info-circle';
    if (type === 'success') iconClass = 'check-circle';
    if (type === 'danger') iconClass = 'exclamation-circle';
    if (type === 'warning') iconClass = 'exclamation-triangle';
    
    // 设置内容
    toast.innerHTML = `
        <div class="toast-header cyber-bg text-light">
            <i class="fas fa-${iconClass} me-2 text-${type}"></i>
            <strong class="me-auto">${title}</strong>
            <small>刚刚</small>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body text-light">
            ${message}
        </div>
    `;
    
    // 添加到容器
    container.appendChild(toast);
    
    // 使用Bootstrap的Toast组件
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 5000
    });
    
    // 显示toast
    bsToast.show();
    
    // 添加进入动画
    toast.style.animation = 'fadeIn 0.3s ease-out forwards';
    
    // 自动移除
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

/**
 * 将数字格式化为带单位的字符串
 * @param {number} value - 要格式化的数字
 * @param {string} unit - 单位 (例如'B', 'KB', 'MB', 'GB')
 * @param {number} decimals - 小数点后位数
 * @returns {string} 格式化后的字符串
 */
function formatNumber(value, unit = '', decimals = 2) {
    return value.toFixed(decimals) + (unit ? ' ' + unit : '');
}

/**
 * 为指定元素添加科技感打字机效果
 * @param {HTMLElement} element - 目标元素
 * @param {string} text - 要输入的文本
 * @param {number} speed - 打字速度 (毫秒)
 */
function typeWriter(element, text, speed = 50) {
    let i = 0;
    element.textContent = '';
    
    function type() {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// 导出函数供全局使用
window.showCyberToast = showCyberToast;
window.formatNumber = formatNumber;
window.typeWriter = typeWriter; 
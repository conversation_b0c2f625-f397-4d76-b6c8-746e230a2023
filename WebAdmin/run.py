from WebAdmin import create_app
import Config.ConfigServer as Cs
from threading import Thread
from loguru import logger
import sys


def runWebAdmin():
    """启动Web管理后台"""
    # 从配置文件读取Web配置
    configData = Cs.returnConfigData()
    webConfig = configData.get('WebAdminConfig', {})
    
    # 检查是否启用Web管理后台
    if not webConfig.get('enabled', False):
        logger.warning("Web管理后台未启用，请在config.toml中设置WebAdminConfig.enabled = true")
        return False
    
    # 创建Flask应用
    app = create_app()
    
    # 初始化Session扩展
    try:
        from flask_session import Session
        Session(app)
        logger.info("Flask-Session已初始化")
    except ImportError:
        logger.warning("Flask-Session未安装，将使用默认会话管理")
    
    host = webConfig.get('webHost', '127.0.0.1')
    port = int(webConfig.get('webPort', 8080))
    
    try:
        # 禁用Werkzeug的访问日志
        import logging
        werkzeug_logger = logging.getLogger('werkzeug')
        werkzeug_logger.setLevel(logging.ERROR)
        
        logger.info(f"Web管理后台启动在 http://{host}:{port}")
        app.run(host=host, port=port, threaded=True)
        return True
    except Exception as e:
        logger.error(f"启动Web管理后台失败: {e}")
        return False


def startWebAdminThread():
    """在新线程中启动Web管理后台"""
    webThread = Thread(target=runWebAdmin, daemon=True)
    webThread.start()
    return webThread


if __name__ == '__main__':
    # 如果直接运行此文件，则在主线程中启动
    import signal
    
    def shutdownHandler(signum, frame):
        """处理退出信号"""
        logger.warning(f"收到退出信号 {signum}，正在关闭Web管理后台...")
        sys.exit(0)
    
    # 只在主线程中注册信号处理器
    signal.signal(signal.SIGINT, shutdownHandler)
    signal.signal(signal.SIGTERM, shutdownHandler)
    
    runWebAdmin() 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NGC660 Bot Star - 登录</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css">
    <style>
        :root {
            /* 暗色主题变量 */
            --primary-color-dark: #3a0ca3;
            --secondary-color-dark: #4361ee;
            --accent-color-dark: #7209b7;
            --background-dark: #121212;
            --card-bg-dark: rgba(25, 25, 36, 0.8);
            --text-light: #f8f9fa;
            
            /* 亮色主题变量 */
            --primary-color-light: #4361ee;
            --secondary-color-light: #3a0ca3;
            --accent-color-light: #7209b7;
            --background-light: #f8f9fa;
            --card-bg-light: rgba(255, 255, 255, 0.8);
            --text-dark: #212529;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
            margin: 0;
            padding: 0;
            transition: all 0.3s ease;
        }
        
        body.dark-mode {
            background-color: var(--background-dark);
            color: var(--text-light);
        }
        
        body.light-mode {
            background-color: var(--background-light);
            color: var(--text-dark);
        }
        
        .particles-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        
        .login-container {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        
        .login-card {
            border-radius: 15px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            padding: 40px;
            width: 400px;
            max-width: 90%;
            transition: all 0.3s ease-in-out;
            position: relative;
            overflow: hidden;
        }
        
        .dark-mode .login-card {
            background: var(--card-bg-dark);
        }
        
        .light-mode .login-card {
            background: var(--card-bg-light);
        }
        
        .login-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px 0 rgba(31, 38, 135, 0.5);
        }
        
        .theme-toggle {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 10;
            cursor: pointer;
            background: transparent;
            border: none;
            font-size: 20px;
            color: inherit;
            transition: all 0.3s ease;
        }
        
        .theme-toggle:hover {
            transform: rotate(45deg);
        }
        
        .brand-logo {
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: bold;
        }
        
        .dark-mode .brand-logo {
            color: var(--text-light);
            text-shadow: 0 0 10px rgba(66, 99, 235, 0.7);
        }
        
        .light-mode .brand-logo {
            color: var(--text-dark);
            text-shadow: 0 0 10px rgba(66, 99, 235, 0.3);
        }
        
        .logo-text {
            background: linear-gradient(45deg, #4cc9f0, #4361ee, #7209b7);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: 800;
        }
        
        .glow-border {
            transition: all 0.3s ease;
        }
        
        .dark-mode .glow-border {
            border: 2px solid;
            border-image-slice: 1;
            border-image-source: linear-gradient(45deg, #4cc9f0, #4361ee, #7209b7);
            border-radius: 5px;
        }
        
        .light-mode .glow-border {
            border: 2px solid;
            border-image-slice: 1;
            border-image-source: linear-gradient(45deg, #4cc9f0, #4361ee, #7209b7);
            border-radius: 5px;
        }
        
        .form-control {
            padding: 12px 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            transition: all 0.2s;
        }
        
        .dark-mode .form-control {
            background-color: rgba(30, 30, 45, 0.9);
            border: none;
            color: var(--text-light);
        }
        
        .light-mode .form-control {
            background-color: rgba(240, 240, 245, 0.9);
            border: none;
            color: var(--text-dark);
        }
        
        .dark-mode .form-control:focus {
            background-color: rgba(40, 40, 55, 0.9);
            box-shadow: 0 0 15px rgba(74, 97, 238, 0.5);
            color: white;
        }
        
        .light-mode .form-control:focus {
            background-color: rgba(250, 250, 255, 0.9);
            box-shadow: 0 0 15px rgba(74, 97, 238, 0.5);
            color: var(--text-dark);
        }
        
        .btn-primary {
            border: none;
            padding: 12px;
            font-weight: 600;
            letter-spacing: 1px;
            border-radius: 8px;
            margin-top: 10px;
            transition: all 0.3s;
        }
        
        .dark-mode .btn-primary {
            background: linear-gradient(45deg, #4361ee, #7209b7);
        }
        
        .light-mode .btn-primary {
            background: linear-gradient(45deg, #3a0ca3, #4361ee);
        }
        
        .btn-primary:hover, .btn-primary:focus {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        
        .dark-mode .btn-primary:hover, .dark-mode .btn-primary:focus {
            background: linear-gradient(45deg, #3a0ca3, #560bad);
        }
        
        .light-mode .btn-primary:hover, .light-mode .btn-primary:focus {
            background: linear-gradient(45deg, #4361ee, #7209b7);
        }
        
        .form-check-input {
            transition: all 0.3s;
        }
        
        .dark-mode .form-check-input {
            background-color: rgba(30, 30, 45, 0.9);
            border-color: rgba(255, 255, 255, 0.2);
        }
        
        .light-mode .form-check-input {
            background-color: rgba(240, 240, 245, 0.9);
            border-color: rgba(0, 0, 0, 0.2);
        }
        
        .form-check-input:checked {
            background-color: var(--accent-color-dark);
            border-color: var(--accent-color-dark);
        }
        
        .alert {
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 20px;
        }
        
        .dark-mode .alert {
            background-color: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #ff758f;
        }
        
        .light-mode .alert {
            background-color: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }
        
        .input-icon {
            position: relative;
        }
        
        .input-icon i {
            position: absolute;
            top: 16px;
            left: 15px;
        }
        
        .dark-mode .input-icon i {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .light-mode .input-icon i {
            color: rgba(0, 0, 0, 0.6);
        }
        
        .input-icon input {
            padding-left: 40px;
        }
        
        /* 动画背景元素 */
        .cyber-cube {
            position: absolute;
            background: transparent;
            border-radius: 10px;
            filter: blur(2px);
            animation: float 6s ease-in-out infinite;
            z-index: -1;
        }
        
        .dark-mode .cyber-cube {
            background: rgba(66, 99, 235, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .light-mode .cyber-cube {
            background: rgba(74, 97, 238, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .cyber-cube:nth-child(1) {
            top: 15%;
            left: 15%;
            width: 100px;
            height: 100px;
            animation-delay: 0s;
        }
        
        .cyber-cube:nth-child(2) {
            top: 25%;
            right: 20%;
            width: 150px;
            height: 150px;
            animation-delay: 1s;
        }
        
        .cyber-cube:nth-child(3) {
            bottom: 20%;
            left: 25%;
            width: 80px;
            height: 80px;
            animation-delay: 2s;
        }
        
        .cyber-cube:nth-child(4) {
            bottom: 30%;
            right: 25%;
            width: 120px;
            height: 120px;
            animation-delay: 3s;
        }
        
        /* 光晕效果 */
        .glow {
            position: absolute;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            filter: blur(30px);
            z-index: -2;
            opacity: 0.5;
            animation: pulse 8s infinite alternate;
        }
        
        .dark-mode .glow {
            background: radial-gradient(circle, rgba(114, 9, 183, 0.6) 0%, rgba(67, 97, 238, 0.3) 50%, rgba(0, 0, 0, 0) 70%);
        }
        
        .light-mode .glow {
            background: radial-gradient(circle, rgba(114, 9, 183, 0.3) 0%, rgba(67, 97, 238, 0.2) 50%, rgba(0, 0, 0, 0) 70%);
        }
        
        .glow-1 {
            top: -50px;
            left: -50px;
            animation-delay: 0s;
        }
        
        .glow-2 {
            bottom: -50px;
            right: -50px;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(10deg);
            }
            100% {
                transform: translateY(0) rotate(0deg);
            }
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.5;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 0.5;
            }
        }
        
        /* 登录按钮悬停特效 */
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(67, 97, 238, 0.2), rgba(114, 9, 183, 0.1));
            z-index: -1;
            transition: all 0.4s;
            transform: scaleX(0);
            transform-origin: 0 50%;
            border-radius: 8px;
        }
        
        .btn-primary:hover::before {
            transform: scaleX(1);
        }
        
        /* 光标动画 */
        .cursor-fx {
            position: fixed;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            pointer-events: none;
            mix-blend-mode: difference;
            z-index: 9999;
            transform: translate(-50%, -50%);
            transition: transform 0.1s;
        }
        
        .dark-mode .cursor-fx {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .light-mode .cursor-fx {
            background: rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body class="dark-mode">
    <div class="particles-background" id="particlesJs"></div>
    <div class="cyber-cube"></div>
    <div class="cyber-cube"></div>
    <div class="cyber-cube"></div>
    <div class="cyber-cube"></div>
    <div class="glow glow-1"></div>
    <div class="glow glow-2"></div>
    
    <div class="login-container">
        <div class="login-card">
            <button class="theme-toggle" id="themeToggle">
                <i class="fas fa-sun"></i>
            </button>
            
            <div class="brand-logo">
                <span class="logo-text">NGC660 Bot Star</span>
                <div class="mt-2" style="font-size: 14px; opacity: 0.7;">Web管理控制台</div>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="post" action="{{ url_for('auth.login') }}">
                <div class="input-icon">
                    <i class="fas fa-user"></i>
                    <input type="text" name="username" class="form-control glow-border" placeholder="用户名" required autocomplete="off">
                </div>
                
                <div class="input-icon">
                    <i class="fas fa-lock"></i>
                    <input type="password" name="password" class="form-control glow-border" placeholder="密码" required>
                </div>
                
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" name="remember" id="remember">
                    <label class="form-check-label" for="remember">记住我</label>
                </div>
                
                <button type="submit" class="btn btn-primary w-100 position-relative">登 录</button>
            </form>
            
            <div class="text-center mt-4" style="font-size: 12px; opacity: 0.6;">
                <p>NGC660安全实验室 © 2023-2024</p>
            </div>
        </div>
    </div>
    
    <div class="cursor-fx" id="cursorFx"></div>
    
    <!-- 核心JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    
    <script>
        // 主题切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('themeToggle');
            const body = document.body;
            const themeIcon = themeToggle.querySelector('i');
            
            // 检查本地存储中的主题设置
            const savedTheme = localStorage.getItem('ngcBotTheme');
            if (savedTheme) {
                body.className = savedTheme;
                updateThemeIcon();
            }
            
            // 主题切换事件
            themeToggle.addEventListener('click', function() {
                if (body.classList.contains('dark-mode')) {
                    body.classList.remove('dark-mode');
                    body.classList.add('light-mode');
                    localStorage.setItem('ngcBotTheme', 'light-mode');
                } else {
                    body.classList.remove('light-mode');
                    body.classList.add('dark-mode');
                    localStorage.setItem('ngcBotTheme', 'dark-mode');
                }
                updateThemeIcon();
            });
            
            // 更新主题图标
            function updateThemeIcon() {
                if (body.classList.contains('dark-mode')) {
                    themeIcon.className = 'fas fa-sun';
                } else {
                    themeIcon.className = 'fas fa-moon';
                }
            }
            
            // 初始化粒子效果
            if (typeof particlesJS !== 'undefined') {
                particlesJS('particlesJs', {
                    particles: {
                        number: { value: 80, density: { enable: true, value_area: 800 } },
                        color: { value: '#7209b7' },
                        shape: { type: 'circle' },
                        opacity: { value: 0.5, random: true },
                        size: { value: 3, random: true },
                        line_linked: {
                            enable: true,
                            distance: 150,
                            color: '#4361ee',
                            opacity: 0.4,
                            width: 1
                        },
                        move: {
                            enable: true,
                            speed: 2,
                            direction: 'none',
                            random: true,
                            out_mode: 'out'
                        }
                    },
                    interactivity: {
                        detect_on: 'canvas',
                        events: {
                            onhover: { enable: true, mode: 'grab' },
                            onclick: { enable: true, mode: 'push' }
                        },
                        modes: {
                            grab: { distance: 140, line_linked: { opacity: 1 } },
                            push: { particles_nb: 4 }
                        }
                    }
                });
            }
            
            // 光标特效
            const cursorFx = document.getElementById('cursorFx');
            
            document.addEventListener('mousemove', (e) => {
                if (cursorFx) {
                    cursorFx.style.transform = `translate(${e.clientX}px, ${e.clientY}px)`;
                }
            });
            
            // 提升交互体验
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    if (cursorFx) {
                        cursorFx.style.transform = 'scale(1.5)';
                        setTimeout(() => {
                            cursorFx.style.transform = 'scale(1)';
                        }, 300);
                    }
                });
            });
        });
    </script>
</body>
</html> 
{% extends "base.html" %}

{% block title %}插件管理 - NGC660 Bot Star{% endblock %}

{% block header_title %}插件管理{% endblock %}

{% block content %}
<div class="plugins-container">
    <!-- 调试区域 -->
    <div class="row mb-3 bg-dark p-3 rounded">
        <div class="col-12">
            <h5 class="text-warning"><i class="fas fa-bug me-2"></i> 调试区域</h5>
            <p class="text-light">插件列表加载问题诊断</p>
            <div class="mb-2">
                <button id="debug-api-btn" class="btn btn-warning mb-2">
                    <i class="fas fa-sync-alt me-1"></i> 手动调用API
                </button>
                <a href="/plugins/debug" target="_blank" class="btn btn-info mb-2 ms-2">
                    <i class="fas fa-wrench me-1"></i> 打开调试页面
                </a>
                <button id="toggle-debug-data" class="btn btn-secondary mb-2 ms-2">
                    <i class="fas fa-code me-1"></i> 显示/隐藏插件原始数据
                </button>
            </div>
            <div class="bg-dark text-light p-2 border rounded mb-2">
                <p class="mb-1"><strong>当前页面URL:</strong> <span id="current-url"></span></p>
                <p class="mb-1"><strong>API完整路径:</strong> <span id="api-full-path"></span></p>
                <p class="mb-0"><strong>状态:</strong> <span id="debug-status">未测试</span></p>
            </div>
            <div id="debug-raw-data" class="bg-dark text-light p-2 border rounded mb-2" style="display: none; max-height: 300px; overflow: auto;">
                <p><strong>原始插件数据:</strong></p>
                <pre id="debug-raw-json">无数据</pre>
            </div>
        </div>
    </div>

    <!-- 插件列表头部 -->
    <div class="row mb-3">
        <div class="col-md-6">
            <h4 class="section-title"><i class="fas fa-puzzle-piece me-2"></i> 插件列表</h4>
        </div>
        <div class="col-md-6 text-end">
            <div class="input-group">
                <input type="text" id="plugin-search" class="form-control" placeholder="搜索插件...">
                <button class="btn btn-primary" id="refresh-plugins">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>
    </div>
    
    <!-- 插件列表 -->
    <div class="card chart-card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-plugins">
                    <thead>
                        <tr>
                            <th width="20%">插件名称</th>
                            <th width="12%">版本</th>
                            <th width="13%">作者</th>
                            <th width="25%">描述</th>
                            <th width="10%">状态</th>
                            <th width="20%">操作</th>
                        </tr>
                    </thead>
                    <tbody id="plugin-list">
                        <!-- 插件列表将通过JavaScript动态加载 -->
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载插件列表...</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- 分页导航 -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="pagination-info">
                    显示 <span id="pagination-start">0</span> 到 <span id="pagination-end">0</span> 条，共 <span id="pagination-total">0</span> 条
                </div>
                <nav aria-label="插件分页导航">
                    <ul class="pagination mb-0">
                        <li class="page-item" id="pagination-prev">
                            <a class="page-link" href="#" aria-label="上一页">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <span class="page-link" id="pagination-current">1</span>
                        </li>
                        <li class="page-item" id="pagination-next">
                            <a class="page-link" href="#" aria-label="下一页">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
    
    <!-- 插件详情模态框 -->
    <div class="modal fade" id="pluginDetailModal" tabindex="-1" aria-labelledby="pluginDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content bg-white">
                <div class="modal-header">
                    <h5 class="modal-title" id="pluginDetailModalLabel"><i class="fas fa-puzzle-piece me-2"></i> 插件详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="plugin-header mb-4">
                        <h3 id="detail-plugin-name" class="mb-2">插件名称</h3>
                        <div class="plugin-meta">
                            <span class="badge bg-primary me-2" id="detail-plugin-version">v1.0.0</span>
                            <span class="text-muted" id="detail-plugin-author"><i class="fas fa-user me-1"></i> 作者: 未知</span>
                            <span class="ms-3" id="detail-plugin-status"></span>
                        </div>
                        <p class="mt-3 plugin-description" id="detail-plugin-description">插件描述...</p>
                    </div>
                    
                    <div class="row">
                        <!-- 定时任务 -->
                        <div class="col-md-6 mb-3">
                            <h5><i class="fas fa-clock me-2"></i> 定时任务</h5>
                            <div class="card border">
                                <div class="card-body p-0">
                                    <div id="detail-scheduled-tasks" class="list-group list-group-flush">
                                        <div class="list-group-item border">
                                            <div class="text-center py-3">
                                                <i class="fas fa-calendar-times mb-2"></i>
                                                <p class="mb-0">没有定时任务</p>
                                                <small>该插件未注册任何定时任务</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 事件处理器 -->
                        <div class="col-md-6">
                            <h5><i class="fas fa-bolt me-2"></i> 事件处理器</h5>
                            <div class="card border">
                                <div class="card-body p-0">
                                    <div id="detail-event-handlers" class="list-group list-group-flush">
                                        <div class="list-group-item border">
                                            <div class="text-center py-3">
                                                <i class="fas fa-times-circle mb-2"></i>
                                                <p class="mb-0">没有事件处理器</p>
                                                <small>该插件未注册任何事件处理器</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> 关闭
                    </button>
                    <button type="button" class="btn btn-primary" id="detail-action-btn">
                        <i class="fas fa-power-off me-1"></i> 启用
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 添加全局错误处理
    window.addEventListener('error', function(event) {
        console.error('全局JavaScript错误:', event.error);
        alert('JavaScript错误: ' + event.error.message + '\n请查看控制台获取详细信息。');
    });

    // 立即执行一个简单的自检代码，确认JavaScript能正常运行
    console.log('==================');
    console.log('插件页面JavaScript加载检查点');
    console.log('时间:', new Date().toISOString());
    console.log('URL:', window.location.href);
    console.log('==================');

    // 当前插件列表数据
    let pluginsData = [];
    // 当前选中的插件（用于详情模态框）
    let selectedPlugin = null;
    // 模态框实例
    let pluginDetailModal = null;
    
    // 分页相关变量
    let currentPage = 1;
    const pageSize = 10;
    let totalPages = 1;
    let filteredPlugins = []; // 用于保存搜索过滤后的插件
    
    // 初始化模态框
    function initModal() {
        const modalElement = document.getElementById('pluginDetailModal');
        // 确保旧实例被销毁
        const oldInstance = bootstrap.Modal.getInstance(modalElement);
        if (oldInstance) {
            oldInstance.dispose();
        }
        // 创建新实例
        pluginDetailModal = new bootstrap.Modal(modalElement);
        
        // 绑定模态框隐藏事件，确保清理
        modalElement.addEventListener('hidden.bs.modal', function () {
            // 清除选中的插件
            selectedPlugin = null;
            // 停止所有可能的动画
            const actionBtn = document.getElementById('detail-action-btn');
            const icon = actionBtn.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-spin');
            }
            actionBtn.disabled = false;
        });
        
        // 设置详情模态框中的操作按钮事件
        document.getElementById('detail-action-btn').addEventListener('click', handlePluginAction);
    }
    
    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
        console.log("页面初始化 - DOMContentLoaded 事件触发");
        
        // Bootstrap检查
        if (typeof bootstrap === 'undefined') {
            console.error("Bootstrap未定义！模态框初始化将失败");
            alert("页面加载错误：Bootstrap库未找到。请刷新页面或联系管理员。");
            return;
        } else {
            console.log("Bootstrap库已加载:", bootstrap.Modal ? "Modal组件可用" : "Modal组件不可用");
        }
        
        // 显示当前页面URL
        document.getElementById('current-url').textContent = window.location.href;
        // 显示API完整路径
        const apiUrl = new URL('/plugins/api/list', window.location.origin).href;
        document.getElementById('api-full-path').textContent = apiUrl;
        
        // 初始化模态框
        initModal();
        console.log("模态框初始化完成");
        
        // 加载插件列表
        console.log("准备加载插件列表...");
        loadPlugins();
        console.log("loadPlugins()函数已调用");
        
        // 设置刷新按钮事件
        const refreshButton = document.getElementById('refresh-plugins');
        refreshButton.addEventListener('click', function() {
            // 添加旋转动画
            const icon = this.querySelector('i');
            icon.style.animation = 'spin 1s linear infinite';
            
            // 1秒后停止动画并刷新
            setTimeout(() => {
                loadPlugins();
                setTimeout(() => {
                    icon.style.animation = '';
                }, 500);
            }, 700);
        });
        
        // 设置调试按钮事件
        document.getElementById('debug-api-btn').addEventListener('click', testApiCall);
        
        // 测试API调用
        function testApiCall() {
            const statusEl = document.getElementById('debug-status');
            statusEl.textContent = '正在测试...';
            statusEl.className = 'text-info';
            
            const apiUrl = new URL('/plugins/api/list', window.location.origin).href;
            console.log("测试API调用:", apiUrl);
            
            fetch('/plugins/api/list')
                .then(response => {
                    console.log("API测试 - 收到响应:", response.status, response.statusText);
                    
                    if (!response.ok) {
                        statusEl.textContent = `错误: ${response.status} ${response.statusText}`;
                        statusEl.className = 'text-danger';
                        return;
                    }
                    
                    return response.json();
                })
                .then(data => {
                    if (!data) return;
                    
                    console.log("API测试 - 响应数据:", data);
                    
                    if (data.plugins && Array.isArray(data.plugins)) {
                        statusEl.textContent = `成功: 返回了 ${data.plugins.length} 个插件`;
                        statusEl.className = 'text-success';
                        // 更新原始数据显示
                        document.getElementById('debug-raw-json').textContent = JSON.stringify(data, null, 2);
                    } else if (Array.isArray(data)) {
                        statusEl.textContent = `成功: 返回了 ${data.length} 个插件 (旧格式)`;
                        statusEl.className = 'text-success';
                        // 更新原始数据显示
                        document.getElementById('debug-raw-json').textContent = JSON.stringify(data, null, 2);
                    } else {
                        statusEl.textContent = `成功: 但返回了意外的数据格式`;
                        statusEl.className = 'text-warning';
                        // 更新原始数据显示
                        document.getElementById('debug-raw-json').textContent = JSON.stringify(data, null, 2);
                    }
                })
                .catch(error => {
                    console.error("API测试 - 错误:", error);
                    statusEl.textContent = `错误: ${error.message}`;
                    statusEl.className = 'text-danger';
                });
        }
        
        // 设置切换调试数据按钮事件
        document.getElementById('toggle-debug-data').addEventListener('click', function() {
            const debugData = document.getElementById('debug-raw-data');
            if (debugData.style.display === 'none') {
                debugData.style.display = 'block';
                debugData.classList.add('fade-in');
            } else {
                debugData.style.display = 'none';
                debugData.classList.remove('fade-in');
            }
        });
        
        // 设置搜索功能
        document.getElementById('plugin-search').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            
            if (!pluginsData || pluginsData.length === 0) return;
            
            if (searchTerm === '') {
                filteredPlugins = [...pluginsData];
                currentPage = 1;
                renderPluginList(filteredPlugins);
                return;
            }
            
            filteredPlugins = pluginsData.filter(plugin => {
                return (
                    plugin.name.toLowerCase().includes(searchTerm) ||
                    (plugin.description && plugin.description.toLowerCase().includes(searchTerm)) ||
                    (plugin.author && plugin.author.toLowerCase().includes(searchTerm))
                );
            });
            
            currentPage = 1;
            renderPluginList(filteredPlugins);
        });
        
        // 设置分页控件事件监听
        document.getElementById('pagination-prev').addEventListener('click', function(e) {
            e.preventDefault();
            if (currentPage > 1) {
                currentPage--;
                renderPluginList(filteredPlugins);
            }
        });
        
        document.getElementById('pagination-next').addEventListener('click', function(e) {
            e.preventDefault();
            if (currentPage < totalPages) {
                currentPage++;
                renderPluginList(filteredPlugins);
            }
        });
    });
    
    // 加载插件列表
    function loadPlugins() {
        console.log("加载插件列表...");
        
        // 显示加载中状态
        document.getElementById('plugin-list').innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载插件列表...</p>
                </td>
            </tr>
        `;
        
        document.getElementById('debug-status').textContent = '正在请求...';
        document.getElementById('debug-status').className = 'text-info';
        
        fetch('/plugins/api/list')
            .then(response => {
                console.log("API响应状态:", response.status);
                document.getElementById('debug-status').textContent = `API状态: ${response.status} ${response.statusText}`;
                if (!response.ok) {
                    document.getElementById('debug-status').className = 'text-danger';
                    throw new Error(`API响应错误: ${response.status} ${response.statusText}`);
                }
                document.getElementById('debug-status').className = 'text-success';
                return response.json();
            })
            .then(data => {
                console.log("API返回数据:", data);
                
                // 更新原始数据显示
                document.getElementById('debug-raw-json').textContent = JSON.stringify(data, null, 2);
                
                // 检查数据有效性
                if (!data) {
                    document.getElementById('debug-status').textContent = 'API返回无效数据';
                    document.getElementById('debug-status').className = 'text-danger';
                    throw new Error('API返回无效数据');
                }
                
                // 保存并渲染插件数据
                if (data.plugins && Array.isArray(data.plugins)) {
                    pluginsData = data.plugins;
                } else if (Array.isArray(data)) {
                    pluginsData = data;
                } else {
                    pluginsData = [];
                    console.error('未知的API数据格式');
                }
                
                // 初始化过滤后的插件列表和当前页
                filteredPlugins = [...pluginsData];
                currentPage = 1;
                
                // 渲染插件列表
                renderPluginList(pluginsData);
                
                // 显示成功状态
                document.getElementById('debug-status').textContent = `成功加载 ${pluginsData.length} 个插件`;
                document.getElementById('debug-status').className = 'text-success';
            })
            .catch(error => {
                console.error("加载插件列表失败:", error);
                document.getElementById('debug-status').textContent = `错误: ${error.message}`;
                document.getElementById('debug-status').className = 'text-danger';
                
                // 使用备用数据
                useMockData();
            });
    }

    // 使用模拟数据（当API调用失败时）
    function useMockData() {
        console.log("使用备用数据");
        
        // 模拟插件数据
        const mockPlugins = [
            {
                name: 'ChatGPT',
                version: '1.0.0',
                author: 'NGC660',
                description: 'ChatGPT聊天机器人插件',
                status: 'enabled',
                loaded: true,
                folder: 'ChatGPT',
                scheduled_tasks: [
                    {
                        name: '定时清理会话',
                        interval: 3600,
                        next_run: '2023-12-01 12:00:00'
                    },
                    '每日问候'
                ],
                event_handlers: ['on_message', 'on_friend_request']
            },
            {
                name: 'ImageSearch',
                version: '0.9.5',
                author: 'NGC660',
                description: '图片搜索插件',
                status: 'disabled',
                loaded: false,
                folder: 'ImageSearch',
                scheduled_tasks: [],
                event_handlers: ['on_image_message']
            },
            {
                name: 'WeatherBot',
                version: '1.2.0',
                author: 'NGC660',
                description: '天气预报插件',
                status: 'enabled',
                loaded: false,  // 已启用但未加载
                folder: 'WeatherBot',
                scheduled_tasks: [
                    {
                        name: '每日天气推送',
                        at_time: '08:00',
                        next_run: '明天 08:00'
                    }
                ],
                event_handlers: ['on_keyword_weather']
            },
            {
                name: 'TranslateBot',
                version: '1.0.0',
                author: 'NGC660',
                description: '多语言翻译插件',
                status: 'enabled',
                loaded: true,
                folder: 'TranslateBot',
                scheduled_tasks: [],
                event_handlers: ['on_command_translate']
            },
            {
                name: 'ReminderBot',
                version: '0.8.0',
                author: 'NGC660',
                description: '提醒事项插件',
                status: 'disabled',
                loaded: false,
                folder: 'ReminderBot',
                scheduled_tasks: [
                    {
                        name: '检查提醒事项',
                        interval: 60,
                        next_run: '1分钟后'
                    }
                ],
                event_handlers: ['on_command_remind']
            },
            {
                name: 'NewsBot',
                version: '1.1.0',
                author: 'NGC660',
                description: '新闻聚合插件',
                status: 'enabled',
                loaded: true,
                folder: 'NewsBot',
                scheduled_tasks: [
                    {
                        name: '早间新闻推送',
                        at_time: '07:30',
                        next_run: '明天 07:30'
                    },
                    {
                        name: '晚间新闻推送',
                        at_time: '19:30',
                        next_run: '今天 19:30'
                    }
                ],
                event_handlers: ['on_command_news']
            },
            {
                name: 'MusicBot',
                version: '0.7.5',
                author: 'NGC660',
                description: '音乐点播插件',
                status: 'disabled',
                loaded: false,
                folder: 'MusicBot',
                scheduled_tasks: [],
                event_handlers: ['on_command_music', 'on_command_playlist']
            },
            {
                name: 'GameBot',
                version: '1.3.0',
                author: 'NGC660',
                description: '游戏助手插件',
                status: 'enabled',
                loaded: true,
                folder: 'GameBot',
                scheduled_tasks: [],
                event_handlers: ['on_command_game']
            },
            {
                name: 'StockBot',
                version: '1.0.2',
                author: 'NGC660',
                description: '股票行情插件',
                status: 'enabled',
                loaded: true,
                folder: 'StockBot',
                scheduled_tasks: [
                    {
                        name: '盘前提醒',
                        at_time: '09:15',
                        next_run: '明天 09:15'
                    },
                    {
                        name: '盘后总结',
                        at_time: '15:30',
                        next_run: '今天 15:30'
                    }
                ],
                event_handlers: ['on_command_stock']
            },
            {
                name: 'ScreenshotBot',
                version: '0.5.0',
                author: 'NGC660',
                description: '网页截图插件',
                status: 'disabled',
                loaded: false,
                folder: 'ScreenshotBot',
                scheduled_tasks: [],
                event_handlers: ['on_command_screenshot']
            },
            {
                name: 'CookingBot',
                version: '1.1.1',
                author: 'NGC660',
                description: '食谱推荐插件',
                status: 'enabled',
                loaded: true,
                folder: 'CookingBot',
                scheduled_tasks: [],
                event_handlers: ['on_command_recipe']
            },
            {
                name: 'DebugPlugin',
                version: '0.0.1',
                author: 'NGC660',
                description: '测试用插件',
                status: 'enabled',
                loaded: false,
                folder: 'DebugPlugin',
                scheduled_tasks: [
                    {
                        name: '自检任务',
                        interval: 300,
                        next_run: '5分钟后'
                    }
                ],
                event_handlers: ['on_debug']
            }
        ];
        
        // 显示通知
        showToast('使用备用数据', '无法获取插件列表，使用本地备用数据', 'warning');
        
        // 使用备用数据
        pluginsData = mockPlugins;
        filteredPlugins = [...mockPlugins];
        currentPage = 1;
        renderPluginList(mockPlugins);
    }
    
    // 标准化插件状态，确保前后端一致
    function normalizePluginStatus(status, loaded) {
        if (status === undefined && loaded === undefined) return 'disabled';
        
        // 处理loaded状态
        let isLoaded = undefined;  // 默认为未定义，稍后根据情况确定
        if (loaded !== undefined) {
            if (typeof loaded === 'boolean') {
                isLoaded = loaded;
            } else if (typeof loaded === 'string') {
                isLoaded = ['true', '1', 'yes', 'loaded', '已加载'].includes(loaded.toLowerCase());
            } else if (typeof loaded === 'number') {
                isLoaded = loaded === 1;
            }
        }
        
        // 处理启用状态
        let isEnabled = false;
        if (status) {
            const statusStr = String(status).toLowerCase();
            // 表示启用的状态（包含中文支持）
            const enabledStatuses = [
                'enabled', 'enable', 'active', 'activated', 'on', 'true', '1', 'yes', 
                '已加载', '已启用', '加载', '启用', 'loaded'
            ];
            isEnabled = enabledStatuses.includes(statusStr);
            
            // 如果状态字符串本身表示已加载，且loaded未明确设置为false，则认为已加载
            if (['loaded', '已加载'].includes(statusStr) && isLoaded !== false) {
                isLoaded = true;
            }
        }
        
        // 如果loaded仍未定义，根据status推断
        if (isLoaded === undefined) {
            // 对于插件列表视图，默认认为已启用的插件也是已加载的
            isLoaded = isEnabled;
        }
        
        // 返回状态
        if (isEnabled && isLoaded) {
            return 'enabled';
        } else if (isEnabled && !isLoaded) {
            return 'enabled_not_loaded';
        } else {
            return 'disabled';
        }
    }
    
    // 渲染插件列表
    function renderPluginList(plugins) {
        const pluginList = document.getElementById('plugin-list');
        
        if (!plugins || plugins.length === 0) {
            pluginList.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4">
                        <i class="fas fa-puzzle-piece fa-2x mb-2"></i>
                        <p>没有找到插件</p>
                    </td>
                </tr>
            `;
            
            // 更新分页信息
            updatePaginationInfo(0, 0, 0);
            return;
        }
        
        // 计算分页信息
        totalPages = Math.ceil(plugins.length / pageSize);
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize, plugins.length);
        
        // 获取当前页的插件
        const currentPagePlugins = plugins.slice(startIndex, endIndex);
        
        // 更新分页信息
        updatePaginationInfo(startIndex + 1, endIndex, plugins.length);
        
        // 清空列表
        pluginList.innerHTML = '';
        
        // 按状态排序插件：已启用并加载 > 已启用未加载 > 已禁用
        currentPagePlugins.sort((a, b) => {
            const statusA = normalizePluginStatus(a.status, a.loaded);
            const statusB = normalizePluginStatus(b.status, b.loaded);
            
            if (statusA === 'enabled' && statusB !== 'enabled') return -1;
            if (statusA !== 'enabled' && statusB === 'enabled') return 1;
            if (statusA === 'enabled_not_loaded' && statusB === 'disabled') return -1;
            if (statusA === 'disabled' && statusB === 'enabled_not_loaded') return 1;
            return 0;
        });
        
        // 添加插件到列表
        currentPagePlugins.forEach((plugin, index) => {
            // 标准化状态
            const normalizedStatus = normalizePluginStatus(plugin.status, plugin.loaded);
            
            const row = document.createElement('tr');
            
            // 根据状态设置不同的类名
            if (normalizedStatus === 'enabled') {
                row.className = 'plugin-enabled';
            } else if (normalizedStatus === 'enabled_not_loaded') {
                row.className = 'plugin-enabled-not-loaded';
            } else {
                row.className = 'plugin-disabled';
            }
            
            // 添加动画延迟效果
            row.style.animation = `fadeIn 0.3s ease-out ${index * 0.05}s both`;
            
            // 设置状态样式
            let statusClass, statusText;
            if (normalizedStatus === 'enabled') {
                statusClass = 'success';
                statusText = '已启用';
            } else if (normalizedStatus === 'enabled_not_loaded') {
                statusClass = 'warning';
                statusText = '已启用但未加载';
            } else {
                statusClass = 'secondary';
                statusText = '已禁用';
            }
            
            // 操作按钮
            const actionBtn = normalizedStatus === 'enabled' || normalizedStatus === 'enabled_not_loaded'
                ? `<button class="btn btn-sm btn-danger me-1" data-action="disable" data-plugin="${plugin.name}"><i class="fas fa-power-off"></i> 禁用</button>`
                : `<button class="btn btn-sm btn-success me-1" data-action="enable" data-plugin="${plugin.name}"><i class="fas fa-power-off"></i> 启用</button>`;
            
            row.innerHTML = `
                <td>
                    <a href="#" class="plugin-name" data-plugin="${plugin.name}">
                        ${plugin.name}
                    </a>
                </td>
                <td>${plugin.version || 'N/A'}</td>
                <td>${plugin.author || '未知'}</td>
                <td>${plugin.description || '无描述'}</td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>
                    ${actionBtn}
                    <button class="btn btn-sm btn-primary" data-action="reload" data-plugin="${plugin.name}">
                        <i class="fas fa-sync-alt"></i> 重载
                    </button>
                </td>
            `;
            
            // 添加到列表
            pluginList.appendChild(row);
            
            // 添加事件处理器
            const nameLink = row.querySelector('.plugin-name');
            nameLink.addEventListener('click', (e) => {
                e.preventDefault();
                showPluginDetail(plugin.name);
            });
            
            // 添加操作按钮事件处理器
            const buttons = row.querySelectorAll('button[data-action]');
            buttons.forEach(btn => {
                btn.addEventListener('click', handlePluginButtonClick);
            });
        });
    }
    
    // 更新分页信息
    function updatePaginationInfo(start, end, total) {
        document.getElementById('pagination-start').textContent = start;
        document.getElementById('pagination-end').textContent = end;
        document.getElementById('pagination-total').textContent = total;
        document.getElementById('pagination-current').textContent = currentPage;
        
        // 更新上一页按钮状态
        const prevBtn = document.getElementById('pagination-prev');
        if (currentPage <= 1) {
            prevBtn.classList.add('disabled');
        } else {
            prevBtn.classList.remove('disabled');
        }
        
        // 更新下一页按钮状态
        const nextBtn = document.getElementById('pagination-next');
        if (currentPage >= totalPages) {
            nextBtn.classList.add('disabled');
        } else {
            nextBtn.classList.remove('disabled');
        }
    }
    
    // 处理插件按钮点击事件
    function handlePluginButtonClick(e) {
        const action = e.currentTarget.getAttribute('data-action');
        const pluginName = e.currentTarget.getAttribute('data-plugin');
        const icon = e.currentTarget.querySelector('i');
        
        // 添加按钮效果
        icon.classList.add('fa-spin');
        e.currentTarget.disabled = true;
        
        switch (action) {
            case 'enable':
                enablePlugin(pluginName, e.currentTarget);
                break;
            case 'disable':
                disablePlugin(pluginName, e.currentTarget);
                break;
            case 'reload':
                reloadPlugin(pluginName, e.currentTarget);
                break;
        }
    }
    
    // 启用插件
    function enablePlugin(pluginName, button) {
        // 禁用所有按钮
        setButtonsEnabled(false);
        
        fetch(`/plugins/enable/${pluginName}`, {
            method: 'POST'
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('成功', `插件 ${pluginName} 已启用`, 'success');
                    
                    // 在本地更新插件状态，避免刷新整个列表
                    const plugin = pluginsData.find(p => p.name === pluginName);
                    if (plugin) {
                        // 更新状态为启用
                        plugin.status = 'enabled';
                        
                        // 尝试获取完整的插件数据
                        fetch(`/plugins/api/detail/${pluginName}`)
                            .then(response => response.json())
                            .then(detailData => {
                                if (detailData && detailData.success && detailData.plugin) {
                                    // 使用详细数据更新插件
                                    const index = pluginsData.findIndex(p => p.name === pluginName);
                                    if (index !== -1) {
                                        pluginsData[index] = detailData.plugin;
                                    }
                                }
                                // 更新视图
                                filteredPlugins = [...pluginsData];
                                renderPluginList(filteredPlugins);
                            })
                            .catch(() => {
                                // 如果无法获取详细数据，只更新视图
                                filteredPlugins = [...pluginsData];
                                renderPluginList(filteredPlugins);
                            });
                    } else {
                        // 如果找不到插件，重新加载所有插件
                        loadPlugins();
                    }
                } else {
                    showToast('错误', data.message, 'danger');
                    // 停止按钮动画
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) icon.classList.remove('fa-spin');
                        button.disabled = false;
                    }
                }
            })
            .catch(error => {
                console.error('启用插件失败:', error);
                showToast('错误', '启用插件失败: ' + error.message, 'danger');
                // 停止按钮动画
                if (button) {
                    const icon = button.querySelector('i');
                    if (icon) icon.classList.remove('fa-spin');
                    button.disabled = false;
                }
            })
            .finally(() => {
                // 重新启用所有按钮
                setButtonsEnabled(true);
            });
    }
    
    // 禁用插件
    function disablePlugin(pluginName, button) {
        // 禁用所有按钮
        setButtonsEnabled(false);
        
        fetch(`/plugins/disable/${pluginName}`, {
            method: 'POST'
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('成功', `插件 ${pluginName} 已禁用`, 'success');
                    
                    // 在本地更新插件状态，避免刷新整个列表
                    const plugin = pluginsData.find(p => p.name === pluginName);
                    if (plugin) {
                        // 更新状态为禁用
                        plugin.status = 'disabled';
                        plugin.loaded = false;
                        
                        // 更新视图
                        filteredPlugins = [...pluginsData];
                        renderPluginList(filteredPlugins);
                    } else {
                        // 如果找不到插件，重新加载所有插件
                        loadPlugins();
                    }
                } else {
                    showToast('错误', data.message, 'danger');
                    // 停止按钮动画
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) icon.classList.remove('fa-spin');
                        button.disabled = false;
                    }
                }
            })
            .catch(error => {
                console.error('禁用插件失败:', error);
                showToast('错误', '禁用插件失败: ' + error.message, 'danger');
                // 停止按钮动画
                if (button) {
                    const icon = button.querySelector('i');
                    if (icon) icon.classList.remove('fa-spin');
                    button.disabled = false;
                }
            })
            .finally(() => {
                // 重新启用所有按钮
                setButtonsEnabled(true);
            });
    }
    
    // 重载插件
    function reloadPlugin(pluginName, button) {
        // 禁用所有按钮
        setButtonsEnabled(false);
        
        fetch(`/plugins/reload/${pluginName}`, {
            method: 'POST'
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('成功', `插件 ${pluginName} 已重新加载`, 'success');
                    
                    // 在本地更新插件状态，避免刷新整个列表
                    const plugin = pluginsData.find(p => p.name === pluginName);
                    if (plugin) {
                        // 更新状态为启用并已加载
                        plugin.status = 'enabled';
                        plugin.loaded = true;
                        
                        // 尝试获取完整的插件数据
                        fetch(`/plugins/api/detail/${pluginName}`)
                            .then(response => response.json())
                            .then(detailData => {
                                if (detailData && detailData.success && detailData.plugin) {
                                    // 使用详细数据更新插件
                                    const index = pluginsData.findIndex(p => p.name === pluginName);
                                    if (index !== -1) {
                                        pluginsData[index] = detailData.plugin;
                                    }
                                }
                                // 更新视图
                                filteredPlugins = [...pluginsData];
                                renderPluginList(filteredPlugins);
                            })
                            .catch(() => {
                                // 如果无法获取详细数据，只更新视图
                                filteredPlugins = [...pluginsData];
                                renderPluginList(filteredPlugins);
                            });
                    } else {
                        // 如果找不到插件，重新加载所有插件
                        loadPlugins();
                    }
                } else {
                    showToast('错误', data.message, 'danger');
                    // 停止按钮动画
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) icon.classList.remove('fa-spin');
                        button.disabled = false;
                    }
                }
            })
            .catch(error => {
                console.error('重载插件失败:', error);
                showToast('错误', '重载插件失败: ' + error.message, 'danger');
                // 停止按钮动画
                if (button) {
                    const icon = button.querySelector('i');
                    if (icon) icon.classList.remove('fa-spin');
                    button.disabled = false;
                }
            })
            .finally(() => {
                // 重新启用所有按钮
                setButtonsEnabled(true);
            });
    }
    
    // 设置所有按钮的启用状态
    function setButtonsEnabled(enabled) {
        const buttons = document.querySelectorAll('.table-plugins button');
        buttons.forEach(btn => {
            btn.disabled = !enabled;
        });
    }
    
    // 显示插件详情
    function showPluginDetail(pluginName) {
        // 查找插件
        selectedPlugin = pluginsData.find(p => p.name === pluginName);
        
        if (!selectedPlugin) {
            showToast('错误', `找不到插件 ${pluginName}`, 'error');
            return;
        }
        
        // 先用当前数据填充模态框
        updatePluginDetailModal(selectedPlugin);
        
        // 显示模态框
        pluginDetailModal.show();
        
        // 加载最新的插件详情数据
        fetch(`/plugins/api/detail/${pluginName}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`API响应错误: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.plugin) {
                    // 使用服务器返回的最新数据更新模态框
                    selectedPlugin = data.plugin;
                    
                    // 确保插件数据格式符合预期
                    if (!selectedPlugin.scheduled_tasks) selectedPlugin.scheduled_tasks = [];
                    if (!selectedPlugin.event_handlers) selectedPlugin.event_handlers = [];
                    
                    updatePluginDetailModal(selectedPlugin);
                    
                    // 更新插件列表中的数据
                    const index = pluginsData.findIndex(p => p.name === selectedPlugin.name);
                    if (index !== -1) {
                        // 保留原有数据中的一些字段，以免被覆盖
                        const originalData = pluginsData[index];
                        
                        // 创建状态映射
                        let newStatus = 'disabled';
                        if (selectedPlugin.enabled !== undefined) {
                            newStatus = selectedPlugin.enabled ? 'enabled' : 'disabled';
                        } else if (selectedPlugin.status) {
                            newStatus = selectedPlugin.status;
                        }
                        
                        // 更新插件列表中的数据，保留原有字段
                        pluginsData[index] = {
                            ...originalData,
                            ...selectedPlugin,
                            // 确保status字段存在并符合预期格式
                            status: newStatus
                        };
                    }
                } else {
                    // 显示API错误
                    const errorMsg = data.error || '未知错误';
                    showToast('警告', `无法获取最新的插件详情: ${errorMsg}`, 'warning');
                }
            })
            .catch(error => {
                // 使用当前数据显示
                showToast('警告', `无法获取最新的插件详情: ${error.message}`, 'warning');
            });
    }
    
    // 更新插件详情模态框内容
    function updatePluginDetailModal(plugin) {
        // 确保plugin对象存在且有效
        if (!plugin || typeof plugin !== 'object') {
            return;
        }
        
        // 设置模态框内容
        document.getElementById('detail-plugin-name').textContent = plugin.name || '未知插件';
        document.getElementById('detail-plugin-version').textContent = plugin.version || 'v1.0.0';
        document.getElementById('detail-plugin-author').innerHTML = '<i class="fas fa-user me-1"></i> 作者: ' + (plugin.author || '未知');
        document.getElementById('detail-plugin-description').textContent = plugin.description || '无描述';
        
        // 设置插件状态 - 处理多种可能的状态表示
        // 首先检查plugin.status和plugin.loaded属性
        let pluginStatus = plugin.status;
        let isLoaded = plugin.loaded;
        
        // 然后检查plugin.enabled属性
        let isEnabled = undefined;
        if (plugin.enabled !== undefined) {
            isEnabled = plugin.enabled;
            // 如果只有enabled属性，且没有明确的loaded状态，则认为enabled=true时也是loaded的
            if (isLoaded === undefined) {
                isLoaded = isEnabled;
            }
        }
        
        // 如果有明确的enabled值，则使用它替代status
        if (isEnabled !== undefined) {
            pluginStatus = isEnabled ? 'enabled' : 'disabled';
        }
        
        // 设置状态显示
        const normalizedStatus = normalizePluginStatus(pluginStatus, isLoaded);
        
        const statusEl = document.getElementById('detail-plugin-status');
        
        if (normalizedStatus === 'enabled') {
            statusEl.innerHTML = '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i> 已启用并加载</span>';
        } else if (normalizedStatus === 'enabled_not_loaded') {
            statusEl.innerHTML = '<span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i> 已启用但未加载</span>';
        } else {
            statusEl.innerHTML = '<span class="badge bg-secondary"><i class="fas fa-times-circle me-1"></i> 已禁用</span>';
        }
        
        // 设置操作按钮
        const actionBtn = document.getElementById('detail-action-btn');
        
        if (normalizedStatus === 'enabled' || normalizedStatus === 'enabled_not_loaded') {
            actionBtn.className = 'btn btn-danger';
            actionBtn.innerHTML = '<i class="fas fa-power-off me-1"></i> 禁用';
            actionBtn.setAttribute('data-action', 'disable');
        } else {
            actionBtn.className = 'btn btn-success';
            actionBtn.innerHTML = '<i class="fas fa-power-off me-1"></i> 启用';
            actionBtn.setAttribute('data-action', 'enable');
        }
        
        // 渲染定时任务 - 确保处理各种可能的数据格式
        let tasks = plugin.scheduled_tasks;
        if (!tasks && plugin.tasks) {
            tasks = plugin.tasks;
        }
        
        renderScheduledTasks(tasks);
        
        // 渲染事件处理器 - 确保处理各种可能的数据格式
        let handlers = plugin.event_handlers;
        if (!handlers && plugin.handlers) {
            handlers = plugin.handlers;
        }
        
        renderEventHandlers(handlers);
    }
    
    // 渲染定时任务列表
    function renderScheduledTasks(tasks) {
        const tasksList = document.getElementById('detail-scheduled-tasks');
        const currentPluginName = selectedPlugin ? selectedPlugin.name : '';
        
        // 确保tasks是数组
        if (!tasks) tasks = [];
        if (!Array.isArray(tasks)) {
            console.warn('定时任务数据不是数组格式，尝试转换:', tasks);
            if (typeof tasks === 'object') {
                // 尝试转换对象为数组
                tasks = Object.entries(tasks).map(([key, value]) => {
                    if (typeof value === 'object') {
                        return { ...value, name: key, plugin_name: currentPluginName };
                    } else {
                        return { name: key, info: value, plugin_name: currentPluginName };
                    }
                });
            } else if (typeof tasks === 'string') {
                // 如果是单个字符串，转换为数组
                tasks = [{ name: tasks, plugin_name: currentPluginName }];
            } else {
                tasks = [];
            }
        }
        
        // 进一步过滤任务，确保只显示当前插件的任务
        const filteredTasks = tasks.filter(task => {
            // 如果任务有plugin_name字段，检查是否匹配
            if (task && task.plugin_name && currentPluginName) {
                return task.plugin_name === currentPluginName;
            }
            return true; // 无plugin_name字段或无法判断时保留
        });
        
        if (filteredTasks.length === 0) {
            tasksList.innerHTML = `
                <div class="list-group-item border">
                    <div class="text-center py-3">
                        <i class="fas fa-calendar-times mb-2"></i>
                        <p class="mb-0">没有定时任务</p>
                        <small>该插件未注册任何定时任务</small>
                    </div>
                </div>
            `;
            return;
        }
        
        // 清空列表
        tasksList.innerHTML = '';
        
        // 添加任务
        filteredTasks.forEach(task => {
            // 跳过无效任务
            if (!task) return;
            
            const item = document.createElement('div');
            item.className = 'list-group-item border';
            
            // 获取任务名称 (支持多种格式)
            let taskName = '未命名任务';
            if (typeof task === 'string') {
                taskName = task;
            } else if (task.name) {
                taskName = task.name;
            } else if (task.task_name) {
                taskName = task.task_name;
            } else if (task.func) {
                taskName = task.func;
            }
            
            // 决定显示的计划信息
            let scheduleInfo = '';
            
            if (task.interval) {
                scheduleInfo = `每 ${task.interval} 秒运行`;
            } else if (task.at_time) {
                scheduleInfo = `每天 ${task.at_time} 运行`;
            } else if (task.day_of_week) {
                scheduleInfo = `每周 ${task.day_of_week} 运行`;
            } else if (task.schedule) {
                scheduleInfo = `计划: ${task.schedule}`;
            } else if (task.cron) {
                scheduleInfo = `Cron: ${task.cron}`;
            } else {
                scheduleInfo = '定时任务';
            }
            
            // 显示上次/下次运行时间信息
            let timeInfo = '';
            if (task.next_run) {
                timeInfo = `下次: ${task.next_run}`;
            } else if (task.last_run) {
                timeInfo = `上次: ${task.last_run}`;
            }
            
            item.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${taskName}</h6>
                        <small class="text-secondary">${scheduleInfo}</small>
                    </div>
                    <div>
                        <span class="badge bg-info">
                            <i class="fas fa-clock me-1"></i> 
                            ${timeInfo || '未运行'}
                        </span>
                    </div>
                </div>
            `;
            
            tasksList.appendChild(item);
        });
    }
    
    // 渲染事件处理器列表
    function renderEventHandlers(handlers) {
        const handlersList = document.getElementById('detail-event-handlers');
        const currentPluginName = selectedPlugin ? selectedPlugin.name : '';
        
        // 确保handlers是数组
        if (!handlers) handlers = [];
        if (!Array.isArray(handlers)) {
            console.warn('事件处理器数据不是数组格式，尝试转换:', handlers);
            handlers = [];
        }
        
        // 如果没有找到处理器
        if (handlers.length === 0) {
            handlersList.innerHTML = `
                <div class="list-group-item border">
                    <div class="text-center py-3">
                        <i class="fas fa-bolt-slash mb-2"></i>
                        <p class="mb-0">没有事件处理器</p>
                        <small>该插件未注册任何事件处理器</small>
                    </div>
                </div>
            `;
            return;
        }
        
        // 清空列表
        handlersList.innerHTML = '';
        
        // 过滤掉非当前插件的处理器
        const filteredHandlers = handlers.filter(handler => {
            // 如果有plugin_name字段，确保与当前插件匹配
            if (handler.plugin_name && currentPluginName) {
                return handler.plugin_name === currentPluginName;
            }
            return true; // 如果没有plugin_name字段，默认保留
        });
        
        if (filteredHandlers.length === 0) {
            handlersList.innerHTML = `
                <div class="list-group-item border">
                    <div class="text-center py-3">
                        <i class="fas fa-bolt-slash mb-2"></i>
                        <p class="mb-0">没有找到此插件的事件处理器</p>
                        <small>该插件未注册任何事件处理器，或数据格式异常</small>
                    </div>
                </div>
            `;
            return;
        }
        
        // 遍历处理器列表
        filteredHandlers.forEach(handler => {
            const item = document.createElement('div');
            item.className = 'list-group-item border';
            
            // 处理不同结构的事件处理器数据
            // 新的格式: {event_type: x, event_name: y, handler_name: z, priority: p}
            const eventName = handler.event_name || String(handler.event_type || '未知事件');
            const handlerName = handler.handler_name || handler.name || '未命名处理器';
            const priority = handler.priority || 0;
            
            item.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${handlerName}</h6>
                        <small class="text-secondary">事件类型: ${eventName}</small>
                    </div>
                    <div>
                        <span class="badge bg-primary">
                            优先级: ${priority}
                        </span>
                    </div>
                </div>
            `;
            
            handlersList.appendChild(item);
        });
    }
    
    // 处理详情模态框中的操作按钮
    function handlePluginAction() {
        if (!selectedPlugin) return;
        
        const actionBtn = document.getElementById('detail-action-btn');
        const action = actionBtn.getAttribute('data-action');
        
        // 添加按钮动画效果
        const icon = actionBtn.querySelector('i');
        if (icon) {
            icon.classList.add('fa-spin');
        }
        actionBtn.disabled = true;
        
        // 执行对应的操作
        if (action === 'enable') {
            // 启用插件
            fetch(`/plugins/enable/${selectedPlugin.name}`, {
                method: 'POST'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('成功', `插件 ${selectedPlugin.name} 已启用`, 'success');
                        
                        // 更新本地状态
                        selectedPlugin.status = 'enabled';
                        
                        // 更新插件列表
                        const plugin = pluginsData.find(p => p.name === selectedPlugin.name);
                        if (plugin) {
                            plugin.status = 'enabled';
                            filteredPlugins = [...pluginsData];
                            renderPluginList(filteredPlugins);
                        }
                        
                        // 重新获取插件详情
                        fetch(`/plugins/api/detail/${selectedPlugin.name}`)
                            .then(response => response.json())
                            .then(detailData => {
                                if (detailData.success && detailData.plugin) {
                                    selectedPlugin = detailData.plugin;
                                    updatePluginDetailModal(selectedPlugin);
                                    
                                    // 更新插件列表中的数据
                                    const index = pluginsData.findIndex(p => p.name === selectedPlugin.name);
                                    if (index !== -1) {
                                        pluginsData[index] = detailData.plugin;
                                        filteredPlugins = [...pluginsData];
                                        renderPluginList(filteredPlugins);
                                    }
                                }
                            })
                            .catch(error => {
                                console.error('获取插件详情失败:', error);
                            });
                    } else {
                        showToast('错误', data.message, 'danger');
                    }
                    
                    // 停止按钮动画
                    if (icon) icon.classList.remove('fa-spin');
                    actionBtn.disabled = false;
                })
                .catch(error => {
                    console.error('启用插件失败:', error);
                    showToast('错误', '启用插件失败: ' + error.message, 'danger');
                    
                    // 停止按钮动画
                    if (icon) icon.classList.remove('fa-spin');
                    actionBtn.disabled = false;
                });
        } else if (action === 'disable') {
            // 禁用插件
            fetch(`/plugins/disable/${selectedPlugin.name}`, {
                method: 'POST'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('成功', `插件 ${selectedPlugin.name} 已禁用`, 'success');
                        
                        // 更新本地状态
                        selectedPlugin.status = 'disabled';
                        selectedPlugin.loaded = false;
                        
                        // 更新插件列表
                        const plugin = pluginsData.find(p => p.name === selectedPlugin.name);
                        if (plugin) {
                            plugin.status = 'disabled';
                            plugin.loaded = false;
                            filteredPlugins = [...pluginsData];
                            renderPluginList(filteredPlugins);
                        }
                        
                        // 更新模态框
                        updatePluginDetailModal(selectedPlugin);
                    } else {
                        showToast('错误', data.message, 'danger');
                    }
                    
                    // 停止按钮动画
                    if (icon) icon.classList.remove('fa-spin');
                    actionBtn.disabled = false;
                })
                .catch(error => {
                    console.error('禁用插件失败:', error);
                    showToast('错误', '禁用插件失败: ' + error.message, 'danger');
                    
                    // 停止按钮动画
                    if (icon) icon.classList.remove('fa-spin');
                    actionBtn.disabled = false;
                });
        }
    }
    
    // 显示提示消息
    function showToast(title, message, type) {
        // 检查是否已存在toast容器
        let toastContainer = document.querySelector('.toast-container');
        
        if (!toastContainer) {
            // 创建toast容器
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        // 创建toast元素
        const toastEl = document.createElement('div');
        toastEl.className = `toast align-items-center text-white bg-${type} border-0`;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        // 设置toast内容
        toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}</strong>: ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="关闭"></button>
            </div>
        `;
        
        // 添加到容器
        toastContainer.appendChild(toastEl);
        
        // 初始化toast
        const toast = new bootstrap.Toast(toastEl, {
            autohide: true,
            delay: 5000
        });
        
        // 显示toast
        toast.show();
        
        // 设置自动移除
        toastEl.addEventListener('hidden.bs.toast', () => {
            toastEl.remove();
        });
    }

    // 用于测试的API模拟函数
    function mockApiResponse(url, method, responseData) {
        // 将原始的fetch保存为oldFetch
        const oldFetch = window.fetch;
        
        // 覆盖全局的fetch
        window.fetch = function(input, init) {
            if (typeof input === 'string' && input.includes(url) && (!init || init.method === method)) {
                console.log(`拦截请求 ${method} ${input}`);
                return Promise.resolve({
                    ok: true,
                    status: 200,
                    json: () => Promise.resolve(responseData)
                });
            }
            // 其他请求使用原来的fetch
            return oldFetch.apply(this, arguments);
        };
        
        // 5秒后恢复原始fetch (避免长时间覆盖)
        setTimeout(() => {
            window.fetch = oldFetch;
            console.log('已恢复原始fetch函数');
        }, 5000);
    }
    
    // 测试模拟加载已启用/已禁用的插件
    function mockTogglePlugin(pluginName, isEnabled) {
        const plugin = pluginsData.find(p => p.name === pluginName);
        if (!plugin) {
            console.error(`找不到插件: ${pluginName}`);
            return;
        }
        
        // 准备模拟API响应数据
        const mockResponse = {
            success: true,
            message: isEnabled ? `插件 ${pluginName} 已启用` : `插件 ${pluginName} 已禁用`
        };
        
        // 模拟API调用
        const apiUrl = isEnabled ? `/plugins/enable/${pluginName}` : `/plugins/disable/${pluginName}`;
        mockApiResponse(apiUrl, 'POST', mockResponse);
        
        // 调用实际函数
        if (isEnabled) {
            enablePlugin(pluginName);
        } else {
            disablePlugin(pluginName);
        }
    }
</script>
{% endblock %} 
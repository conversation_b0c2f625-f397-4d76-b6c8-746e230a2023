{% extends "base.html" %}

{% block title %}日志查看 - NGC660 Bot Star{% endblock %}

{% block header_title %}系统日志{% endblock %}

{% block content %}
<div class="logsContainer">
    <!-- 日志筛选工具栏 -->
    <div class="row mb-3">
        <div class="col-md-8">
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text bg-dark border-secondary text-light">
                        <i class="fas fa-filter"></i>
                    </span>
                </div>
                <select id="logLevel" class="form-select bg-dark text-light border-secondary">
                    <option value="all">所有级别</option>
                    <option value="debug">Debug</option>
                    <option value="info">Info</option>
                    <option value="warning">Warning</option>
                    <option value="error">Error</option>
                    <option value="critical">Critical</option>
                </select>
                <input type="text" id="logSearch" class="form-control bg-dark text-light border-secondary" placeholder="搜索日志内容...">
                <button id="searchBtn" class="btn btnCyber">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <button id="refreshLogs" class="btn btnCyber">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button id="clearLogs" class="btn btn-danger">
                <i class="fas fa-trash-alt"></i> 清空
            </button>
        </div>
    </div>
    
    <!-- 日志显示区域 -->
    <div class="card chartCard">
        <div class="cardHeader d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-list me-2"></i> 系统日志</h5>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="autoRefresh">
                <label class="form-check-label" for="autoRefresh">自动刷新</label>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="logContent" id="logContent">
                <pre class="logText bg-dark text-light p-3" id="logTextArea" style="max-height: 600px; overflow: auto;">正在加载日志...</pre>
            </div>
        </div>
        <div class="card-footer d-flex justify-content-between">
            <div>
                <span class="badge bg-primary me-2">信息总数: <span id="logCount">0</span></span>
                <span class="badge bg-success me-2">Info: <span id="infoCount">0</span></span>
                <span class="badge bg-warning me-2">Warning: <span id="warningCount">0</span></span>
                <span class="badge bg-danger">Error: <span id="errorCount">0</span></span>
            </div>
            <div>
                <button id="downloadLogs" class="btn btn-sm btn-primary">
                    <i class="fas fa-download"></i> 下载日志
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 模拟日志数据
        const mockLogs = [
            { level: 'INFO', timestamp: '2025-05-08 21:15:23', message: '系统启动成功', module: 'main' },
            { level: 'INFO', timestamp: '2025-05-08 21:15:24', message: 'Web管理后台已启动在 http://127.0.0.1:8081', module: 'WebAdmin' },
            { level: 'INFO', timestamp: '2025-05-08 21:15:25', message: '加载插件: HelloWorld', module: 'PluginManager' },
            { level: 'INFO', timestamp: '2025-05-08 21:15:25', message: '加载插件: WeatherQuery', module: 'PluginManager' },
            { level: 'WARNING', timestamp: '2025-05-08 21:15:26', message: '插件 ImageSearch 加载失败: 缺少依赖项', module: 'PluginManager' },
            { level: 'INFO', timestamp: '2025-05-08 21:15:27', message: '机器人登录成功: NGC660-Star', module: 'BotManager' },
            { level: 'INFO', timestamp: '2025-05-08 21:17:45', message: '收到消息: 你好', module: 'MessageHandler' },
            { level: 'INFO', timestamp: '2025-05-08 21:17:46', message: '触发插件: HelloWorld', module: 'PluginManager' },
            { level: 'INFO', timestamp: '2025-05-08 21:17:46', message: '发送回复: 你好！我是NGC660 Bot Star，有什么我可以帮你的吗？', module: 'MessageHandler' },
            { level: 'ERROR', timestamp: '2025-05-08 21:22:13', message: '无法连接到数据库: Connection refused', module: 'DbServer' },
            { level: 'INFO', timestamp: '2025-05-08 21:22:15', message: '尝试重连数据库', module: 'DbServer' },
            { level: 'INFO', timestamp: '2025-05-08 21:22:18', message: '数据库连接恢复', module: 'DbServer' },
            { level: 'WARNING', timestamp: '2025-05-08 21:24:35', message: '收到不支持的消息类型: 视频消息', module: 'MessageHandler' },
            { level: 'INFO', timestamp: '2025-05-08 21:25:42', message: '执行定时任务: 天气播报', module: 'ScheduleManager' },
            { level: 'ERROR', timestamp: '2025-05-08 21:25:43', message: '天气API请求失败: 超时', module: 'WeatherQuery' }
        ];
        
        // 初始化日志计数
        let infoCount = 0;
        let warningCount = 0;
        let errorCount = 0;
        
        // 计算各级别日志数量
        mockLogs.forEach(log => {
            if (log.level === 'INFO') infoCount++;
            if (log.level === 'WARNING') warningCount++;
            if (log.level === 'ERROR') errorCount++;
        });
        
        // 更新计数显示
        document.getElementById('logCount').textContent = mockLogs.length;
        document.getElementById('infoCount').textContent = infoCount;
        document.getElementById('warningCount').textContent = warningCount;
        document.getElementById('errorCount').textContent = errorCount;
        
        // 格式化日志显示
        function formatLogs(logs) {
            let formattedText = '';
            logs.forEach(log => {
                let logClass = '';
                switch(log.level) {
                    case 'INFO':
                        logClass = 'text-info';
                        break;
                    case 'WARNING':
                        logClass = 'text-warning';
                        break;
                    case 'ERROR':
                    case 'CRITICAL':
                        logClass = 'text-danger';
                        break;
                    case 'DEBUG':
                        logClass = 'text-secondary';
                        break;
                    default:
                        logClass = 'text-light';
                }
                
                formattedText += `<span class="${logClass}">[${log.timestamp}] [${log.level}] [${log.module}] ${log.message}</span>\n`;
            });
            
            return formattedText;
        }
        
        // 显示日志
        document.getElementById('logTextArea').innerHTML = formatLogs(mockLogs);
        
        // 过滤日志
        function filterLogs() {
            const level = document.getElementById('logLevel').value;
            const searchText = document.getElementById('logSearch').value.toLowerCase();
            
            let filteredLogs = mockLogs;
            
            // 按级别过滤
            if (level !== 'all') {
                filteredLogs = filteredLogs.filter(log => log.level.toLowerCase() === level.toLowerCase());
            }
            
            // 按搜索文本过滤
            if (searchText) {
                filteredLogs = filteredLogs.filter(log => 
                    log.message.toLowerCase().includes(searchText) || 
                    log.module.toLowerCase().includes(searchText)
                );
            }
            
            // 更新显示
            document.getElementById('logTextArea').innerHTML = formatLogs(filteredLogs);
        }
        
        // 绑定搜索按钮
        document.getElementById('searchBtn').addEventListener('click', filterLogs);
        
        // 绑定日志级别选择
        document.getElementById('logLevel').addEventListener('change', filterLogs);
        
        // 绑定刷新按钮
        document.getElementById('refreshLogs').addEventListener('click', function() {
            // 在实际应用中，这里应该从服务器获取最新日志
            document.getElementById('logTextArea').innerHTML = formatLogs(mockLogs);
            // 显示通知
            showToast('刷新成功', '日志已刷新至最新状态', 'success');
        });
        
        // 绑定清空按钮
        document.getElementById('clearLogs').addEventListener('click', function() {
            // 显示确认对话框
            if (confirm('确定要清空日志吗？此操作不可恢复。')) {
                // 在实际应用中，这里应该调用API清空日志
                document.getElementById('logTextArea').innerHTML = '<span class="text-muted">日志已清空</span>';
                document.getElementById('logCount').textContent = '0';
                document.getElementById('infoCount').textContent = '0';
                document.getElementById('warningCount').textContent = '0';
                document.getElementById('errorCount').textContent = '0';
                // 显示通知
                showToast('操作成功', '日志已清空', 'success');
            }
        });
        
        // 绑定下载按钮
        document.getElementById('downloadLogs').addEventListener('click', function() {
            // 在实际应用中，这里应该从服务器下载日志文件
            // 模拟下载操作
            const logText = mockLogs.map(log => `[${log.timestamp}] [${log.level}] [${log.module}] ${log.message}`).join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ngcbot_logs_${new Date().toISOString().slice(0, 10)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            // 显示通知
            showToast('下载开始', '日志文件正在下载', 'info');
        });
        
        // 绑定自动刷新
        const autoRefreshToggle = document.getElementById('autoRefresh');
        let autoRefreshInterval;
        
        autoRefreshToggle.addEventListener('change', function() {
            if (this.checked) {
                // 启动自动刷新（每10秒）
                autoRefreshInterval = setInterval(function() {
                    // 在实际应用中，这里应该从服务器获取最新日志
                    document.getElementById('refreshLogs').click();
                }, 10000);
                showToast('自动刷新', '已启用自动刷新（10秒）', 'info');
            } else {
                // 停止自动刷新
                clearInterval(autoRefreshInterval);
                showToast('自动刷新', '已禁用自动刷新', 'info');
            }
        });
    });
</script>
{% endblock %} 
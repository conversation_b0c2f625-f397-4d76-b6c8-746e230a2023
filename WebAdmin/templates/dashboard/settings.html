{% extends "base.html" %}

{% block title %}系统设置 - NGC660 Bot Star{% endblock %}

{% block header_title %}系统设置{% endblock %}

{% block content %}
<div class="settingsContainer">
    <!-- 设置内容 -->
    <div class="row">
        <div class="col-md-3 mb-4">
            <!-- 设置导航 -->
            <div class="card chartCard">
                <div class="cardHeader">
                    <h5><i class="fas fa-list me-2"></i> 设置分类</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush bg-dark">
                        <a href="#generalSettings" class="list-group-item list-group-item-action bg-dark text-light border-light border-opacity-10 active" data-bs-toggle="list">
                            <i class="fas fa-cog me-2"></i> 常规设置
                        </a>
                        <a href="#botSettings" class="list-group-item list-group-item-action bg-dark text-light border-light border-opacity-10" data-bs-toggle="list">
                            <i class="fas fa-robot me-2"></i> 机器人设置
                        </a>
                        <a href="#pluginSettings" class="list-group-item list-group-item-action bg-dark text-light border-light border-opacity-10" data-bs-toggle="list">
                            <i class="fas fa-puzzle-piece me-2"></i> 插件设置
                        </a>
                        <a href="#securitySettings" class="list-group-item list-group-item-action bg-dark text-light border-light border-opacity-10" data-bs-toggle="list">
                            <i class="fas fa-shield-alt me-2"></i> 安全设置
                        </a>
                        <a href="#backupSettings" class="list-group-item list-group-item-action bg-dark text-light border-light border-opacity-10" data-bs-toggle="list">
                            <i class="fas fa-save me-2"></i> 备份与恢复
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <!-- 设置内容 -->
            <div class="tab-content">
                <!-- 常规设置 -->
                <div class="tab-pane fade show active" id="generalSettings">
                    <div class="card chartCard">
                        <div class="cardHeader">
                            <h5><i class="fas fa-cog me-2"></i> 常规设置</h5>
                        </div>
                        <div class="card-body">
                            <form id="generalSettingsForm">
                                <div class="mb-3">
                                    <label for="siteTitle" class="form-label">网站标题</label>
                                    <input type="text" class="form-control bg-dark text-light border-secondary" id="siteTitle" value="NGC660 Bot Star - 管理后台">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="adminEmail" class="form-label">管理员邮箱</label>
                                    <input type="email" class="form-control bg-dark text-light border-secondary" id="adminEmail" value="<EMAIL>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="logLevel" class="form-label">日志级别</label>
                                    <select class="form-select bg-dark text-light border-secondary" id="logLevel">
                                        <option value="debug">Debug</option>
                                        <option value="info" selected>Info</option>
                                        <option value="warning">Warning</option>
                                        <option value="error">Error</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="timezone" class="form-label">时区</label>
                                    <select class="form-select bg-dark text-light border-secondary" id="timezone">
                                        <option value="Asia/Shanghai" selected>Asia/Shanghai (GMT+8)</option>
                                        <option value="Asia/Tokyo">Asia/Tokyo (GMT+9)</option>
                                        <option value="America/New_York">America/New_York (GMT-5)</option>
                                        <option value="Europe/London">Europe/London (GMT+0)</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3 form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableDebug" checked>
                                    <label class="form-check-label" for="enableDebug">启用调试模式</label>
                                </div>
                                
                                <div class="text-end">
                                    <button type="button" class="btn btn-secondary me-2">重置</button>
                                    <button type="button" class="btn btnCyber" id="saveGeneralSettings">保存设置</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 机器人设置 -->
                <div class="tab-pane fade" id="botSettings">
                    <div class="card chartCard">
                        <div class="cardHeader">
                            <h5><i class="fas fa-robot me-2"></i> 机器人设置</h5>
                        </div>
                        <div class="card-body">
                            <form id="botSettingsForm">
                                <div class="mb-3">
                                    <label for="botNickname" class="form-label">机器人昵称</label>
                                    <input type="text" class="form-control bg-dark text-light border-secondary" id="botNickname" value="NGC660星辰机器人">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="autoReply" class="form-label">自动回复内容</label>
                                    <textarea class="form-control bg-dark text-light border-secondary" id="autoReply" rows="3">您好！我是NGC660星辰机器人，正在自动回复模式中。如需帮助，请联系管理员。</textarea>
                                </div>
                                
                                <div class="mb-3 form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableAutoReply" checked>
                                    <label class="form-check-label" for="enableAutoReply">启用自动回复</label>
                                </div>
                                
                                <div class="mb-3 form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoLogin">
                                    <label class="form-check-label" for="autoLogin">自动登录</label>
                                </div>
                                
                                <div class="text-end">
                                    <button type="button" class="btn btn-secondary me-2">重置</button>
                                    <button type="button" class="btn btnCyber" id="saveBotSettings">保存设置</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 插件设置 -->
                <div class="tab-pane fade" id="pluginSettings">
                    <div class="card chartCard">
                        <div class="cardHeader">
                            <h5><i class="fas fa-puzzle-piece me-2"></i> 插件设置</h5>
                        </div>
                        <div class="card-body">
                            <form id="pluginSettingsForm">
                                <div class="mb-3">
                                    <label for="pluginDirectory" class="form-label">插件目录</label>
                                    <input type="text" class="form-control bg-dark text-light border-secondary" id="pluginDirectory" value="./Plugins">
                                </div>
                                
                                <div class="mb-3 form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoLoadPlugins" checked>
                                    <label class="form-check-label" for="autoLoadPlugins">自动加载插件</label>
                                </div>
                                
                                <div class="mb-3 form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="safeMode">
                                    <label class="form-check-label" for="safeMode">安全模式 (禁用所有插件)</label>
                                </div>
                                
                                <div class="text-end">
                                    <button type="button" class="btn btn-secondary me-2">重置</button>
                                    <button type="button" class="btn btnCyber" id="savePluginSettings">保存设置</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 安全设置 -->
                <div class="tab-pane fade" id="securitySettings">
                    <div class="card chartCard">
                        <div class="cardHeader">
                            <h5><i class="fas fa-shield-alt me-2"></i> 安全设置</h5>
                        </div>
                        <div class="card-body">
                            <form id="securitySettingsForm">
                                <div class="mb-3">
                                    <label for="sessionTimeout" class="form-label">会话超时时间 (分钟)</label>
                                    <input type="number" class="form-control bg-dark text-light border-secondary" id="sessionTimeout" value="30">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="maxLoginAttempts" class="form-label">最大登录尝试次数</label>
                                    <input type="number" class="form-control bg-dark text-light border-secondary" id="maxLoginAttempts" value="5">
                                </div>
                                
                                <div class="mb-3 form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableCaptcha" checked>
                                    <label class="form-check-label" for="enableCaptcha">启用验证码</label>
                                </div>
                                
                                <div class="mb-3 form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="forceHTTPS" checked>
                                    <label class="form-check-label" for="forceHTTPS">强制使用HTTPS</label>
                                </div>
                                
                                <div class="text-end">
                                    <button type="button" class="btn btn-secondary me-2">重置</button>
                                    <button type="button" class="btn btnCyber" id="saveSecuritySettings">保存设置</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 备份与恢复 -->
                <div class="tab-pane fade" id="backupSettings">
                    <div class="card chartCard">
                        <div class="cardHeader">
                            <h5><i class="fas fa-save me-2"></i> 备份与恢复</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <h6 class="mb-3">创建备份</h6>
                                <p class="text-secondary mb-3">创建系统配置和数据的完整备份。</p>
                                <div class="d-flex align-items-center">
                                    <div class="form-check me-3">
                                        <input class="form-check-input" type="checkbox" id="backupConfig" checked>
                                        <label class="form-check-label" for="backupConfig">配置文件</label>
                                    </div>
                                    <div class="form-check me-3">
                                        <input class="form-check-input" type="checkbox" id="backupPlugins" checked>
                                        <label class="form-check-label" for="backupPlugins">插件</label>
                                    </div>
                                    <div class="form-check me-3">
                                        <input class="form-check-input" type="checkbox" id="backupDatabase" checked>
                                        <label class="form-check-label" for="backupDatabase">数据库</label>
                                    </div>
                                </div>
                                <button class="btn btnCyber mt-3" id="createBackup">
                                    <i class="fas fa-download me-2"></i> 创建备份
                                </button>
                            </div>
                            
                            <div class="mb-4">
                                <h6 class="mb-3">恢复备份</h6>
                                <p class="text-secondary mb-3">从备份文件恢复系统配置和数据。</p>
                                <div class="mb-3">
                                    <input class="form-control bg-dark text-light border-secondary" type="file" id="restoreFile">
                                </div>
                                <button class="btn btn-warning" id="restoreBackup">
                                    <i class="fas fa-upload me-2"></i> 恢复备份
                                </button>
                            </div>
                            
                            <div>
                                <h6 class="mb-3">自动备份设置</h6>
                                <div class="mb-3">
                                    <label for="autoBackupInterval" class="form-label">自动备份频率</label>
                                    <select class="form-select bg-dark text-light border-secondary" id="autoBackupInterval">
                                        <option value="0">禁用自动备份</option>
                                        <option value="1">每天</option>
                                        <option value="7" selected>每周</option>
                                        <option value="30">每月</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="maxBackupFiles" class="form-label">保留备份数量</label>
                                    <input type="number" class="form-control bg-dark text-light border-secondary" id="maxBackupFiles" value="5">
                                </div>
                                <button class="btn btnCyber" id="saveBackupSettings">
                                    <i class="fas fa-save me-2"></i> 保存设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 保存常规设置
        document.getElementById('saveGeneralSettings').addEventListener('click', function() {
            // 这里应该发送AJAX请求保存设置
            // 模拟保存
            setTimeout(function() {
                showToast('设置已保存', '常规设置已成功更新', 'success');
            }, 500);
        });
        
        // 保存机器人设置
        document.getElementById('saveBotSettings').addEventListener('click', function() {
            // 这里应该发送AJAX请求保存设置
            // 模拟保存
            setTimeout(function() {
                showToast('设置已保存', '机器人设置已成功更新', 'success');
            }, 500);
        });
        
        // 保存插件设置
        document.getElementById('savePluginSettings').addEventListener('click', function() {
            // 这里应该发送AJAX请求保存设置
            // 模拟保存
            setTimeout(function() {
                showToast('设置已保存', '插件设置已成功更新', 'success');
            }, 500);
        });
        
        // 保存安全设置
        document.getElementById('saveSecuritySettings').addEventListener('click', function() {
            // 这里应该发送AJAX请求保存设置
            // 模拟保存
            setTimeout(function() {
                showToast('设置已保存', '安全设置已成功更新', 'success');
            }, 500);
        });
        
        // 创建备份
        document.getElementById('createBackup').addEventListener('click', function() {
            // 这里应该发送AJAX请求创建备份
            // 模拟备份创建
            const backupBtn = this;
            backupBtn.disabled = true;
            backupBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> 创建中...';
            
            setTimeout(function() {
                backupBtn.disabled = false;
                backupBtn.innerHTML = '<i class="fas fa-download me-2"></i> 创建备份';
                
                // 模拟下载备份文件
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = '#';
                a.download = `ngcbot_backup_${new Date().toISOString().slice(0, 10)}.zip`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                
                showToast('备份完成', '系统备份已创建并准备下载', 'success');
            }, 3000);
        });
        
        // 恢复备份
        document.getElementById('restoreBackup').addEventListener('click', function() {
            const fileInput = document.getElementById('restoreFile');
            if (!fileInput.files.length) {
                showToast('错误', '请选择备份文件', 'danger');
                return;
            }
            
            if (confirm('确定要恢复此备份吗？当前数据将被覆盖。')) {
                // 这里应该发送AJAX请求恢复备份
                // 模拟恢复过程
                const restoreBtn = this;
                restoreBtn.disabled = true;
                restoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> 恢复中...';
                
                setTimeout(function() {
                    restoreBtn.disabled = false;
                    restoreBtn.innerHTML = '<i class="fas fa-upload me-2"></i> 恢复备份';
                    fileInput.value = '';
                    showToast('恢复完成', '系统已成功从备份恢复', 'success');
                }, 4000);
            }
        });
        
        // 保存备份设置
        document.getElementById('saveBackupSettings').addEventListener('click', function() {
            // 这里应该发送AJAX请求保存设置
            // 模拟保存
            setTimeout(function() {
                showToast('设置已保存', '备份设置已成功更新', 'success');
            }, 500);
        });
    });
</script>
{% endblock %} 
{% extends "base.html" %}

{% block title %}个人资料 - NGC660 Bot Star{% endblock %}

{% block header_title %}个人资料{% endblock %}

{% block content %}
<div class="profileContainer">
    <div class="row">
        <!-- 个人信息卡片 -->
        <div class="col-md-4 mb-4">
            <div class="card chartCard">
                <div class="cardHeader">
                    <h5><i class="fas fa-user me-2"></i> 个人信息</h5>
                </div>
                <div class="card-body text-center">
                    <div class="profileAvatar mb-4">
                        <div class="avatarCircle card3d">
                            <i class="fas fa-user-circle fa-6x"></i>
                        </div>
                        <button class="btn btn-sm btnCyber mt-2">
                            <i class="fas fa-camera me-1"></i> 更换头像
                        </button>
                    </div>
                    
                    <h4 class="username neonText mb-2">{{ current_user.username }}</h4>
                    <p class="userRole mb-3">
                        <span class="badge bg-primary">管理员</span>
                    </p>
                    
                    <div class="userStats d-flex justify-content-center text-start">
                        <div class="statsItem me-4">
                            <div class="statsValue">{{ current_user.last_login|default('未知', true) }}</div>
                            <div class="statsLabel text-secondary">上次登录</div>
                        </div>
                        <div class="statsItem">
                            <div class="statsValue">{{ current_user.login_count|default('0', true) }}</div>
                            <div class="statsLabel text-secondary">登录次数</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-danger w-100">
                        <i class="fas fa-sign-out-alt me-2"></i> 退出登录
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 账户信息 -->
        <div class="col-md-8">
            <div class="card chartCard mb-4">
                <div class="cardHeader">
                    <h5><i class="fas fa-id-card me-2"></i> 账户信息</h5>
                </div>
                <div class="card-body">
                    <form id="profileForm">
                        <!-- 用户名 -->
                        <div class="mb-3 row">
                            <label for="username" class="col-sm-3 col-form-label">用户名</label>
                            <div class="col-sm-9">
                                <input type="text" readonly class="form-control-plaintext text-light" id="username" value="{{ current_user.username }}">
                            </div>
                        </div>
                        
                        <!-- 电子邮箱 -->
                        <div class="mb-3 row">
                            <label for="email" class="col-sm-3 col-form-label">电子邮箱</label>
                            <div class="col-sm-9">
                                <input type="email" class="form-control bg-dark text-light border-secondary" id="email" value="<EMAIL>">
                            </div>
                        </div>
                        
                        <!-- 显示名称 -->
                        <div class="mb-3 row">
                            <label for="displayName" class="col-sm-3 col-form-label">显示名称</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control bg-dark text-light border-secondary" id="displayName" value="NGC管理员">
                            </div>
                        </div>
                        
                        <!-- 电话号码 -->
                        <div class="mb-3 row">
                            <label for="phone" class="col-sm-3 col-form-label">电话号码</label>
                            <div class="col-sm-9">
                                <input type="tel" class="form-control bg-dark text-light border-secondary" id="phone" value="">
                            </div>
                        </div>
                        
                        <!-- 所在地区 -->
                        <div class="mb-3 row">
                            <label for="location" class="col-sm-3 col-form-label">所在地区</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control bg-dark text-light border-secondary" id="location" value="中国">
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="button" class="btn btnCyber" id="saveProfile">
                                <i class="fas fa-save me-2"></i> 保存更改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 修改密码 -->
            <div class="card chartCard">
                <div class="cardHeader">
                    <h5><i class="fas fa-key me-2"></i> 修改密码</h5>
                </div>
                <div class="card-body">
                    <form id="passwordForm">
                        <!-- 当前密码 -->
                        <div class="mb-3">
                            <label for="currentPassword" class="form-label">当前密码</label>
                            <input type="password" class="form-control bg-dark text-light border-secondary" id="currentPassword">
                        </div>
                        
                        <!-- 新密码 -->
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">新密码</label>
                            <input type="password" class="form-control bg-dark text-light border-secondary" id="newPassword">
                            <div class="form-text text-secondary">密码至少需要8个字符，包含大小写字母、数字和特殊字符</div>
                        </div>
                        
                        <!-- 确认新密码 -->
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">确认新密码</label>
                            <input type="password" class="form-control bg-dark text-light border-secondary" id="confirmPassword">
                        </div>
                        
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary me-2">重置</button>
                            <button type="button" class="btn btnCyber" id="changePassword">
                                <i class="fas fa-key me-2"></i> 修改密码
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 保存个人资料
        document.getElementById('saveProfile').addEventListener('click', function() {
            // 这里应该发送AJAX请求保存个人资料
            // 模拟保存
            const saveBtn = this;
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> 保存中...';
            
            setTimeout(function() {
                saveBtn.disabled = false;
                saveBtn.innerHTML = '<i class="fas fa-save me-2"></i> 保存更改';
                showToast('保存成功', '个人资料已更新', 'success');
            }, 1000);
        });
        
        // 修改密码
        document.getElementById('changePassword').addEventListener('click', function() {
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            // 简单的表单验证
            if (!currentPassword) {
                showToast('错误', '请输入当前密码', 'danger');
                return;
            }
            
            if (!newPassword) {
                showToast('错误', '请输入新密码', 'danger');
                return;
            }
            
            if (newPassword.length < 8) {
                showToast('错误', '新密码至少需要8个字符', 'danger');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                showToast('错误', '两次输入的密码不一致', 'danger');
                return;
            }
            
            // 这里应该发送AJAX请求修改密码
            // 模拟修改密码
            const changeBtn = this;
            changeBtn.disabled = true;
            changeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> 处理中...';
            
            setTimeout(function() {
                changeBtn.disabled = false;
                changeBtn.innerHTML = '<i class="fas fa-key me-2"></i> 修改密码';
                
                // 清空表单
                document.getElementById('currentPassword').value = '';
                document.getElementById('newPassword').value = '';
                document.getElementById('confirmPassword').value = '';
                
                showToast('修改成功', '密码已成功修改', 'success');
            }, 1500);
        });
    });
</script>
{% endblock %} 
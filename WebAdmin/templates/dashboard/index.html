{% extends "base.html" %}

{% block title %}仪表盘 - NGC660 Bot Star{% endblock %}

{% block header_title %}<span class="neon-title">系统仪表盘</span>{% endblock %}

{% block content %}
<div class="dashboard-container animate__animated animate__fadeIn">
    <!-- 状态卡片行 -->
    <div class="row mb-4">
        <!-- 系统状态卡片 -->
        <div class="col-md-3 animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
            <div class="card status-card dynamic-card glow-border">
                <div class="card-body">
                    <div class="status-icon pulse-glow">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="status-info">
                        <h5 class="status-title">系统状态</h5>
                        <div class="status-value">
                            <span id="systemStatus" class="status-badge online">在线</span>
                            <span id="systemUptime" class="status-detail">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 机器人状态卡片 -->
        <div class="col-md-3 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
            <div class="card status-card dynamic-card glow-border">
                <div class="card-body">
                    <div class="status-icon pulse-glow">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="status-info">
                        <h5 class="status-title">机器人状态</h5>
                        <div class="status-value">
                            <span id="botStatus" class="status-badge offline">离线</span>
                            <span id="botName" class="status-detail">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 插件状态卡片 -->
        <div class="col-md-3 animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
            <div class="card status-card dynamic-card glow-border">
                <div class="card-body">
                    <div class="status-icon pulse-glow">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <div class="status-info">
                        <h5 class="status-title">总共插件</h5>
                        <div class="status-value">
                            <span id="pluginCount" class="status-count number-animate">0</span>
                            <span id="enabledPluginCount" class="status-detail">已启用: 0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 内存使用卡片 -->
        <div class="col-md-3 animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
            <div class="card status-card dynamic-card glow-border">
                <div class="card-body">
                    <div class="status-icon pulse-glow">
                        <i class="fas fa-memory"></i>
                    </div>
                    <div class="status-info">
                        <h5 class="status-title">内存使用</h5>
                        <div class="status-value">
                            <span id="memoryPercent" class="status-count number-animate">0%</span>
                            <span id="memoryDetail" class="status-detail">0GB / 0GB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图表和信息 -->
    <div class="row mb-4">
        <!-- CPU 使用图表 -->
        <div class="col-md-6 animate__animated animate__fadeInLeft" style="animation-delay: 0.5s">
            <div class="card chart-card dynamic-card glow-border">
                <div class="card-header">
                    <h5><i class="fas fa-microchip me-2 pulse-glow"></i> CPU 使用率</h5>
                </div>
                <div class="card-body">
                    <div id="cpuChart" class="chart-container"></div>
                </div>
            </div>
        </div>
        
        <!-- 机器人详情 -->
        <div class="col-md-6 animate__animated animate__fadeInRight" style="animation-delay: 0.5s">
            <div class="card chart-card dynamic-card glow-border">
                <div class="card-header">
                    <h5><i class="fas fa-robot me-2 pulse-glow"></i> 机器人详情</h5>
                </div>
                <div class="card-body">
                    <div id="botInfo" class="bot-info">
                        <div class="alert alert-secondary text-center" id="noBotMessage">
                            <i class="fas fa-robot fa-2x mb-2 pulse-glow"></i>
                            <p>未检测到在线的机器人</p>
                            <small>请确保您的微信机器人已登录</small>
                        </div>
                        <div id="botInfoDetails" style="display: none;">
                            <div class="bot-avatar">
                                <img id="botAvatarImg" src="" alt="机器人头像" class="rounded-circle mb-3" style="width: 100px; height: 100px; object-fit: cover; border: 3px solid var(--primaryColor);">
                            </div>
                            <div class="bot-details">
                                <h4 id="botNickname" class="glow-effect">加载中...</h4>
                                <p><i class="fas fa-id-card pulse-glow"></i> <span id="botWxid">加载中...</span></p>
                                <p><i class="fas fa-hashtag pulse-glow"></i> <span id="botWxnum">加载中...</span></p>
                                <p><i class="fas fa-phone pulse-glow"></i> <span id="botPhone">加载中...</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息和插件概览 -->
    <div class="row">
        <!-- 系统信息 -->
        <div class="col-md-6 animate__animated animate__fadeInUp" style="animation-delay: 0.6s">
            <div class="card chart-card dynamic-card glow-border">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2 pulse-glow"></i> 系统信息</h5>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <table class="table table-hover table-sm">
                            <tbody>
                                <tr class="animate-on-scroll">
                                    <td width="30%"><i class="fas fa-desktop me-2 pulse-glow"></i> 操作系统</td>
                                    <td id="sysOs">加载中...</td>
                                </tr>
                                <tr class="animate-on-scroll">
                                    <td><i class="fas fa-microchip me-2 pulse-glow"></i> CPU</td>
                                    <td id="sysCpu">加载中...</td>
                                </tr>
                                <tr class="animate-on-scroll">
                                    <td><i class="fas fa-memory me-2 pulse-glow"></i> 内存</td>
                                    <td id="sysMemory">加载中...</td>
                                </tr>
                                <tr class="animate-on-scroll">
                                    <td><i class="fas fa-hdd me-2 pulse-glow"></i> 磁盘</td>
                                    <td id="sysDisk">加载中...</td>
                                </tr>
                                <tr class="animate-on-scroll">
                                    <td><i class="fas fa-network-wired me-2 pulse-glow"></i> 网络</td>
                                    <td id="sysNetwork">加载中...</td>
                                </tr>
                                <tr class="animate-on-scroll">
                                    <td><i class="fas fa-code me-2 pulse-glow"></i> Python版本</td>
                                    <td id="sysPython">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 插件概览 -->
        <div class="col-md-6 animate__animated animate__fadeInUp" style="animation-delay: 0.7s">
            <div class="card chart-card dynamic-card glow-border">
                <div class="card-header">
                    <h5><i class="fas fa-puzzle-piece me-2 pulse-glow"></i> 插件概览</h5>
                </div>
                <div class="card-body">
                    <div id="pluginOverview" class="plugin-overview">
                        <div class="row mb-3">
                            <div class="col-6">
                                <div class="plugin-stat-card card3d glow-border">
                                    <div class="stat-value number-animate" id="totalPlugins">0</div>
                                    <div class="stat-label">总插件数</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="plugin-stat-card card3d glow-border">
                                    <div class="stat-value number-animate" id="enabledPlugins">0</div>
                                    <div class="stat-label">已启用</div>
                                </div>
                            </div>
                        </div>
                        <div class="plugin-chart-container">
                            <div id="pluginChart" class="chart-container"></div>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('plugins.list_plugins') }}" class="btn-cyber glow-effect">
                                <i class="fas fa-cog"></i> 管理插件
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化图表
        initCharts();
        
        // 第一次加载数据
        fetchSystemInfo();
        
        // 定时刷新数据
        setInterval(fetchSystemInfo, 10000);
        
        // 监听主题变化，刷新图表
        document.addEventListener('themeChange', function() {
            // 刷新CPU图表
            if (window.cpuChart) {
                updateChartTheme(window.cpuChart);
            }
            
            // 刷新插件图表
            if (window.pluginChart) {
                updateChartTheme(window.pluginChart);
            }
        });
        
        // 添加动画效果
        animateCounters();
    });
    
    // 初始化图表
    function initCharts() {
        const isDark = document.body.classList.contains('darkMode');
        const textColor = isDark ? '#f8f9fa' : '#212529';
        const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
        
        // CPU图表选项
        const cpuOptions = {
            series: [{
                name: 'CPU使用率',
                data: [0, 0, 0, 0, 0, 0, 0, 0]
            }],
            chart: {
                type: 'bar',
                height: 250,
                fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                toolbar: {
                    show: false
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800,
                    animateGradually: {
                        enabled: true,
                        delay: 150
                    },
                    dynamicAnimation: {
                        enabled: true,
                        speed: 350
                    }
                },
                background: 'transparent'
            },
            colors: ['#4361ee'],
            plotOptions: {
                bar: {
                    columnWidth: '70%',
                    borderRadius: 5,
                    dataLabels: {
                        position: 'top'
                    }
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return val.toFixed(1) + '%';
                },
                offsetY: -20,
                style: {
                    fontSize: '12px',
                    colors: [textColor]
                }
            },
            xaxis: {
                categories: ['CPU 1', 'CPU 2', 'CPU 3', 'CPU 4', 'CPU 5', 'CPU 6', 'CPU 7', 'CPU 8'],
                position: 'bottom',
                labels: {
                    style: {
                        colors: textColor
                    }
                },
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                },
                crosshairs: {
                    fill: {
                        type: 'gradient',
                        gradient: {
                            colorFrom: '#7209b7',
                            colorTo: '#4361ee',
                            stops: [0, 100],
                            opacityFrom: 0.4,
                            opacityTo: 0.5,
                        }
                    }
                },
                tooltip: {
                    enabled: true,
                }
            },
            yaxis: {
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false,
                },
                labels: {
                    show: true,
                    style: {
                        colors: textColor
                    }
                }
            },
            grid: {
                borderColor: gridColor,
                strokeDashArray: 5,
                position: 'back',
                xaxis: {
                    lines: {
                        show: false
                    }
                },
                yaxis: {
                    lines: {
                        show: true
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shade: 'dark',
                    type: "vertical",
                    shadeIntensity: 0.5,
                    gradientToColors: ['#7209b7'],
                    inverseColors: true,
                    opacityFrom: 1,
                    opacityTo: 1,
                    stops: [0, 100]
                }
            },
            tooltip: {
                theme: isDark ? 'dark' : 'light',
                y: {
                    formatter: function(val) {
                        return val.toFixed(1) + '%';
                    }
                }
            }
        };
        
        // 插件图表选项
        const pluginOptions = {
            series: [0, 0],
            chart: {
                type: 'donut',
                height: 200,
                fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800,
                    animateGradually: {
                        enabled: true,
                        delay: 150
                    },
                    dynamicAnimation: {
                        enabled: true,
                        speed: 350
                    }
                },
                background: 'transparent'
            },
            colors: ['#4361ee', '#6c757d'],
            labels: ['已启用', '已禁用'],
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%',
                        labels: {
                            show: true,
                            name: {
                                show: true,
                                fontSize: '14px',
                                fontWeight: 600,
                                color: textColor
                            },
                            value: {
                                show: true,
                                fontSize: '20px',
                                fontWeight: 700,
                                color: textColor
                            },
                            total: {
                                show: true,
                                label: '总计',
                                fontSize: '14px',
                                fontWeight: 600,
                                color: textColor
                            }
                        }
                    }
                }
            },
            dataLabels: {
                enabled: false
            },
            legend: {
                show: true,
                position: 'bottom',
                horizontalAlign: 'center',
                labels: {
                    colors: textColor
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shade: 'dark',
                    type: "vertical",
                    shadeIntensity: 0.5,
                    gradientToColors: ['#7209b7', '#adb5bd'],
                    inverseColors: false,
                    opacityFrom: 1,
                    opacityTo: 1,
                    stops: [0, 100]
                }
            },
            tooltip: {
                theme: isDark ? 'dark' : 'light'
            }
        };
        
        // 初始化CPU图表
        window.cpuChart = new ApexCharts(document.querySelector("#cpuChart"), cpuOptions);
        window.cpuChart.render();
        
        // 初始化插件图表
        window.pluginChart = new ApexCharts(document.querySelector("#pluginChart"), pluginOptions);
        window.pluginChart.render();
    }
    
    // 更新图表主题
    function updateChartTheme(chart) {
        const isDark = document.body.classList.contains('darkMode');
        const textColor = isDark ? '#f8f9fa' : '#212529';
        const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
        
        chart.updateOptions({
            tooltip: {
                theme: isDark ? 'dark' : 'light'
            },
            xaxis: {
                labels: {
                    style: {
                        colors: textColor
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: textColor
                    }
                }
            },
            grid: {
                borderColor: gridColor
            },
            plotOptions: {
                pie: {
                    donut: {
                        labels: {
                            name: {
                                color: textColor
                            },
                            value: {
                                color: textColor
                            },
                            total: {
                                color: textColor
                            }
                        }
                    }
                }
            },
            legend: {
                labels: {
                    colors: textColor
                }
            }
        });
    }
    
    // 获取系统信息
    function fetchSystemInfo() {
        fetch('/dashboard/system_info')
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                updateSystemInfo(data);
            })
            .catch(error => {
                console.error('获取系统信息失败:', error);
                showToast('错误', '获取系统信息失败: ' + error.message, 'danger');
            });
    }
    
    // 更新系统信息
    function updateSystemInfo(data) {
        // 更新系统状态
        const systemUptime = document.getElementById('systemUptime');
        systemUptime.textContent = '正常运行中';
        
        // 更新机器人状态
        const botStatus = document.getElementById('botStatus');
        const botName = document.getElementById('botName');
        const noBotMessage = document.getElementById('noBotMessage');
        const botInfoDetails = document.getElementById('botInfoDetails');
        
        if (data.bot_status === 'online') {
            botStatus.className = 'status-badge online';
            botStatus.textContent = '在线';
            botName.textContent = data.bot_info.nickname || 'NGC机器人';
            
            // 更新机器人详情
            document.getElementById('botNickname').textContent = data.bot_info.nickname || '未知昵称';
            document.getElementById('botWxid').textContent = data.bot_info.id || '未知ID';
            document.getElementById('botWxnum').textContent = data.bot_info.wxNum || '未知微信号';
            document.getElementById('botPhone').textContent = data.bot_info.phone || '未知手机号';
            
            // 更新机器人头像
            const avatarImg = document.getElementById('botAvatarImg');
            if (data.bot_info.avatar) {
                avatarImg.src = data.bot_info.avatar;
            } else {
                // 如果没有头像链接，使用默认头像
                avatarImg.src = 'https://api.dicebear.com/6.x/bottts/svg?seed=' + encodeURIComponent(data.bot_info.nickname || 'ngcbot');
            }
            avatarImg.onerror = function() {
                // 如果加载失败，使用备用头像生成器
                this.src = 'https://api.dicebear.com/6.x/bottts/svg?seed=' + encodeURIComponent(data.bot_info.nickname || 'ngcbot');
            };
            
            // 显示详情，隐藏提示
            noBotMessage.style.display = 'none';
            botInfoDetails.style.display = 'block';
        } else {
            botStatus.className = 'status-badge offline';
            botStatus.textContent = '离线';
            botName.textContent = '未连接';
            
            // 隐藏详情，显示提示
            noBotMessage.style.display = 'block';
            botInfoDetails.style.display = 'none';
        }
        
        // 更新插件信息
        const oldPluginCount = parseInt(document.getElementById('pluginCount').textContent) || 0;
        const newPluginCount = data.plugin_count;
        
        // 使用动画效果更新数字
        animateValue('pluginCount', oldPluginCount, newPluginCount, 1000);
        document.getElementById('enabledPluginCount').textContent = `已启用: ${data.enabled_plugin_count}`;
        
        // 更新内存使用情况
        const oldMemoryPercent = parseInt(document.getElementById('memoryPercent').textContent) || 0;
        const newMemoryPercent = data.memory_info.percent;
        
        // 使用动画效果更新数字
        animateValue('memoryPercent', oldMemoryPercent, newMemoryPercent, 1000, '%');
        document.getElementById('memoryDetail').textContent = 
            `${data.memory_info.used.toFixed(1)}GB / ${data.memory_info.total.toFixed(1)}GB`;
        
        // 更新CPU图表
        if (window.cpuChart) {
            window.cpuChart.updateSeries([{
                name: 'CPU使用率',
                data: data.cpu_percent.slice(0, 8)
            }]);
            
            // 更新CPU核心标签
            if (data.cpu_count > 0) {
                const categories = Array.from({ length: data.cpu_count }, (_, i) => `CPU ${i+1}`);
                window.cpuChart.updateOptions({
                    xaxis: {
                        categories: categories
                    }
                });
            }
        }
        
        // 更新插件图表
        if (window.pluginChart) {
            window.pluginChart.updateSeries([
                data.enabled_plugin_count,
                data.disabled_plugin_count
            ]);
        }
        
        // 更新插件统计卡片
        animateValue('totalPlugins', 
            parseInt(document.getElementById('totalPlugins').textContent) || 0, 
            data.plugin_count, 
            1000);
            
        animateValue('enabledPlugins', 
            parseInt(document.getElementById('enabledPlugins').textContent) || 0, 
            data.enabled_plugin_count, 
            1000);
        
        // 更新系统信息表
        document.getElementById('sysOs').textContent = 
            `${data.sys_info.system} ${data.sys_info.release}`;
            
        document.getElementById('sysCpu').textContent = 
            `${data.sys_info.processor} (${data.cpu_count} 核)`;
            
        document.getElementById('sysMemory').textContent = 
            `总计 ${data.memory_info.total.toFixed(1)}GB，已用 ${data.memory_info.percent}%`;
            
        document.getElementById('sysDisk').textContent = 
            `总计 ${data.disk_info.total.toFixed(1)}GB，已用 ${data.disk_info.percent}%`;
            
        document.getElementById('sysNetwork').textContent = 
            `已发送 ${data.net_info.bytes_sent.toFixed(1)}MB，已接收 ${data.net_info.bytes_recv.toFixed(1)}MB`;
            
        document.getElementById('sysPython').textContent = 
            data.sys_info.python_version;
    }
    
    // 数字变化动画
    function animateValue(elementId, start, end, duration, suffix = '') {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        // 标记数字发生了变化
        if (start !== end) {
            element.classList.add('changed');
            
            // 500毫秒后移除类
            setTimeout(() => {
                element.classList.remove('changed');
            }, 500);
        }
        
        let startTime = null;
        
        function step(timestamp) {
            if (!startTime) startTime = timestamp;
            const progress = Math.min((timestamp - startTime) / duration, 1);
            const value = Math.floor(progress * (end - start) + start);
            element.textContent = value + suffix;
            
            if (progress < 1) {
                window.requestAnimationFrame(step);
            } else {
                element.textContent = end + suffix;
            }
        }
        
        window.requestAnimationFrame(step);
    }
    
    // 添加计数器动画
    function animateCounters() {
        const counters = document.querySelectorAll('.number-animate');
        
        counters.forEach(counter => {
            const value = parseInt(counter.innerText);
            counter.innerText = '0';
            
            const updateCount = () => {
                const target = parseInt(counter.getAttribute('data-target') || value);
                const count = parseInt(counter.innerText);
                const increment = Math.ceil(target / 20);
                
                if (count < target) {
                    counter.innerText = Math.min(count + increment, target);
                    setTimeout(updateCount, 100);
                } else {
                    counter.innerText = target;
                }
            };
            
            updateCount();
        });
    }
</script>
{% endblock %} 
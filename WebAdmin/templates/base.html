<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}NGC Bot Star - 管理后台{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/apexcharts@3.35.3/dist/apexcharts.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 侧边栏 -->
    <div id="sidebar">
        <div class="brand-container">
            <div class="brand-logo">
                <span>NGC Bot</span>
            </div>
            <div class="logo-version">
                <span>星辰版 v2.3</span>
            </div>
        </div>
        
        <ul class="nav-links">
            <li class="nav-item">
                <a href="{{ url_for('dashboard.index') }}" class="nav-link {% if request.endpoint == 'dashboard.index' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>控制面板</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a href="{{ url_for('plugins.list_plugins') }}" class="nav-link {% if request.endpoint == 'plugins.list_plugins' %}active{% endif %}">
                    <i class="fas fa-puzzle-piece"></i>
                    <span>插件管理</span>
                </a>
            </li>
            
            <div class="nav-divider"></div>
            
            <li class="nav-item">
                <a href="{{ url_for('auth.logout') }}" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>退出登录</span>
                </a>
            </li>
        </ul>
        
        <div class="sidebar-footer">
            <span>© NGC660 安全实验室</span>
        </div>
    </div>
    
    <!-- 顶部导航 -->
    <div id="header">
        <button id="toggle-sidebar" type="button">
            <i class="fas fa-bars"></i>
        </button>
        
        <div class="header-title">
            <span>{% block header_title %}控制面板{% endblock %}</span>
        </div>
        
        <div class="actions">
            <button id="refresh-page" class="btn btn-sm btn-outline-primary" title="刷新页面">
                <i class="fas fa-sync-alt"></i>
            </button>
        </div>
    </div>
    
    <!-- 主内容区 -->
    <div id="content">
        <div class="container-fluid py-3 px-4 fade-in">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- Toast 通知 -->
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto" id="toast-title">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toast-message">
                消息内容
            </div>
        </div>
    </div>
    
    <!-- JavaScript 库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.35.3/dist/apexcharts.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边栏切换
            const toggleSidebar = document.getElementById('toggle-sidebar');
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');
            const header = document.getElementById('header');
            
            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                content.classList.toggle('expanded');
                header.classList.toggle('expanded');
            });
            
            // 刷新页面按钮
            const refreshButton = document.getElementById('refresh-page');
            refreshButton.addEventListener('click', function() {
                // 添加旋转动画效果
                const icon = refreshButton.querySelector('i');
                icon.style.transition = 'transform 0.5s ease';
                icon.style.transform = 'rotate(360deg)';
                
                // 显示加载提示
                showToast('刷新中', '正在重新加载页面...', 'info');
                
                // 延迟后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            });
            
            // 添加按钮涟漪效果
            const buttons = document.querySelectorAll('button, .btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const x = e.clientX - e.target.getBoundingClientRect().left;
                    const y = e.clientY - e.target.getBoundingClientRect().top;
                    
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    ripple.style.left = `${x}px`;
                    ripple.style.top = `${y}px`;
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
            
            // Toast通知函数
            window.showToast = function(title, message, type = 'primary') {
                const toast = document.getElementById('toast');
                const toastTitle = document.getElementById('toast-title');
                const toastMessage = document.getElementById('toast-message');
                
                // 设置内容
                toastTitle.textContent = title;
                toastMessage.textContent = message;
                
                // 设置样式
                toast.className = 'toast';
                let bgClass = 'bg-primary';
                if (type === 'danger') bgClass = 'bg-danger';
                if (type === 'success') bgClass = 'bg-success';
                if (type === 'warning') bgClass = 'bg-warning';
                if (type === 'info') bgClass = 'bg-info';
                toast.classList.add(bgClass);
                toast.classList.add('text-white');
                
                // 创建Bootstrap toast实例并显示
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();
            };
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html> 
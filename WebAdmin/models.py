from flask_login import UserMixin
import hashlib


class User(UserMixin):
    """用户模型，用于Web管理后台的身份验证"""
    
    def __init__(self, username, password, role='user'):
        """
        初始化用户
        :param username: 用户名
        :param password: 密码
        :param role: 角色（admin, user, guest）
        """
        self.username = username
        self.password = password  # 实际使用中应该存储哈希值
        self.role = role
    
    def get_id(self):
        """获取用户ID"""
        return self.username
    
    def checkPassword(self, password):
        """检查密码是否正确"""
        return self.password == password
    
    def isAdmin(self):
        """检查是否为管理员"""
        return self.role == 'admin'
    
    @staticmethod
    def hashPassword(password):
        """哈希密码"""
        return hashlib.sha256(password.encode()).hexdigest() 
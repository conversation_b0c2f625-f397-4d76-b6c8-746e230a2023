from flask import Blueprint, render_template, g, jsonify, request
from flask_login import login_required, current_user
import psutil
import platform
import time
import json
from datetime import datetime
from loguru import logger

bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')


@bp.route('/')
@login_required
def index():
    """仪表盘首页"""
    return render_template('dashboard/index.html')


@bp.route('/system_info')
@login_required
def system_info():
    """获取系统信息"""
    try:
        # 系统信息
        sys_info = {
            'system': platform.system(),
            'node': platform.node(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
        }
        
        # CPU信息
        cpu_count = psutil.cpu_count()
        cpu_percent = psutil.cpu_percent(interval=0.1, percpu=True)
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_info = {
            'total': memory.total / (1024 * 1024 * 1024),  # GB
            'available': memory.available / (1024 * 1024 * 1024),  # GB
            'percent': memory.percent,
            'used': memory.used / (1024 * 1024 * 1024),  # GB
        }
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_info = {
            'total': disk.total / (1024 * 1024 * 1024),  # GB
            'used': disk.used / (1024 * 1024 * 1024),  # GB
            'free': disk.free / (1024 * 1024 * 1024),  # GB
            'percent': disk.percent,
        }
        
        # 网络信息
        net = psutil.net_io_counters()
        net_info = {
            'bytes_sent': net.bytes_sent / (1024 * 1024),  # MB
            'bytes_recv': net.bytes_recv / (1024 * 1024),  # MB
            'packets_sent': net.packets_sent,
            'packets_recv': net.packets_recv,
        }
        
        # 进程信息
        process_count = len(psutil.pids())
        
        # 机器人信息
        bot_status = 'offline'
        bot_info = {}
        
        # 安全获取机器人信息
        if hasattr(g, 'ngc_bot_api') and g.ngc_bot_api is not None:
            try:
                bot_list_data = g.ngc_bot_api.getRobotList(1)
                
                if bot_list_data.get('status') == 200:
                    bot_list = bot_list_data.get('data', {})
                    
                    if bot_list:
                        for bot_id, bot_data in bot_list.items():
                            if bot_data.get('status') == '在线':
                                bot_status = 'online'
                                bot_info = {
                                    'id': bot_id,
                                    'nickname': bot_data.get('nickName', ''),
                                    'wxNum': bot_data.get('wxNum', ''),
                                    'phone': bot_data.get('phone', ''),
                                    'avatar': bot_data.get('avatarUrl', ''),
                                }
                                break
            except Exception as e:
                logger.error(f"获取机器人信息时出错: {e}")
        else:
            logger.warning("ngc_bot_api不可用，无法获取机器人信息")
        
        # 插件信息
        plugin_count = 0
        enabled_plugins = []
        disabled_plugins = []
        
        # 安全获取插件信息
        if hasattr(g, 'plugin_manager') and g.plugin_manager is not None:
            try:
                loaded_plugin_count = len(g.plugin_manager.plugins)
                enabled_plugins = g.plugin_manager.get_enabled_plugins()
                disabled_plugins = g.plugin_manager.get_disabled_plugins()
                
                # 总插件数应该是已加载的插件数加上已禁用的插件数
                plugin_count = loaded_plugin_count + len(disabled_plugins)
            except Exception as e:
                logger.error(f"获取插件信息时出错: {e}")
        else:
            logger.warning("plugin_manager不可用，无法获取插件信息")
        
        # 返回所有信息
        result = {
            'sys_info': sys_info,
            'cpu_count': cpu_count,
            'cpu_percent': cpu_percent,
            'memory_info': memory_info,
            'disk_info': disk_info,
            'net_info': net_info,
            'process_count': process_count,
            'bot_status': bot_status,
            'bot_info': bot_info,
            'plugin_count': plugin_count,
            'enabled_plugin_count': len(enabled_plugins),
            'disabled_plugin_count': len(disabled_plugins),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        }
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取系统信息时发生错误: {e}")
        
        # 返回错误信息
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        }), 500


@bp.route('/error_404')
def error_404():
    """404错误页面"""
    return render_template('errors/404.html'), 404


@bp.route('/error_500')
def error_500():
    """500错误页面"""
    return render_template('errors/500.html'), 500 
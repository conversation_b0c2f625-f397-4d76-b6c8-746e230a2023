from flask import Blueprint, render_template, request, flash, redirect, url_for, current_app
from flask_login import login_user, logout_user, login_required, current_user
from WebAdmin.models import User
from loguru import logger
import time

bp = Blueprint('auth', __name__, url_prefix='/auth')


@bp.route('/login', methods=('GET', 'POST'))
def login():
    """处理登录请求"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
        
    # POST请求处理登录
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        # 记住我选项
        remember = True if request.form.get('remember') else False
        
        # 验证用户
        users = current_app.config['USERS']
        userFound = False
        
        for userData in users:
            if userData['username'] == username and userData['password'] == password:
                user = User(userData['username'], userData['password'], userData['role'])
                login_user(user, remember=remember)
                logger.info(f"用户 {username} 登录成功，IP: {request.remote_addr}")
                
                # 获取登录后要跳转的URL
                nextPage = request.args.get('next')
                if not nextPage or nextPage.startswith('/'):
                    nextPage = url_for('dashboard.index')
                    
                # 跳转到仪表盘或请求的页面
                return redirect(nextPage)
                
            if userData['username'] == username:
                userFound = True
        
        # 登录失败处理
        if userFound:
            flash('密码错误，请重试', 'danger')
            logger.warning(f"用户 {username} 登录失败（密码错误），IP: {request.remote_addr}")
        else:
            flash('用户不存在', 'danger')
            logger.warning(f"尝试使用不存在的用户名 {username} 登录，IP: {request.remote_addr}")
            
    # GET请求渲染登录页面
    return render_template('auth/login.html')


@bp.route('/logout')
@login_required
def logout():
    """处理注销请求"""
    username = current_user.username
    logout_user()
    logger.info(f"用户 {username} 已注销")
    flash('您已成功注销', 'success')
    return redirect(url_for('auth.login')) 
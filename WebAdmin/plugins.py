from flask import Blueprint, render_template, g, jsonify, request, flash, redirect, url_for
from flask_login import login_required, current_user
from loguru import logger
import inspect
import os
import importlib
import re

bp = Blueprint('plugins', __name__, url_prefix='/plugins')


@bp.route('/')
@login_required
def list_plugins():
    """插件管理页面"""
    return render_template('plugins/index.html')


def normalize_plugin_status(status):
    """标准化插件状态，确保前后端一致
    
    将各种可能的状态值标准化为 'enabled' 或 'disabled'
    """
    if not status:
        return 'disabled'
    
    original_status = status
    
    # 转换为字符串并转小写
    try:
        if isinstance(status, bool):
            status_str = str(status).lower()
        else:
            status_str = str(status).lower()
    except:
        status_str = "unknown"
    
    # 表示启用的状态（扩展了可能的值）
    enabled_statuses = [
        'enabled', 'enable', 'on', 'yes', 'true', 't', 'y', '1', 
        'active', 'activated', 'running', '已加载', '已启用', '加载', '启用', 'loaded'
    ]
    
    # 如果状态在启用列表中，返回'enabled'，否则返回'disabled'
    normalized = 'enabled' if status_str in enabled_statuses else 'disabled'
    
    return normalized


@bp.route('/api/list')
@login_required
def get_plugins_list():
    """获取插件列表"""
    # 插件列表
    plugins_data = []
    
    # 检查插件管理器是否可用
    if not hasattr(g, 'plugin_manager') or g.plugin_manager is None:
        # 返回空列表但包含错误消息
        return jsonify({
            'plugins': plugins_data,
            'error': '插件管理器不可用',
            'success': False
        })
    
    try:
        # 获取所有插件信息
        try:
            # 尝试使用新版参数
            all_plugins = g.plugin_manager.get_plugin_list(include_unloaded=True)
        except (TypeError, Exception) as e:
            # 如果参数错误，尝试不带参数调用
            try:
                # 先获取已加载的插件
                all_plugins = g.plugin_manager.get_plugin_list()
                
                # 然后尝试获取禁用的插件列表并手动添加
                try:
                    disabled_plugins = g.plugin_manager.get_disabled_plugins()
                    
                    # 过滤和去重禁用的插件列表
                    filtered_disabled_plugins = []
                    for plugin_name in disabled_plugins:
                        # 跳过包含点号的插件名（可能是类名）
                        if '.' in plugin_name:
                            continue
                        
                        # 跳过空字符串或None
                        if not plugin_name:
                            continue
                            
                        # 添加到过滤后的列表（避免重复）
                        if plugin_name not in filtered_disabled_plugins:
                            filtered_disabled_plugins.append(plugin_name)
                    
                    # 记录已添加插件名称
                    added_names = set(p.get("name") for p in all_plugins)
                    
                    # 添加禁用的插件（使用过滤后的列表）
                    for plugin_name in filtered_disabled_plugins:
                        if plugin_name not in added_names:
                            # 尝试查找这个插件的真实name属性
                            real_name = plugin_name
                            # 查找所有插件文件夹，尝试获取这个插件的真实name属性
                            for folder in os.listdir("Plugins"):
                                folder_path = os.path.join("Plugins", folder)
                                if not os.path.isdir(folder_path) or folder.startswith('_') or folder.startswith('.') or folder == "core":
                                    continue
                                
                                # 如果文件夹名与插件名匹配
                                if folder.lower() == plugin_name.lower():
                                    # 尝试从配置或主文件中获取真实name
                                    main_file = os.path.join(folder_path, "main.py")
                                    if os.path.exists(main_file):
                                        try:
                                            # 从主文件中获取插件名
                                            with open(main_file, 'r', encoding='utf-8') as f:
                                                content = f.read()
                                                # 简单解析
                                                name_match = re.search(r'name\s*=\s*["\']([^"\']+)["\']', content)
                                                if name_match:
                                                    real_name = name_match.group(1)
                                        except Exception:
                                            pass
                            
                            # 添加禁用的插件
                            all_plugins.append({
                                "name": real_name,
                                "description": "插件已禁用",
                                "version": "未知",
                                "author": "未知",
                                "status": "disabled",
                                "loaded": False,
                                "folder": plugin_name
                            })
                            added_names.add(real_name)
                except Exception:
                    pass
                
                # 尝试获取未加载但已启用的插件
                try:
                    # 直接访问插件管理器的unloaded_plugins属性
                    if hasattr(g.plugin_manager, 'unloaded_plugins'):
                        unloaded_plugins = g.plugin_manager.unloaded_plugins
                        
                        # 过滤和去重卸载的插件列表
                        filtered_unloaded_plugins = []
                        for plugin_name in unloaded_plugins:
                            # 跳过包含点号的插件名（可能是类名）
                            if '.' in plugin_name:
                                continue
                            
                            # 跳过空字符串或None
                            if not plugin_name:
                                continue
                                
                            # 添加到过滤后的列表（避免重复）
                            if plugin_name not in filtered_unloaded_plugins:
                                filtered_unloaded_plugins.append(plugin_name)
                        
                        # 更新已添加插件名称集合
                        added_names = set(p.get("name") for p in all_plugins)
                        
                        # 添加卸载的插件（使用过滤后的列表）
                        for plugin_name in filtered_unloaded_plugins:
                            if plugin_name not in added_names:
                                # 尝试查找这个插件的真实name属性
                                real_name = plugin_name
                                # 查找所有插件文件夹，尝试获取这个插件的真实name属性
                                for folder in os.listdir("Plugins"):
                                    folder_path = os.path.join("Plugins", folder)
                                    if not os.path.isdir(folder_path) or folder.startswith('_') or folder.startswith('.') or folder == "core":
                                        continue
                                    
                                    # 如果文件夹名与插件名匹配
                                    if folder.lower() == plugin_name.lower():
                                        # 尝试从配置或主文件中获取真实name
                                        main_file = os.path.join(folder_path, "main.py")
                                        if os.path.exists(main_file):
                                            try:
                                                # 从主文件中获取插件名
                                                with open(main_file, 'r', encoding='utf-8') as f:
                                                    content = f.read()
                                                    # 简单解析
                                                    name_match = re.search(r'name\s*=\s*["\']([^"\']+)["\']', content)
                                                    if name_match:
                                                        real_name = name_match.group(1)
                                            except Exception:
                                                pass
                                
                                # 检查是否已禁用
                                is_disabled = False
                                try:
                                    if hasattr(g.plugin_manager, 'disabled_plugins'):
                                        is_disabled = plugin_name in g.plugin_manager.disabled_plugins
                                except:
                                    pass
                                
                                if not is_disabled:
                                    # 添加未加载但已启用的插件
                                    all_plugins.append({
                                        "name": real_name,
                                        "description": "插件已启用但未加载",
                                        "version": "未知",
                                        "author": "未知",
                                        "status": "enabled",  # 状态为已启用
                                        "loaded": False,  # 但未加载
                                        "folder": plugin_name
                                    })
                                    added_names.add(real_name)
                except Exception:
                    pass
            except Exception:
                all_plugins = []
        
        # 确保每个插件对象都有loaded属性
        for plugin in all_plugins:
            # 如果loaded字段不存在，则根据status设置
            if 'loaded' not in plugin:
                status = plugin.get('status', '').lower()
                # 检查插件是否在插件管理器的已加载插件列表中
                plugin_name = plugin.get('name')
                is_loaded = False
                if plugin_name:
                    is_loaded = g.plugin_manager.get_plugin(plugin_name) is not None
                
                # 如果插件已经被确认加载，或者状态为"已加载"，则标记为已加载
                if is_loaded or status in ['已加载', 'loaded']:
                    plugin['loaded'] = True
                # 否则，如果状态为enabled/已启用，默认认为插件已加载，除非明确标记为未加载
                elif status in ['enabled', '已启用']:
                    plugin['loaded'] = True
                else:
                    plugin['loaded'] = False
        
        # 添加到结果列表并处理每个插件的状态
        for plugin in all_plugins:
            try:
                # 原始状态
                raw_status = plugin.get('status')
                # 中文状态特殊处理
                if raw_status in ['已加载', '已启用', '加载', '启用']:
                    plugin_status = 'enabled'
                elif raw_status == 'enable':  # 特别处理"enable"到"enabled"的转换
                    plugin_status = 'enabled'
                else:
                    plugin_status = normalize_plugin_status(raw_status)
                
                # 处理加载状态
                is_loaded = plugin.get('loaded')
                
                # 如果未明确指定loaded状态，且状态为enabled，则默认认为已加载
                if is_loaded is None and plugin_status == 'enabled':
                    is_loaded = True
                    
                plugins_data.append({
                    'name': plugin.get('name', '未知'),
                    'version': plugin.get('version', '未知'),
                    'author': plugin.get('author', '未知'),
                    'description': plugin.get('description', '无描述'),
                    'status': plugin_status,
                    'loaded': is_loaded,
                })
            except Exception:
                pass
        
        # 返回处理结果
        return jsonify({
            'plugins': plugins_data,
            'success': True
        })
    except Exception as e:
        logger.error(f"获取插件列表时出错: {e}")
        
        # 提供模拟插件数据作为回退选项
        mock_plugins = [
            {
                'name': 'HelloWorld',
                'version': '1.0.0',
                'author': 'NGC660',
                'description': '一个简单的Hello World插件示例',
                'status': 'enabled',  # 标准化状态
                'loaded': True,  # 已加载
                'folder': 'HelloWorld',
                'scheduled_tasks': [],
                'event_handlers': []
            },
            {
                'name': 'WeatherPlugin',
                'version': '1.1.2',
                'author': 'NGC660',
                'description': '天气查询插件',
                'status': 'enabled',  # 标准化状态
                'loaded': True,  # 已加载
                'folder': 'WeatherPlugin',
                'scheduled_tasks': [],
                'event_handlers': []
            },
            {
                'name': 'NewsPlugin',
                'version': '0.9.5',
                'author': 'NGC660',
                'description': '新闻订阅插件',
                'status': 'disabled',  # 标准化状态
                'loaded': False,  # 未加载
                'folder': 'NewsPlugin',
                'scheduled_tasks': [],
                'event_handlers': []
            }
        ]
        
        return jsonify({
            'plugins': mock_plugins,
            'error': str(e),
            'is_mock': True
        })


@bp.route('/enable/<plugin_name>', methods=['POST'])
@login_required
def enable_plugin(plugin_name):
    """启用插件"""
    if not current_user.isAdmin():
        return jsonify({
            'success': False,
            'message': '权限不足，需要管理员权限'
        }), 403
    
    # 检查插件管理器是否可用
    if not hasattr(g, 'plugin_manager') or g.plugin_manager is None:
        return jsonify({
            'success': False,
            'message': '插件管理器不可用，无法启用插件'
        }), 500
    
    try:
        success = g.plugin_manager.enable_plugin(plugin_name)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'插件 {plugin_name} 已成功启用'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'无法启用插件 {plugin_name}'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'启用插件 {plugin_name} 时出错: {str(e)}'
        }), 500


@bp.route('/disable/<plugin_name>', methods=['POST'])
@login_required
def disable_plugin(plugin_name):
    """禁用插件"""
    if not current_user.isAdmin():
        return jsonify({
            'success': False,
            'message': '权限不足，需要管理员权限'
        }), 403
    
    # 检查插件管理器是否可用
    if not hasattr(g, 'plugin_manager') or g.plugin_manager is None:
        return jsonify({
            'success': False,
            'message': '插件管理器不可用，无法禁用插件'
        }), 500
    
    try:
        success = g.plugin_manager.disable_plugin(plugin_name)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'插件 {plugin_name} 已成功禁用'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'无法禁用插件 {plugin_name}'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'禁用插件 {plugin_name} 时出错: {str(e)}'
        }), 500


@bp.route('/reload/<plugin_name>', methods=['POST'])
@login_required
def reload_plugin(plugin_name):
    """重新加载插件"""
    if not current_user.isAdmin():
        return jsonify({
            'success': False,
            'message': '权限不足，需要管理员权限'
        }), 403
    
    # 检查插件管理器是否可用
    if not hasattr(g, 'plugin_manager') or g.plugin_manager is None:
        return jsonify({
            'success': False,
            'message': '插件管理器不可用，无法重载插件'
        }), 500
    
    try:
        success = g.plugin_manager.reload_plugin(plugin_name)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'插件 {plugin_name} 已成功重载'
            })
        else:
            # 尝试重载已卸载的插件
            try:
                success = g.plugin_manager.reload_unloaded_plugin(plugin_name)
                
                if success:
                    return jsonify({
                        'success': True,
                        'message': f'已卸载的插件 {plugin_name} 已成功重载'
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': f'无法重载插件 {plugin_name}'
                    })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'重载已卸载插件 {plugin_name} 时出错: {str(e)}'
                }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'重载插件 {plugin_name} 时出错: {str(e)}'
        }), 500


@bp.route('/api/detail/<plugin_name>')
@login_required
def get_plugin_detail(plugin_name):
    """获取单个插件的详细信息"""
    # 检查插件管理器是否可用
    if not hasattr(g, 'plugin_manager') or g.plugin_manager is None:
        return jsonify({
            'success': False,
            'error': '插件管理器不可用'
        })
    
    def get_tasks_and_handlers():
        # 获取定时任务
        scheduled_tasks = []
        try:
            # 获取所有定时任务
            all_tasks = g.plugin_manager.get_scheduled_tasks()
            
            # 筛选出当前插件的任务
            for task in all_tasks:
                if task.get('plugin_name') == plugin_name:
                    scheduled_tasks.append({
                        'name': task.get('name', '未命名任务'),
                        'interval': task.get('interval'),
                        'at_time': task.get('at_time'),
                        'day_of_week': task.get('day_of_week')
                    })
            
            # 如果没有找到任务，尝试直接查询插件实例
            if not scheduled_tasks:
                plugin_instance = g.plugin_manager.get_plugin(plugin_name)
                if plugin_instance:
                    # 查找所有带定时任务装饰器的方法
                    import inspect
                    for name, method in inspect.getmembers(plugin_instance, inspect.ismethod):
                        if hasattr(method, "_scheduled_task"):
                            task_params = method._scheduled_task
                            task_data = {
                                'name': name,
                                'interval': task_params.get('interval'),
                                'at_time': task_params.get('at_time'),
                                'day_of_week': task_params.get('day_of_week')
                            }
                            scheduled_tasks.append(task_data)
                            
        except Exception:
            pass
        
        # 获取事件处理器
        event_handlers = []
        try:
            # 获取所有事件处理器信息
            all_handlers = g.plugin_manager.get_handlers_priority_info()
            
            # 按事件类型整理处理器信息
            handler_entries = []
            
            # 遍历所有事件类型的处理器组
            for event_group in all_handlers:
                event_type = event_group.get('event_type')
                event_handlers_list = event_group.get('handlers', [])
                
                # 从Plugins.core.event_types获取事件名称
                from Plugins.core.event_types import EventType
                event_name = EventType.get_name(event_type) if event_type is not None else "未知事件"
                
                # 筛选当前插件的处理器
                for handler_info in event_handlers_list:
                    if handler_info.get('plugin_name') == plugin_name:
                        handler_entries.append({
                            'event_type': event_type,
                            'event_name': event_name,
                            'handler_name': handler_info.get('handler_name', '未命名处理器'),
                            'priority': handler_info.get('priority', 0)
                        })
            
            # 如果找到了处理器，添加到结果中
            if handler_entries:
                event_handlers = handler_entries
                
        except Exception:
            pass
        
        return scheduled_tasks, event_handlers
    
    try:
        # 获取所有插件信息
        try:
            # 尝试使用新版参数
            all_plugins = g.plugin_manager.get_plugin_list(include_unloaded=True)
        except (TypeError, Exception):
            # 如果参数错误，尝试不带参数调用
            all_plugins = g.plugin_manager.get_plugin_list()
        
        # 查找目标插件
        target_plugin = None
        for plugin in all_plugins:
            if plugin['name'] == plugin_name:
                target_plugin = plugin
                break
        
        if not target_plugin:
            return jsonify({
                'success': False,
                'error': f'未找到插件: {plugin_name}'
            })
        
        # 获取定时任务和事件处理器
        scheduled_tasks, event_handlers = get_tasks_and_handlers()
        
        # 检查并标准化插件状态
        is_enabled = False
        if 'enabled' in target_plugin:
            is_enabled = target_plugin['enabled']
        else:
            # 使用状态字段推断是否启用
            status = target_plugin.get('status', '').lower()
            is_enabled = status in ['enabled', 'enable', '已加载', '已启用', 'loaded']
        
        # 确定加载状态
        # 首先检查plugin中是否有明确的loaded字段
        is_loaded = target_plugin.get('loaded')
        
        # 如果没有明确的loaded字段，则检查是否有处理器或任务
        if is_loaded is None:
            # 检查插件是否真的已加载到插件管理器中
            plugin_instance = g.plugin_manager.get_plugin(plugin_name)
            is_loaded = plugin_instance is not None
        
            # 如果还是无法确定，则检查是否有事件处理器或定时任务
            if is_loaded is None and (event_handlers or scheduled_tasks):
                is_loaded = True
        
            # 最后，对于已启用的插件，默认认为是已加载的，除非明确标记为未加载
            if is_loaded is None and is_enabled:
                is_loaded = True
        
        # 返回插件详情
        return jsonify({
            'success': True,
            'plugin': {
                'name': target_plugin.get('name', ''),
                'description': target_plugin.get('description', ''),
                'version': target_plugin.get('version', ''),
                'author': target_plugin.get('author', ''),
                'enabled': is_enabled,
                'loaded': is_loaded,
                'scheduled_tasks': scheduled_tasks,
                'event_handlers': event_handlers
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取插件详情时出错: {str(e)}'
        }) 
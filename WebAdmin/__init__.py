from flask import Flask, g
from flask_login import LoginManager
import Config.ConfigServer as Cs
from WebAdmin.models import User
from loguru import logger
import os

# 创建插件和API的引用（这些将在创建应用时设置）
plugin_manager = None
ngc_bot_api = None


def create_app():
    """
    创建并配置Flask应用
    :return: Flask应用实例
    """
    global plugin_manager, ngc_bot_api
    
    # 创建Flask应用
    app = Flask(__name__, 
                static_folder='static',
                template_folder='templates')
    
    # 从配置文件读取Web配置
    config_data = Cs.returnConfigData()
    web_config = config_data.get('WebAdminConfig', {})
    
    # 设置应用配置
    app.config['SECRET_KEY'] = web_config.get('secretKey', 'homebot-web-admin-secret-key')
    app.config['USERS'] = web_config.get('users', [])
    app.config['PERMANENT_SESSION_LIFETIME'] = 3600  # 会话有效期1小时
    app.config['SESSION_COOKIE_SECURE'] = False  # 如果使用HTTPS则设为True
    app.config['SESSION_COOKIE_HTTPONLY'] = True  # 防止XSS攻击
    app.config['SESSION_TYPE'] = 'filesystem'  # 使用文件系统存储会话
    app.config['SESSION_FILE_DIR'] = os.path.join(os.path.dirname(__file__), 'sessions')  # 会话文件目录
    app.config['SESSION_USE_SIGNER'] = True  # 对cookie进行签名
    
    # 确保会话目录存在
    if not os.path.exists(app.config['SESSION_FILE_DIR']):
        os.makedirs(app.config['SESSION_FILE_DIR'])
    
    # 初始化登录管理器
    login_manager = LoginManager()
    login_manager.login_view = 'auth.login'  # 设置登录视图
    login_manager.login_message = '请先登录'  # 设置登录提示消息
    login_manager.login_message_category = 'warning'  # 设置提示消息的类别
    login_manager.init_app(app)
    
    # 用户加载函数
    @login_manager.user_loader
    def load_user(username):
        # 从配置文件中的用户列表查找用户
        for user_data in app.config['USERS']:
            if user_data['username'] == username:
                return User(user_data['username'], user_data['password'], user_data['role'])
        return None
    
    # 注册蓝图
    from WebAdmin import auth, dashboard, plugins
    app.register_blueprint(auth.bp)
    app.register_blueprint(dashboard.bp)
    app.register_blueprint(plugins.bp)
    
    # 设置默认路由为仪表盘
    @app.route('/')
    def index():
        return dashboard.index()
    
    # 在每个请求之前设置全局变量
    @app.before_request
    def before_request():
        try:
            # 导入PluginManager和NGCBotApi
            
            # 设置默认值，确保g总是有这些属性
            g.plugin_manager = None
            g.ngc_bot_api = None
            
            try:
                # 导入正确路径的PluginManager
                from Plugins.PluginManager.main import PluginManager
                
                # 导入NGCBotApi
                from NGCBotApi import NGCBotApi
                
                # 初始化PluginManager
                global plugin_manager
                if plugin_manager is None:
                    try:
                        # 尝试从主模块导入现有实例
                        import Plugins
                        if hasattr(Plugins, 'pluginManager'):
                            plugin_manager = Plugins.pluginManager
                        else:
                            # 如果没有导入成功，创建一个新的实例
                            plugin_manager = PluginManager()
                    except ImportError as e:
                        logger.error(f"导入PluginManager失败: {e}")
                        # 如果没有导入成功，创建一个新的实例
                        try:
                            plugin_manager = PluginManager()
                        except Exception as e2:
                            logger.error(f"创建PluginManager实例失败: {e2}")
                            plugin_manager = None
                
                # 初始化NGCBotApi
                global ngc_bot_api
                if ngc_bot_api is None:
                    try:
                        # 尝试创建新的NGCBotApi实例
                        ngc_bot_api = NGCBotApi()
                    except Exception as e:
                        logger.error(f"创建NGCBotApi实例失败: {e}")
                        ngc_bot_api = None
                
                # 设置全局变量
                g.plugin_manager = plugin_manager
                g.ngc_bot_api = ngc_bot_api
            
            except ImportError as e:
                logger.error(f"导入模块失败: {e}")
            
        except Exception as e:
            logger.error(f"在请求处理前设置全局变量时出错: {e}")
            # 确保即使出错也不影响后续处理
    
    # 注册错误处理器
    @app.errorhandler(404)
    def page_not_found(e):
        return dashboard.error_404()
    
    @app.errorhandler(500)
    def internal_server_error(e):
        return dashboard.error_500()
    
    # 添加线程安全设置
    app.config['PRESERVE_CONTEXT_ON_EXCEPTION'] = False
    
    return app 
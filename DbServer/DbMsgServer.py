from DbServer.DbDomServer import *
import Config.ConfigServer as Cs


class DbMsgServer:
    """
    数据库操作类，用于插入、删除和查询MSG表数据
    """

    def __init__(self):
        self.msgDb = Cs.returnMsgDbPath()

    def insertMsg(self, message: dict):
        """
        插入消息数据
        :param message 原始消息
        :return: 是否成功
        """
        msgData = message.get('data')
        msgId = msgData.get('messageId')
        msgType = msgData.get('messageType')
        content = msgData.get('message')
        wxId = msgData.get('fromWxId')
        nickName = msgData.get('fromNickName')
        finalFromWxId = msgData.get('finalFromWxId')
        finalFromNickName = msgData.get('finalFromNickName')
        robotId = msgData.get('robotId')
        if wxId=='34486331727@chatroom':
            return True
        try:
            conn, cursor = openDb(self.msgDb)
            cursor.execute(
                "INSERT INTO MSG (msgId, msgType, message, wxId, nickName, finalFromWxId, finalFromNickName, robotId) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                (msgId, msgType, content, wxId, nickName, finalFromWxId, finalFromNickName, robotId)
            )
            conn.commit()
            closeDb(conn, cursor)
            return True
        except Exception as e:
            logger.error(f'插入消息数据失败，错误信息: {e}')
            return False

    def queryByRoomIdAndWxId(self,wxId,roomId, limit=100):
        """
        查询用户在群里说的最近N条消息
        :param wxId: 用户微信ID
        :param roomId: 群ID
        :param limit: 限制条数
        :return: 消息记录列表
        """
        try:
            conn, cursor = openDb(self.msgDb)
            cursor.execute(
                "SELECT * FROM MSG WHERE wxId = ? AND finalFromWxId = ? ORDER BY createTime DESC LIMIT ?",
                (roomId, wxId, limit)
            )
            results = cursor.fetchall()
            closeDb(conn, cursor)
            return results
        except Exception as e:
            logger.error(f'查询用户在群里说的最近N条消息失败，错误信息: {e}')
            return []

    def getGroupLastContents(self,roomId,limit=10):
        """
        获取群最后N条聊天记录
        :param robotId:
        :param roomId:
        :param limit:
        :return:
        """
        try:
            conn, cursor = openDb(self.msgDb)
            cursor.execute(
                "SELECT * FROM MSG WHERE wxId = ? ORDER BY createTime DESC LIMIT ?",
                (roomId, limit)
            )
            results = cursor.fetchall()
            closeDb(conn, cursor)
            return results
        except Exception as e:
            logger.error(f'获取群最后N条聊天记录失败，错误信息: {e}')
            return []

    def queryByRoomId(self,roomId,days=1):
        """
        查询群几天内的消息列表（按自然天计算）
        :param roomId: 群ID
        :param days: 天数，默认1天（1=今天，2=今天+昨天，以此类推）
        :return: 消息记录列表
        """
        try:
            conn, cursor = openDb(self.msgDb)
            # 计算开始时间：days天前的0点
            # 例如：days=1时，查询今天0点到现在的消息
            # days=2时，查询昨天0点到现在的消息
            start_date = f"date('now', '-{days-1} day')"
            cursor.execute(
                f"SELECT * FROM MSG WHERE wxId = ? AND createTime >= {start_date} ORDER BY createTime DESC",
                (roomId,)
            )
            results = cursor.fetchall()
            closeDb(conn, cursor)
            return results
        except Exception as e:
            logger.error(f'查询群消息失败，错误信息: {e}')
            return []

    def deleteOldData(self, days=7):
        """
        删除指定天数之前的数据
        :param days: 天数，默认7天
        :return: 删除的记录数
        """
        try:
            conn, cursor = openDb(self.msgDb)
            cursor.execute(
                "DELETE FROM MSG WHERE createTime < datetime('now', ?)",
                (f'-{days} day',)
            )
            deleted_rows = cursor.rowcount
            conn.commit()
            closeDb(conn, cursor)
            logger.info(f'成功删除 {deleted_rows} 条 {days} 天前的记录')
            return deleted_rows
        except Exception as e:
            logger.error(f'删除旧数据失败，错误信息: {e}')
            return 0

    def queryMsgById(self, msgId):
        """
        根据消息ID查询消息
        :param msgId: 消息ID
        :return: 消息记录（元组）或None
        """
        try:
            conn, cursor = openDb(self.msgDb)
            cursor.execute("SELECT * FROM MSG WHERE msgId = ?", (msgId,))
            result = cursor.fetchone()
            closeDb(conn, cursor)
            return result
        except Exception as e:
            logger.error(f'查询消息失败，错误信息: {e}')
            return None

    def queryMsgByTimeRange(self, start_time=None, end_time=None):
        """
        根据时间范围查询消息
        :param start_time: 开始时间，格式：'YYYY-MM-DD HH:MM:SS'，默认为None
        :param end_time: 结束时间，格式：'YYYY-MM-DD HH:MM:SS'，默认为当前时间
        :return: 消息记录列表
        """
        try:
            conn, cursor = openDb(self.msgDb)
            query = "SELECT * FROM MSG WHERE 1=1"
            params = []

            if start_time:
                query += " AND createTime >= ?"
                params.append(start_time)

            if end_time:
                query += " AND createTime <= ?"
                params.append(end_time)
            else:
                query += " AND createTime <= datetime('now')"

            cursor.execute(query, params)
            results = cursor.fetchall()
            closeDb(conn, cursor)
            return results
        except Exception as e:
            logger.error(f'按时间范围查询消息失败，错误信息: {e}')
            return []


if __name__ == '__main__':
    # 测试代码
    dbo = DbMsgServer()
    # 插入测试
    # dbo.insertMsg(1001, 1, '测试消息', 'wxid_test1', '测试用户', 'wxid_from1', 'robot1')
    # 查询测试
    # msg = dbo.queryMsgById(1001)
    # print(msg)
    # 删除测试
    # dbo.deleteOldData(7)

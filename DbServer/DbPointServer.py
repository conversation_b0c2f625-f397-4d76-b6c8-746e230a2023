import DbServer.DbDomServer as Dds
import Config.ConfigServer as Cs
from loguru import logger


class DbPointServer:
    def __init__(self):
        self.pointDbPath = Cs.returnPointDbPath()

    def initFunctionPoint(self, roomId, wxId):
        """
        初始化用户功能积分
        :param roomId:
        :param wxId:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            cursor.execute('SELECT wxId FROM FunctionPoint WHERE wxId=? AND roomId=?', (wxId, roomId))
            result = cursor.fetchone()
            if result:
                return False
            cursor.execute('INSERT INTO FunctionPoint VALUES (?, ?, ?)', (roomId, wxId, 0))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f'初始化用户功能积分出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

    def addFunctionPoint(self, roomId, wxId, point):
        """
        增加功能积分
        :param roomId:
        :param wxId:
        :param point:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            cursor.execute(f'UPDATE FunctionPoint SET point=point+{int(point)} WHERE wxId=? AND roomId=?', (wxId, roomId))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f'增加功能积分出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

    def reduceFunctionPoint(self, roomId, wxId, point):
        """
        扣除功能积分
        :param roomId:
        :param wxId:
        :param point:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            cursor.execute(f'UPDATE FunctionPoint SET point=point-{int(point)} WHERE wxId=? AND roomId=?',
                           (wxId, roomId))
            Dds.closeDb(conn, cursor)
            return True
        except Exception as e:
            logger.error(f'扣除功能积分出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

    def queryFunctionPoint(self, roomId, wxId):
        """
        查询功能积分
        :param roomId:
        :param wxId:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            cursor.execute('SELECT point FROM FunctionPoint WHERE wxId=? AND roomId=?', (wxId, roomId))
            result = cursor.fetchone()
            if result:
                return result[0]
            else:
                return 0
        except Exception as e:
            logger.error(f'查询功能积分出现错误, 错误信息: {e}')
            return 0
        finally:
            Dds.closeDb(conn, cursor)

    def showFunctionPointTopTen(self, roomId):
        """
        查看功能积分排行榜
        :param roomId:
        :return:  [dict]
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            # 查询该群积分前十的用户（正序排列）
            cursor.execute(
                'SELECT wxId, point FROM FunctionPoint '
                'WHERE roomId=? '
                'ORDER BY point ASC '
                'LIMIT 10',
                (roomId,)
            )
            results = cursor.fetchall()
            return [{row[0]: row[1]} for row in results] if results else []
        except Exception as e:
            logger.error(f'查询积分排行榜出现错误, 错误信息：{e}')
            return []
        finally:
            Dds.closeDb(conn, cursor)

    def initGamePoint(self, roomId, wxId, gameType):
        """
        初始化用户游戏积分
        :param roomId:
        :param wxId:
        :param gameType:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            if self.queryGamePoint(roomId, wxId, gameType):
                return False
            cursor.execute('INSERT INTO GamePoint VALUES (?, ?, ?, ?)', (roomId, wxId, gameType, 0))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f'初始化用户游戏积分出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

    def addGamePoint(self, roomId, wxId, gameType, point):
        """
        增加游戏积分
        :param roomId:
        :param wxId:
        :param gameType:
        :param point:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            cursor.execute(f'UPDATE GamePoint SET point=point+{int(point)} WHERE wxId=? AND roomId=? AND gameType=?',
                           (wxId, roomId, gameType))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f'增加游戏积分出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

    def reduceGamePoint(self, roomId, wxId, gameType, point):
        """
        扣除游戏积分
        :param roomId:
        :param wxId:
        :param gameType:
        :param point:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            cursor.execute(f'UPDATE GamePoint SET point=point-{int(point)} WHERE wxId=? AND roomId=? AND gameType=?',
                           (wxId, roomId, gameType))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f'扣除游戏积分出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

    def queryGamePoint(self, roomId, wxId, gameType):
        """
        查询用户单款游戏积分
        :param roomId:
        :param wxId:
        :param gameType:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            cursor.execute('SELECT point FROM GamePoint WHERE wxId=? AND roomId=? AND gameType=?',
                           (wxId, roomId, gameType))
            result = cursor.fetchone()
            if result:
                return result[0]
            else:
                return False
        except Exception as e:
            logger.error(f'查询单款游戏积分出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

    def showOneGamePointTopTen(self, roomId, gameType):
        """
        查看单款游戏积分前十排行榜
        :param roomId:
        :param gameType:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            # 查询该群积分前十的用户（正序排列）
            cursor.execute(
                'SELECT wxId, point FROM GamePoint '
                'WHERE roomId=? AND gameType=?'
                'ORDER BY point ASC '
                'LIMIT 10',
                (roomId, gameType)
            )
            results = cursor.fetchall()
            return [{row[0]: row[1]} for row in results] if results else []
        except Exception as e:
            logger.error(f'查询单款游戏积分排行榜出现错误, 错误信息：{e}')
            return []
        finally:
            Dds.closeDb(conn, cursor)

    def showGamePointTopTen(self, roomId):
        """
        查看所有游戏积分前十排行榜
        :param roomId:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            # 查询该群积分前十的用户（正序排列）
            cursor.execute(
                'SELECT wxId, point FROM GamePoint '
                'WHERE roomId=?'
                'ORDER BY point ASC '
                'LIMIT 10',
                (roomId,)
            )
            results = cursor.fetchall()
            return [{row[0]: row[1]} for row in results] if results else []
        except Exception as e:
            logger.error(f'查询所有游戏积分排行榜出现错误, 错误信息：{e}')
            return []
        finally:
            Dds.closeDb(conn, cursor)

    def sign(self, roomId, wxId, signPoint):
        """
        签到
        :param roomId:
        :param wxId:
        :param signPoint:
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            cursor.execute('SELECT wxId FROM Sign WHERE roomId=? and wxId=?', (roomId,wxId))
            result = cursor.fetchone()
            if result:
                return False
            cursor.execute('INSERT INTO Sign VALUES (?, ?)', (roomId, wxId))
            conn.commit()
            self.addFunctionPoint(roomId=roomId, wxId=wxId, point=signPoint)
            return True
        except Exception as e:
            logger.error(f'签到出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

    def clearSign(self, ):
        """
        清空签到表
        :return:
        """
        conn, cursor = Dds.openDb(self.pointDbPath)
        try:
            cursor.execute('DELETE FROM Sign')
            conn.commit()
            return True
        except Exception as e:
            logger.error(f'清空签到表错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

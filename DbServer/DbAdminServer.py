import DbServer.DbDomServer as Dds
import Config.ConfigServer as Cs
from loguru import logger


class DbAdminServer:
    def __init__(self):
        self.adminDbPath = Cs.returnAdminDbPath()

    def addAdmin(self, roomId, wxId):
        """
        添加管理员
        :param roomId:
        :param wxId:
        :return:
        """
        conn, cursor = Dds.openDb(self.adminDbPath)
        try:
            cursor.execute('INSERT INTO Admins VALUES (?, ?)', (roomId, wxId))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f'添加管理员出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

    def delAdmin(self, roomId, wxId):
        """
        删除管理员
        :param roomId:
        :param wxId:
        :return:
        """
        conn, cursor = Dds.openDb(self.adminDbPath)
        try:
            cursor.execute('DELETE FROM Admins WHERE roomId=? AND wxId=?', (roomId, wxId))
            conn.commit()
            return True
        except Exception as e:
            logger.error(f'删除管理员出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)

    def queryAdmin(self, roomId, wxId):
        """
        查询管理员
        :param roomId:
        :param wxId:
        :return:
        """
        conn, cursor = Dds.openDb(self.adminDbPath)
        try:
            cursor.execute('SELECT wxId FROM Admins WHERE wxId=? AND roomId=?', (wxId, roomId))
            result = cursor.fetchone()
            if result:
                return True
            return False
        except Exception as e:
            logger.error(f'查询管理员出现错误, 错误信息: {e}')
            return False
        finally:
            Dds.closeDb(conn, cursor)


if __name__ == '__main__':
    Das = DbAdminServer()
    # print(Das.queryAdmin('1', '1'))
    print(Das.delAdmin('1', '1'))
from DbServer.DbDomServer import *
import Config.ConfigServer as Cs


class DbInitServer:
    def __init__(self):
        self.msgDb = Cs.returnMsgDbPath()
        self.pointDb = Cs.returnPointDbPath()
        self.adminDb = Cs.returnAdminDbPath()

    def _initDb(self, ):
        """
        初始化数据库
        :return:
        """
        msgConn, msgCursor = openDb(self.msgDb)
        createTable(msgCursor, 'MSG',
                    'msgId INT, msgType INT, message TEXT, wxId VARCHAR(255), nickName VARCHAR(255), finalFromWxId VARCHAR(255), finalFromNickName VARCHAR(255), robotId varchar(255), createTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
        closeDb(msgConn, msgCursor)

        pointConn, pointCursor = openDb(self.pointDb)
        createTable(pointCursor, 'FunctionPoint', 'roomId varchar(255), wxId varchar(255), point INT')
        createTable(pointCursor, 'GamePoint',
                    'roomId varchar(255), wxId varchar(255), gameType varchar(255), point INT')
        createTable(pointCursor, 'Sign', 'roomId varchar(255), wxId varchar(255)')
        closeDb(pointConn, pointCursor)

        adminConn, adminCursor = openDb(self.adminDb)
        createTable(adminCursor, 'Admins', 'roomId varchar(255), wxId varchar(255)')
        closeDb(adminConn, adminCursor)


if __name__ == '__main__':
    Ids = DbInitServer()
    Ids._initDb()

from FileCache.FileCacheServer import initCacheFolder
from BotServer.MessageHandle import app
from Plugins import pluginManager
import Config.ConfigServer as Cs
from NGCBotApi import NGCBotApi
from loguru import logger
from io import BytesIO
from PIL import Image
import subprocess
import requests
import logging
import uvicorn
import base64
import signal
import atexit
import psutil
import locale
import time
import os
import sys



class MainServer:
    def __init__(self):
        configData = Cs.returnConfigData()
        StarBotConfig = configData['StarBotConfig']
        self.bot = NGCBotApi()
        self._silence_all_uvicorn_logs()
        self.config = uvicorn.Config(
            app=app,
            host=StarBotConfig['cbHost'],
            port=int(StarBotConfig['cbPort']),
            lifespan="on",
            access_log=False,
            log_level="critical"
        )
        self.ngcBotApiProcess = None
        self.ngcBotApiPid = None

        # 注册退出处理函数，确保程序退出时关闭NGCBotApi
        atexit.register(self.stopNgcBotApi)

        # 注册信号处理，捕获终止信号
        self._registerSignalHandlers()

    def initServer(self):
        # 启动NGCBotApi
        if not self.runNGCBotApi():
            logger.error("NGCBotApi启动失败，程序无法继续执行")
            return

        loginBool = False
        for i in range(2):
            loginBool = self.isLogin()
            if loginBool:
                break
        if not loginBool:
            logger.error(f'没有微信处于在线状态, 程序退出！')
            self.stopNgcBotApi()  # 确保在退出前关闭NGCBotApi
            return
        # 初始化缓存文件夹
        initCacheFolder()
        # 输出插件系统状态
        for plugin_key, plugin in pluginManager.plugins.items():
            logger.info(f"插件: {plugin.name} (版本: {plugin.version}, 作者: {plugin.author})")
            logger.info(f"描述: {plugin.description}")

        # 启动定时任务调度器
        pluginManager.start_scheduler()

        try:
            self.handMessage()
        finally:
            # 确保程序退出时关闭NGCBotApi
            self.stopNgcBotApi()

    def runNGCBotApi(self):
        """
        启动NGCBotApi
        :return: 是否成功启动
        """
        try:
            # 获取Core目录路径
            corePath = self.getCorePath()
            exePath = os.path.join(corePath, "NGCBotApi.exe")

            if not os.path.exists(exePath):
                logger.error(f"找不到NGCBotApi.exe文件: {exePath}")
                return False

            # 启动进程
            logger.info(f"正在启动NGCBotApi.exe: {exePath}")

            # 在Windows下创建一个不继承父进程的进程
            startupinfo = None
            if sys.platform == 'win32':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

            self.ngcBotApiProcess = subprocess.Popen(
                exePath,
                cwd=corePath,
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )

            # 保存PID
            self.ngcBotApiPid = self.ngcBotApiProcess.pid
            logger.info(f"NGCBotApi进程已启动，PID: {self.ngcBotApiPid}")

            # 检测是否启动成功
            if self.checkNgcBotApiRunning():
                logger.success("NGCBotApi 启动成功")
                return True
            else:
                logger.error("NGCBotApi 启动失败, 请手动启动！")
                self.stopNgcBotApi()
                return False
        except Exception as e:
            logger.error(f"启动NGCBotApi时出错: {e}")
            return False

    def checkNgcBotApiRunning(self, maxAttempts=5):
        """
        检查NGCBotApi是否成功启动
        :param maxAttempts: 最大尝试次数
        :return: 是否启动成功
        """
        if not self.ngcBotApiProcess:
            return False

        # 使用ConfigServer获取配置
        configData = Cs.returnConfigData()
        ngcBotConfig = configData['NGCBotConfig']
        apiHost = ngcBotConfig.get('NGCBotApi', '127.0.0.1')
        apiPort = ngcBotConfig.get('NGCBotPort', '7777')
        apiKey = ngcBotConfig.get('NGCBotKey', '123123')

        # 尝试访问API检查是否启动成功
        for attempt in range(maxAttempts):
            try:
                url = f"http://{apiHost}:{apiPort}/getRobotList"
                headers = {"NGCBotKey": apiKey}
                response = requests.get(url, headers=headers, json={"status": 1}, timeout=2)
                if response.status_code == 200:
                    return True
                logger.warning(f"NGCBotApi 响应状态码: {response.status_code}, 第{attempt + 1}次尝试")
            except Exception as e:
                logger.warning(f"无法连接到NGCBotApi (尝试 {attempt + 1}/{maxAttempts}): {e}")

            # 检查进程是否已退出
            if self.ngcBotApiProcess.poll() is not None:
                logger.error(f"NGCBotApi 进程已退出, 状态码: {self.ngcBotApiProcess.returncode}")
                return False

            time.sleep(1)  # 等待1秒再次尝试

        return False

    def stopNgcBotApi(self):
        """
        停止NGCBotApi进程
        """
        if self.ngcBotApiProcess is None and self.ngcBotApiPid is None:
            return

        logger.info("正在停止NGCBotApi进程...")

        # 保存PID以防进程对象失效
        pid_to_kill = self.ngcBotApiPid if self.ngcBotApiPid else (
            self.ngcBotApiProcess.pid if self.ngcBotApiProcess else None
        )

        if pid_to_kill is None:
            logger.warning("没有找到NGCBotApi进程的PID")
            return

        try:
            # Windows系统特定的进程终止方法
            # 1. 尝试使用taskkill命令
            try:
                logger.info(f"正在使用taskkill命令终止进程 {pid_to_kill}")
                # 获取系统编码
                system_encoding = locale.getpreferredencoding()
                # 运行命令并捕获输出
                result = subprocess.run(
                    ['taskkill', '/F', '/PID', str(pid_to_kill)],
                    check=False,
                    timeout=3,
                    capture_output=True,
                    encoding=system_encoding
                )
                # 如果命令执行成功，记录输出
                if result.returncode == 0:
                    logger.info(f"成功终止进程 {pid_to_kill}")
                else:
                    logger.warning(f"无法终止进程 {pid_to_kill}, 返回码: {result.returncode}")
            except Exception as e:
                logger.warning(f"使用taskkill终止进程失败: {e}")

            # 2. 尝试使用psutil终止进程
            try:
                if psutil.pid_exists(pid_to_kill):
                    logger.info(f"尝试使用psutil终止进程 {pid_to_kill}")
                    process = psutil.Process(pid_to_kill)
                    process.terminate()

                    # 等待进程终止
                    gone, alive = psutil.wait_procs([process], timeout=3)
                    if process in alive:
                        logger.warning("进程没有响应terminate()，尝试kill()")
                        process.kill()
            except Exception as e:
                logger.warning(f"使用psutil终止进程失败: {e}")

            # 3. 终止所有相关子进程
            try:
                # 查找所有NGCBotApi.exe进程
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if 'NGCBotApi' in proc.info['name']:
                            proc_pid = proc.info['pid']
                            if proc_pid != pid_to_kill:  # 避免重复终止相同进程
                                logger.info(f"发现NGCBotApi相关进程: {proc_pid}")
                                # 运行命令并捕获输出
                                result = subprocess.run(
                                    ['taskkill', '/F', '/PID', str(proc_pid)],
                                    check=False,
                                    timeout=2,
                                    capture_output=True,
                                    encoding=system_encoding
                                )
                                if result.returncode == 0:
                                    logger.info(f"成功终止相关进程 {proc_pid}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        pass
            except Exception as e:
                logger.warning(f"终止相关进程失败: {e}")

            # 检查进程是否仍在运行
            try:
                if self.ngcBotApiProcess and self.ngcBotApiProcess.poll() is None:
                    logger.warning("NGCBotApi进程仍在运行，尝试强制终止")
                    self.ngcBotApiProcess.kill()
            except Exception as e:
                logger.warning(f"强制终止进程失败: {e}")

            # 最后检查
            try:
                if psutil.pid_exists(pid_to_kill):
                    logger.warning(f"NGCBotApi进程 (PID: {pid_to_kill}) 仍在运行")
                    try:
                        # 使用os.system会直接输出到控制台，可能导致乱码
                        # 改用subprocess并捕获输出
                        subprocess.run(
                            ['taskkill', '/F', '/PID', str(pid_to_kill)],
                            check=False,
                            timeout=2,
                            capture_output=True,
                            encoding=system_encoding
                        )
                    except:
                        pass
                else:
                    logger.info(f"NGCBotApi进程 (PID: {pid_to_kill}) 已成功终止")
            except:
                pass

        except Exception as e:
            logger.error(f"停止NGCBotApi进程时出错: {e}")
        finally:
            self.ngcBotApiProcess = None
            self.ngcBotApiPid = None
            logger.info("NGCBotApi进程引用已清除")

    def getCorePath(self):
        """
        获取Core目录的路径
        :return: Core目录的绝对路径
        """
        # 获取当前文件所在的目录
        currentDir = os.path.dirname(os.path.abspath(__file__))
        # 获取项目根目录 (假设BotServer是项目的子目录)
        projectRoot = os.path.dirname(currentDir)
        # Core目录路径
        corePath = os.path.join(projectRoot, "Core")

        return corePath

    def isLogin(self, ):
        """
        判断是否登录
        :return:
        """
        BotListData = self.bot.getRobotList(1)
        status = BotListData.get('status')
        loginStatus = False
        if status != 200:
            # 先登录
            logger.error(f'判断是否登录出现错误, 错误信息: {BotListData.get("message")}')
            return False
        BotList = BotListData.get('data')
        for botId, botInfoDict in BotList.items():
            botInfoMsg = f'\n昵称: {botInfoDict.get("nickName")}\n原始ID: {botId}\n微信号: {botInfoDict.get("wxNum")}\n手机号: {botInfoDict.get("phone")}\n在线状态: {botInfoDict.get("status")}\n ========= NGCBot-Star ========='
            if botInfoDict.get("status") == '在线':
                loginStatus = True
            logger.success(botInfoMsg)
        if loginStatus:
            return True
        # 没登录
        startWechatJsonData = self.bot.startWechat()
        if startWechatJsonData.get('status') == 200:
            instanceId = startWechatJsonData.get('data')
            time.sleep(2)  # 暂停2s 打开微信
            wechatQrCodeJsonData = self.bot.getLoginQrCode(instanceId)
            wechatQrCodeData = wechatQrCodeJsonData.get('data')
            wechatQrCode = wechatQrCodeData.get('qrCode')
            wechatQrBase64Data = wechatQrCodeData.get('qrCodePictureBase64')
            logger.warning(f'请先点击链接扫码或者扫描二维码登录！')
            qrUrl = f'https://api.qrserver.com/v1/create-qr-code/?data={wechatQrCode}'
            try:
                if "base64," in wechatQrBase64Data:
                    wechatQrBase64Data = wechatQrBase64Data.split("base64,")[1]
                imageData = base64.b64decode(wechatQrBase64Data)
                wxQrImage = Image.open(BytesIO(imageData)).convert('RGB')
                logger.warning(f'二维码链接: {qrUrl}')
                wxQrImage.show()
                # 等待30s扫码
                time.sleep(30)
                return False
            except Exception as e:
                time.sleep(20)
                logger.error(f'判断微信是否登录出现错误, 错误信息: {e}')
                return False

    def _silence_all_uvicorn_logs(self):
        """彻底禁用所有Uvicorn相关日志"""
        uvicorn_loggers = [
            "uvicorn",
            "uvicorn.logger.error",
            "uvicorn.access",
            "uvicorn.asgi",
            "uvicorn.server",
            "uvicorn.lifespan",
            "uvicorn.protocols.http",
            "uvicorn.protocols.websockets"
        ]

        for name in uvicorn_loggers:
            logger = logging.getLogger(name)
            logger.handlers = []
            logger.propagate = False
            logger.setLevel(logging.CRITICAL + 1)  # 设置高于最高级别

    def handMessage(self, ):
        server = uvicorn.Server(self.config)
        server.run()

    def _registerSignalHandlers(self):
        """
        注册信号处理函数，确保在程序接收到终止信号时关闭NGCBotApi
        """
        if sys.platform != 'win32':
            # Unix信号处理
            signal.signal(signal.SIGTERM, self._signalHandler)
            signal.signal(signal.SIGINT, self._signalHandler)
            signal.signal(signal.SIGHUP, self._signalHandler)
        else:
            # Windows信号处理
            signal.signal(signal.SIGTERM, self._signalHandler)
            signal.signal(signal.SIGINT, self._signalHandler)

    def _signalHandler(self, signum, frame):
        """
        信号处理函数
        """
        logger.warning(f"收到终止信号 {signum}，正在停止NGCBotApi...")
        self.stopNgcBotApi()
        sys.exit(0)


if __name__ == '__main__':
    Ms = MainServer()
    Ms.initServer()

from fastapi import FastAPI
from loguru import logger
from Plugins import plugin<PERSON>anager
from threading import Thread
from DbServer import DbServer
import logging

app = FastAPI(debug=False)
Ds = DbServer()
Ds._initDb()


# 禁用Uvicorn默认访问日志
@app.on_event("startup")
async def configure_logging():
    # 禁用访问日志处理器
    access_logger = logging.getLogger("uvicorn.access")
    access_logger.disabled = True

    # 设置Uvicorn错误日志级别为WARNING
    logging.getLogger("uvicorn.logger.error").setLevel(logging.WARNING)
    logging.getLogger("uvicorn").setLevel(logging.WARNING)

    # 加载所有插件
    pluginManager.load_plugins()


@app.post('/sync')
def messageHandle(message: dict):
    # print(message)
    try:
        # 确保message是一个有效的字典
        if not isinstance(message, dict):
            logger.error(f"接收到无效的消息格式: {message}")
            return
        if message.get('type') == '群成员退出事件':
            message = message.get('content')
        if not message.get('event'):
            return
        # 插入消息数据
        Thread(target=Ds.insertMsg, args=(message,)).start()
        event = handleEvent(message)
        description = message.get('description')
        msgData = message.get('data', {})
        messageType = msgData.get('messageType')
        robotId = msgData.get('robotId')

        # 构建日志消息
        logMsg = f'接收到消息: \n消息信号: {event}\n消息类别: {description}\n消息类型: {messageType}\n来源RobotId: {robotId}\n详细内容: {msgData}\n ======== ========'
        logger.info(logMsg)
        # 调用插件系统处理事件
        if event is not None:
            pluginManager.handle_event(event, message)
        else:
            logger.warning(f"消息中没有有效的事件类型: {message}")
    except Exception as e:
        logger.error(f"处理消息时出错: {e}")


def handleEvent(message: dict):
    """
    事件处理函数
    :param message:
    :return:
    """
    event = int(message.get('event'))
    msgData = message.get('data', {})
    msgType = int(msgData.get('messageType'))
    content = msgData.get('message')
    robotId = msgData.get('robotId')
    description = message.get('description')
    # 群聊事件 103 开头
    # 好友事件 102 开头
    if event == 10003:
        if msgType == 1:
            # 群聊文本@机器人事件
            if msgData.get('atWxIdList'):
                # 群聊@机器人事件
                if robotId in msgData.get('atWxIdList') and '@所有人' not in content:
                    event = 10311
                # 群聊@人事件 不包含机器人
                elif robotId not in msgData.get('atWxIdList') and '@所有人' not in content:
                    event = 10312
            else:
                # 群聊文本消息事件
                event = 10301
        elif msgType == 3:
            # 群聊图片消息事件
            event = 10304
        elif msgType == 43:
            # 群聊视频消息事件
            event = 10343
        elif msgType == 49 and description == '群聊小程序消息事件':
            # 群聊小程序消息事件
            event = 10349
        elif msgType == 49 and description == '群聊链接消息事件':
            # 群聊链接消息事件
            event = 10348
        elif msgType == 49 and 'appmsg appid' in content:
            # 群聊小程序事件
            event = 10348
        elif msgType == 49 and '<type>57</type>' in content:
            # 群聊消息引用事件
            event = 10357
        elif msgType == 49 and '群聊文件消息事件' in description:
            # 群聊文件事件
            event = 10347
        elif msgType == 42 and '群聊名片消息事件' in description:
            # 群聊名片事件
            event = 10342
        elif msgType == 10000 and '收到红包，请在手机上查看' in content:
            # 群聊红包事件
            event = 10300
        elif msgType == 10000 and '通过扫描' in content and '分享的二维码加入群聊' in content:
            event = 10309
        elif msgType == 10000 and '邀请' in content and '加入了群聊' in content:
            event = 10309
        elif msgType == 47 and '群聊表情消息事件' in description:
            # 群聊表情事件
            event = 10346
        else:
            event = 10303
    elif event == 10002:
        # 私聊事件处理
        if msgType == 1:
            # 私聊文本事件
            event = 10201
        elif msgType == 3:
            # 私聊图片事件
            event = 10203
        elif msgType == 43:
            # 私聊视频消息事件
            event = 10243
        elif msgType == 49 and description == '私聊小程序消息事件':
            # 私聊小程序消息事件
            event = 10249
        elif msgType == 49 and description == '私聊链接消息事件':
            # 私聊链接消息事件
            event = 10248
        elif msgType == 49 and '<type>57</type>' in content:
            # 私聊消息引用事件
            event = 10257
        elif msgType == 49 and '私聊文件消息事件' in description:
            # 私聊文件事件
            event = 10247
        elif msgType == 34 and '私聊语音消息事件' in description:
            # 私聊语音事件
            event = 10234
        elif msgType == 42 and '私聊名片消息事件' in description:
            # 私聊名片事件
            event = 10242
        elif msgType == 10000 and '收到红包，请在手机上查看' in content:
            # 私聊红包事件
            event = 10200
        elif msgType == 10000 and '你已添加了' in content and '现在可以开始聊天了' in content:
            # 好友通过事件
            event = 10211
        else:
            event = 10002
    elif event == 10005:
        if msgType == 49 and '转账事件' in description:
            event = 10005
    elif event == 10010:
        if '好友新增事件' in description:
            # 好友新增事件
            event = 10010
    elif event == 10007:
        if msgType == 37 and '收到好友申请事件' in description:
            # 收到好友申请事件
            event = 10007
    elif event == 10013:
        if not msgType:
            # 群成员退出事件
            event = 10013
    elif event == 10014:
        # 群创建事件
        event = 10014
    elif event == 10001:
        # 微信登录成功事件
        event = 10001
    elif event == 10009:
        # 微信注销成功事件
        event = 10009
    elif event == 10006:
        if msgType == 10002:
            # 消息撤回事件 群聊&好友
            event = 10006
    elif event == 10004:
        # 收到公众号消息事件
        event = 10004
    else:
        event = event
    return int(event)


# 可选：全局异常处理
@app.exception_handler(Exception)
def universal_exception_handler(request, exc):
    logger.error(f"未处理异常: {str(exc)}")

<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
-------------------------------------------------------------------------------------> 

- 当前项目是一个微信机器人的项目，项目功能完整

- 我们现在要开发的是一个插件功能,目录是Plugins/Mir2Game，所有的开发权限仅限于这个插件Plugins/Mir2Game目录下面，不要修改其他目录下的任何文件和代码

- 插件的技术开发实现可以参考其他的插件实现形式

- 游戏项目还没上线，所有的开发都不用考虑兼容以前的，包括也不需要考虑旧的数据兼容和数据迁移，功能上也不要考虑旧版本，不存在旧版本的定义，只要保持一套最新干净的代码

- 项目的背景是做一个游戏插件，游戏是一款基于社交聊天APP的文字RPG游戏。游戏玩家在充满江湖气息的游戏中体验升级、战斗、社交等丰富玩法；

- 游戏特色
   - 无界面交互
   - 纯文字命令式玩法
   - 基于社交APP群聊实现
   - 通过文字描述和emoji表情增强游戏体验
   - 部分场景使用ASCII字符绘制界面效果
   - 社交化设计
   - 群聊即游戏区服
   - 支持多人实时互动
   - 玩家之间可交易

- 核心玩法
   - 基础玩法
      - 通过文字命令进行游戏操作
      - 支持地图探索、打怪升级
      - 装备收集与强化
      - 技能学习与使用
      - 战斗系统

   - 三大职业相互克制
      - 支持PVE和PVP
      - 技能战斗
      - 装备属性加成

   - 经济系统
      - 金币：基础货币
      - 银两：高级货币（1银两=10000金币）
      - 支持交易系统

- 命令设定
  - 强命令："!" 开头
  - 操作词 + 操作对象
  - @人物 相关操作

- 命令模式
  - !游戏模式：进入游戏状态
  - !退出游戏模式：退出游戏状态
  - 游戏模式下无需使用"!"
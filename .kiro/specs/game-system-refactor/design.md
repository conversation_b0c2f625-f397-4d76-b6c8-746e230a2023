# 游戏系统重构设计文档

## 概述

本设计文档基于需求文档，详细规划游戏系统重构的架构设计、组件设计和实现方案。重构采用渐进式方式，在现有系统基础上进行优化和扩展。

## 架构设计

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    游戏主程序 (main.py)                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 配置管理层                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           GameConfigManager                             │ │
│  │  - 怪物配置 (monster_configs)                           │ │
│  │  - 物品配置 (item_configs)                              │ │
│  │  - 地图配置 (map_configs)                               │ │
│  │  - 掉落配置 (drop_configs)                              │ │
│  │  - 刷怪配置 (spawn_configs)                             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   核心系统层                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐   │
│  │MonsterSystem│  │ ItemSystem  │  │    MapSystem        │   │
│  │(优化后)     │  │(优化后)     │  │   (优化后)          │   │
│  └─────────────┘  └─────────────┘  └─────────────────────┘   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   游戏机制层                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐   │
│  │  MapSpawnSystem │  │  ExploreSystem  │  │BattleSystem │   │
│  │  (智能刷怪)     │  │  (探索发现)     │  │ (协同战斗)  │   │
│  └─────────────────┘  └─────────────────┘  └─────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           DropDistributionSystem                        │ │
│  │              (掉落分配)                                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   命令处理层                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              CommandRouter                              │ │
│  │  - MapCommandHandler (探索命令)                         │ │
│  │  - BattleCommandHandler (战斗命令)                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 数据流设计

```
启动阶段:
GameConfigManager 加载配置 → 各系统初始化 → 开始刷怪

运行阶段:
玩家命令 → CommandRouter → 相应Handler → 调用系统方法 → 返回结果

探索流程:
探索命令 → ExploreSystem → MapSpawnSystem → 返回发现的怪物

战斗流程:
攻击命令 → BattleSystem → 协同战斗处理 → 掉落分配 → 返回结果
```

## 组件设计

### 1. GameConfigManager (配置管理器)

#### 职责
- 启动时一次性加载所有配置到内存
- 提供统一的配置访问接口
- 支持配置热更新
- 自动生成衍生配置

#### 接口设计
```python
class GameConfigManager:
    def __init__(self):
        """初始化配置管理器"""
        
    def get_monster_config(self, monster_name: str) -> Optional[Monster]:
        """获取怪物配置"""
        
    def get_item_config(self, item_name: str) -> Optional[Item]:
        """获取物品配置"""
        
    def get_map_config(self, map_name: str) -> Optional[GameMap]:
        """获取地图配置"""
        
    def get_drop_config(self, monster_name: str) -> Optional[Dict]:
        """获取掉落配置"""
        
    def get_spawn_config(self, map_name: str) -> Optional[Dict]:
        """获取刷怪配置"""
        
    def reload_configs(self):
        """重新加载所有配置"""
```

#### 配置生成逻辑
```python
# 掉落配置生成
drop_configs[monster_name] = {
    'items': monster.drop_items,
    'experience': monster.experience_reward,
    'coins': monster.coin_reward
}

# 刷怪配置生成
spawn_configs[map_name] = {
    'monsters': map_obj.monster_types,
    'max_count': calculate_max_monsters(map_name, monster_types),
    'spawn_interval': calculate_spawn_interval(level_range),
    'level_range': map_obj.level_range,
    'is_safe_zone': map_obj.is_safe_zone
}
```

### 2. MapSpawnSystem (智能刷怪系统)

#### 职责
- 管理地图中的怪物实例
- 自动刷新怪物
- 维护怪物状态

#### 核心数据结构
```python
@dataclass
class SpawnedMonster:
    monster_id: str
    monster_name: str
    map_name: str
    level: int
    current_health: int
    max_health: int
    spawn_time: datetime
    is_alive: bool = True
    is_boss: bool = False
    attackers: List[str] = field(default_factory=list)
```

#### 刷怪算法
```python
def calculate_max_monsters(map_name: str, monster_types: List[str]) -> int:
    """计算地图最大怪物数量"""
    if not monster_types:
        return 0
    
    base_count = len(monster_types)
    
    if map_name == "武林主城":
        return 0  # 安全区
    elif "Boss" in map_name:
        return max(3, base_count)
    else:
        return max(4, base_count * 2)

def calculate_spawn_interval(level_range: tuple) -> int:
    """计算刷怪间隔"""
    min_level, max_level = level_range
    
    if max_level <= 10:
        return 180  # 3分钟
    elif max_level <= 30:
        return 300  # 5分钟
    else:
        return 420  # 7分钟
```

#### 自动刷新机制
```python
def start_spawn_thread(self):
    """启动刷怪线程"""
    def spawn_worker():
        while True:
            try:
                self.check_and_spawn_monsters()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"刷怪线程异常: {e}")
    
    spawn_thread = threading.Thread(target=spawn_worker, daemon=True)
    spawn_thread.start()
```

### 3. ExploreSystem (探索系统)

#### 职责
- 处理玩家探索命令
- 管理玩家发现记录
- 提供怪物状态信息

#### 核心逻辑
```python
class ExploreSystem:
    def __init__(self, map_spawn_system, config_manager):
        self.map_spawn_system = map_spawn_system
        self.config_manager = config_manager
        self.player_discoveries = {}  # {user_id: {map_name: set(monster_ids)}}
    
    def explore_map(self, character, map_name: str):
        """探索地图发现怪物"""
        # 获取地图中的所有存活怪物
        available_monsters = self.map_spawn_system.get_alive_monsters_in_map(map_name)
        
        if not available_monsters:
            return None, "这里很安静，没有发现任何怪物"
        
        # 获取玩家已发现的怪物
        discovered_ids = self.get_player_discoveries(character.user_id, map_name)
        
        # 找到未发现的怪物
        undiscovered = [m for m in available_monsters if m.monster_id not in discovered_ids]
        
        if not undiscovered:
            return None, "你已经发现了这个区域的所有怪物"
        
        # 随机发现一个怪物
        discovered_monster = random.choice(undiscovered)
        self.record_discovery(character.user_id, map_name, discovered_monster.monster_id)
        
        return discovered_monster, f"发现了 {discovered_monster.monster_name}！"
```

### 4. CooperativeBattleSystem (协同战斗系统)

#### 职责
- 管理多人攻击同一怪物
- 记录战斗参与者
- 同步怪物状态

#### 数据结构
```python
class CooperativeBattleSystem:
    def __init__(self, map_spawn_system):
        self.map_spawn_system = map_spawn_system
        self.battle_participants = {}  # {monster_id: [user_ids]}
        self.battle_start_time = {}    # {monster_id: datetime}
    
    def join_battle(self, character, monster_id: str):
        """加入战斗"""
        if monster_id not in self.battle_participants:
            self.battle_participants[monster_id] = []
            self.battle_start_time[monster_id] = datetime.now()
        
        if character.user_id not in self.battle_participants[monster_id]:
            self.battle_participants[monster_id].append(character.user_id)
    
    def leave_battle(self, character, monster_id: str):
        """离开战斗"""
        if monster_id in self.battle_participants:
            if character.user_id in self.battle_participants[monster_id]:
                self.battle_participants[monster_id].remove(character.user_id)
    
    def get_battle_participants(self, monster_id: str) -> List[str]:
        """获取战斗参与者"""
        return self.battle_participants.get(monster_id, [])
```

### 5. DropDistributionSystem (掉落分配系统)

#### 职责
- 计算怪物掉落
- 分配掉落物品
- 通知玩家获得奖励

#### 分配算法
```python
class DropDistributionSystem:
    def distribute_drops(self, monster_name: str, participants: List[str]):
        """分配掉落物品"""
        drop_config = self.config_manager.get_drop_config(monster_name)
        if not drop_config:
            return {}
        
        # 计算掉落物品
        dropped_items = self.calculate_drops(drop_config)
        
        if len(participants) == 1:
            # 单人获得所有掉落
            return {participants[0]: dropped_items}
        else:
            # 多人随机分配
            return self.distribute_randomly(dropped_items, participants)
    
    def distribute_randomly(self, items: List, participants: List[str]):
        """随机分配物品"""
        distribution = {participant: [] for participant in participants}
        
        for item_name, quantity in items:
            if item_name in ['experience', 'coins']:
                # 经验和金币平均分配
                avg_quantity = quantity // len(participants)
                remainder = quantity % len(participants)
                
                for i, participant in enumerate(participants):
                    participant_quantity = avg_quantity
                    if i < remainder:  # 余数随机分配给前几个玩家
                        participant_quantity += 1
                    
                    if participant_quantity > 0:
                        distribution[participant].append((item_name, participant_quantity))
            else:
                # 物品随机分配给一个玩家
                lucky_player = random.choice(participants)
                distribution[lucky_player].append((item_name, quantity))
        
        return distribution
```

## 数据模型设计

### 怪物实例模型
```python
@dataclass
class SpawnedMonster:
    monster_id: str          # 唯一ID
    monster_name: str        # 怪物名称
    map_name: str           # 所在地图
    level: int              # 等级
    current_health: int     # 当前血量
    max_health: int         # 最大血量
    spawn_time: datetime    # 生成时间
    is_alive: bool = True   # 是否存活
    is_boss: bool = False   # 是否为Boss
    attackers: List[str] = field(default_factory=list)  # 攻击者列表
    
    def get_health_percentage(self) -> float:
        """获取血量百分比"""
        return self.current_health / self.max_health if self.max_health > 0 else 0.0
    
    def is_in_combat(self) -> bool:
        """是否在战斗中"""
        return len(self.attackers) > 0
```

### 配置数据模型
```python
@dataclass
class SpawnConfig:
    map_name: str
    monsters: List[str]
    max_count: int
    spawn_interval: int
    level_range: Tuple[int, int]
    is_safe_zone: bool = False

@dataclass
class DropConfig:
    monster_name: str
    items: List[DropItem]
    experience: int
    coins: Tuple[int, int]
```

## 错误处理设计

### 异常处理策略
1. **配置加载失败**：使用默认配置，记录错误日志
2. **刷怪线程异常**：自动重启线程，记录异常信息
3. **战斗状态异常**：清理异常状态，确保游戏继续
4. **掉落分配失败**：使用简单分配策略，确保玩家获得奖励

### 日志记录
```python
# 配置加载日志
logger.info(f"加载了 {len(monster_configs)} 种怪物配置")

# 刷怪日志
logger.debug(f"为地图 {map_name} 生成怪物 {monster_name}")

# 战斗日志
logger.info(f"玩家 {player_name} 加入对 {monster_name} 的战斗")

# 掉落日志
logger.info(f"怪物 {monster_name} 掉落分配完成，参与者: {len(participants)}")
```

## 性能优化设计

### 内存优化
1. **配置缓存**：启动时一次性加载，运行时直接访问
2. **怪物实例管理**：使用字典快速查找，定期清理死亡怪物
3. **发现记录优化**：使用集合存储，快速查找和更新

### 响应时间优化
1. **探索命令**：直接从内存获取数据，目标 < 200ms
2. **战斗命令**：优化战斗逻辑，目标 < 300ms
3. **刷怪系统**：后台线程处理，不阻塞主线程

### 并发处理
```python
import threading
from threading import Lock

class ThreadSafeSpawnSystem:
    def __init__(self):
        self.monsters_lock = Lock()
        self.participants_lock = Lock()
    
    def add_monster(self, monster):
        with self.monsters_lock:
            self.spawned_monsters[monster.monster_id] = monster
    
    def join_battle(self, monster_id, user_id):
        with self.participants_lock:
            # 安全地添加参与者
            pass
```

## 测试策略

### 单元测试
- 配置管理器测试
- 刷怪算法测试
- 掉落分配算法测试

### 集成测试
- 系统启动流程测试
- 探索-战斗-掉落完整流程测试
- 多人协同战斗测试

### 性能测试
- 配置加载时间测试
- 命令响应时间测试
- 并发用户测试

这个设计文档提供了完整的系统架构和实现方案，确保重构能够满足所有需求并保持良好的性能和可维护性。
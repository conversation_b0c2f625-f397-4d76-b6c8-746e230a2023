# 命令配置系统设计文档

## 概述

本设计文档描述了一个基于TOML配置文件的命令系统，支持多别名命令和灵活的参数配置。系统将替换现有的硬编码命令定义，提供更好的可维护性和用户体验。

## 架构

### 系统组件

```
CommandConfigSystem
├── CommandConfigLoader     # 配置文件加载器
├── CommandAliasResolver    # 别名解析器
├── CommandValidator        # 命令验证器
└── CommandRegistry         # 命令注册表
```

### 数据流

1. **启动阶段**：CommandConfigLoader 读取 command.toml 文件
2. **解析阶段**：CommandAliasResolver 将别名映射到标准命令名
3. **验证阶段**：CommandValidator 验证命令参数
4. **路由阶段**：CommandRegistry 将命令路由到对应处理器

## 组件和接口

### CommandConfigLoader

负责加载和解析TOML配置文件。

```python
class CommandConfigLoader:
    def load_config(self, config_path: str) -> Dict[str, Any]
    def create_default_config(self, config_path: str) -> None
    def validate_config_format(self, config: Dict[str, Any]) -> bool
```

### CommandAliasResolver

负责将用户输入的命令别名解析为标准命令名。

```python
class CommandAliasResolver:
    def resolve_alias(self, input_command: str) -> Optional[str]
    def get_all_aliases(self, standard_command: str) -> List[str]
    def build_alias_map(self, config: Dict[str, Any]) -> Dict[str, str]
```

### CommandValidator

负责验证命令参数和格式。

```python
class CommandValidator:
    def validate_command(self, command: str, params: List[str]) -> Tuple[bool, str]
    def get_command_info(self, command: str) -> Optional[CommandInfo]
    def generate_usage_hint(self, command: str) -> str
```

### CommandRegistry

负责管理命令到处理器的映射。

```python
class CommandRegistry:
    def register_handler(self, command: str, handler: BaseCommandHandler) -> None
    def get_handler(self, command: str) -> Optional[BaseCommandHandler]
    def get_commands_by_category(self, category: str) -> List[str]
```

## 数据模型

### CommandInfo

```python
@dataclass
class CommandInfo:
    name: str                    # 标准命令名
    aliases: List[str]           # 别名列表
    category: str                # 命令分类
    description: str             # 命令描述
    parameters: List[Parameter]  # 参数定义
    examples: List[str]          # 使用示例
    handler_class: str           # 处理器类名
```

### Parameter

```python
@dataclass
class Parameter:
    name: str           # 参数名
    type: str           # 参数类型
    required: bool      # 是否必需
    description: str    # 参数描述
    default: Any        # 默认值
```

## 配置文件结构

### command.toml 格式

```toml
[commands.basic]
# 帮助命令
[commands.basic.help]
name = "帮助"
aliases = ["帮助", "help", "?", "？"]
description = "显示游戏帮助信息"
handler = "InfoCommandHandler"
parameters = []
examples = ["!帮助", "!help"]

# 状态查看
[commands.basic.status]
name = "查看状态"
aliases = ["查看状态", "状态", "info", "stat"]
description = "查看角色状态信息"
handler = "CharacterCommandHandler"
parameters = []
examples = ["查看状态", "状态"]

[commands.combat]
# 攻击命令
[commands.combat.attack]
name = "攻击"
aliases = ["攻击", "打", "attack", "hit"]
description = "攻击指定目标"
handler = "BattleCommandHandler"
parameters = [
    { name = "target", type = "string", required = true, description = "攻击目标" }
]
examples = ["攻击 野猪", "打 野猪"]

[commands.equipment]
# 装备命令
[commands.equipment.equip]
name = "装备"
aliases = ["装备", "穿", "wear", "equip"]
description = "装备指定物品"
handler = "EquipmentCommandHandler"
parameters = [
    { name = "item", type = "string", required = true, description = "装备名称" }
]
examples = ["装备 游湖剑", "穿 游湖剑"]

# 背包查看
[commands.equipment.inventory]
name = "查看背包"
aliases = ["查看背包", "背包", "bag", "inventory"]
description = "查看背包内容"
handler = "EquipmentCommandHandler"
parameters = []
examples = ["查看背包", "背包"]
```

## 错误处理

### 配置文件错误

1. **文件不存在**：自动创建默认配置文件
2. **格式错误**：记录错误日志，使用默认配置
3. **缺少必需字段**：使用默认值并记录警告

### 命令解析错误

1. **未知命令**：提供相似命令建议
2. **参数错误**：显示正确的使用格式
3. **权限不足**：显示权限要求说明

## 测试策略

### 单元测试

- CommandConfigLoader 配置文件加载测试
- CommandAliasResolver 别名解析测试
- CommandValidator 参数验证测试
- CommandRegistry 命令注册测试

### 集成测试

- 完整命令处理流程测试
- 多别名命令执行测试
- 错误处理流程测试

### 性能测试

- 大量别名解析性能测试
- 配置文件加载性能测试
- 内存使用情况测试

## 实现计划

### 阶段1：核心组件实现
- 实现 CommandConfigLoader
- 实现 CommandAliasResolver
- 创建基础数据模型

### 阶段2：验证和注册
- 实现 CommandValidator
- 实现 CommandRegistry
- 集成现有命令处理器

### 阶段3：配置文件和测试
- 创建完整的 command.toml 配置
- 更新命令解析器集成新系统
- 编写测试用例

### 阶段4：优化和文档
- 性能优化
- 错误处理完善
- 使用文档编写
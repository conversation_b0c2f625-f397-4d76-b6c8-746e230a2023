# 命令配置系统实现计划

- [x] 1. 创建数据模型和基础结构

  - 创建 CommandInfo 和 Parameter 数据类
  - 实现基础的命令配置数据结构
  - _需求: 1.1, 5.2_

- [x] 2. 实现配置文件加载器

  - 创建 CommandConfigLoader 类
  - 实现 TOML 配置文件读取功能
  - 添加默认配置文件创建功能
  - 实现配置格式验证
  - _需求: 1.1, 1.2, 1.3_

- [x] 3. 实现别名解析器

  - 创建 CommandAliasResolver 类
  - 实现别名到标准命令名的映射
  - 构建高效的别名查找机制
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 4. 实现命令验证器

  - 创建 CommandValidator 类
  - 实现命令参数验证逻辑
  - 添加使用提示生成功能
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [x] 5. 实现命令注册表

  - 创建 CommandRegistry 类
  - 实现命令到处理器的映射
  - 添加按分类获取命令的功能
  - _需求: 3.1, 3.2, 3.3_

- [x] 6. 创建完整的命令配置文件

  - 创建 Plugins/Mir2Game/command.toml 文件
  - 配置所有现有游戏命令及其别名
  - 按功能分类组织命令结构
  - 添加参数定义和使用示例
  - _需求: 5.1, 5.2, 5.3, 5.4_

- [x] 7. 集成命令解析器

  - 修改现有的 CommandParser 类
  - 集成新的别名解析功能
  - 更新命令验证逻辑
  - 确保与现有命令处理器兼容
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 8. 更新命令路由器

  - 修改 CommandRouter 类
  - 集成新的命令注册表
  - 更新命令映射逻辑
  - _需求: 3.1, 3.2, 3.3_

- [x] 9. 更新帮助系统

  - 修改 HelpSystem 类
  - 基于配置文件生成帮助信息
  - 实现按分类显示命令功能
  - 添加别名显示支持
  - _需求: 3.3, 4.4_

- [x] 10. 实现错误处理和日志

  - 添加配置文件错误处理
  - 实现命令解析错误处理
  - 添加相似命令建议功能
  - 完善日志记录
  - _需求: 1.3, 4.3_

- [x] 11. 编写单元测试

  - 为 CommandConfigLoader 编写测试
  - 为 CommandAliasResolver 编写测试
  - 为 CommandValidator 编写测试
  - 为 CommandRegistry 编写测试
  - _需求: 1.1, 2.1, 4.1, 3.1_

- [x] 12. 集成测试和验证

  - 测试完整的命令处理流程
  - 验证所有别名命令正常工作
  - 测试错误处理场景
  - 验证配置文件热重载功能（可选）
  - _需求: 1.4, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_
# 西子江湖游戏插件性能优化设计文档

## 设计概述

本设计文档基于性能分析结果，提出了一套完整的性能优化方案。核心思路是通过数据库连接池、异步处理、智能缓存和资源复用来解决多人游戏时的性能瓶颈。

## 系统架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    微信消息处理层                              │
├─────────────────────────────────────────────────────────────┤
│                    用户命令队列管理层                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 命令状态管理  │  │ 用户队列     │  │ 执行控制     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    缓存管理层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 角色缓存     │  │ 配置缓存     │  │ 会话缓存     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 连接池管理   │  │ 读写分离     │  │ 事务管理     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    SQLite数据库                              │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件设计

### 1. 数据库连接池设计

#### 连接池管理器 (ConnectionPoolManager)

```python
class ConnectionPoolManager:
    """数据库连接池管理器"""
    
    def __init__(self, db_path: str, pool_size: int = 10):
        self.db_path = db_path
        self.pool_size = pool_size
        self.read_pool = Queue(maxsize=pool_size)
        self.write_pool = Queue(maxsize=2)  # 写操作连接数较少
        self.pool_lock = threading.Lock()
        
    def get_read_connection(self) -> sqlite3.Connection:
        """获取读连接"""
        
    def get_write_connection(self) -> sqlite3.Connection:
        """获取写连接"""
        
    def return_connection(self, conn: sqlite3.Connection, is_write: bool):
        """归还连接到池中"""
```

#### 设计要点
- **读写分离**：读操作使用较大的连接池，写操作使用较小的连接池
- **连接复用**：避免频繁创建/销毁连接
- **超时管理**：连接获取超时机制，避免无限等待
- **健康检查**：定期检查连接有效性，自动重建失效连接

### 2. 用户命令队列管理设计

#### 用户命令状态管理器 (UserCommandManager)

```python
class UserCommandManager:
    """用户命令状态管理器"""
    
    def __init__(self):
        self.user_commands = {}  # {user_id@room_id: command_status}
        self.lock = threading.RLock()
        
    def can_execute_command(self, user_id: str, room_id: str, command: str) -> tuple[bool, str]:
        """检查用户是否可以执行命令"""
        
    def start_command(self, user_id: str, room_id: str, command: str):
        """开始执行命令"""
        
    def finish_command(self, user_id: str, room_id: str):
        """完成命令执行"""
```

#### 设计要点
- **简单状态管理**：每个用户在每个区只能有一个正在执行的命令
- **友好提示**：命令冲突时返回幽默的茶文化提示
- **自动清理**：命令完成后自动从队列中移除
- **并发支持**：不同用户的命令可以并发执行

### 3. 智能缓存系统设计

#### 多层缓存架构

```python
class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.character_cache = LRUCache(maxsize=1000, ttl=300)  # 角色缓存
        self.config_cache = SingletonCache()                    # 配置缓存
        self.session_cache = TTLCache(maxsize=500, ttl=600)     # 会话缓存
        
    def get_character(self, user_id: str, room_id: str) -> Optional[Character]:
        """获取角色（优先从缓存）"""
        
    def update_character(self, character: Character):
        """更新角色（同时更新缓存和数据库）"""
        
    def invalidate_character(self, user_id: str, room_id: str):
        """使角色缓存失效"""
```

#### 缓存策略
- **LRU缓存**：角色数据使用LRU策略，自动淘汰最少使用的数据
- **TTL缓存**：会话数据使用TTL策略，自动过期清理
- **单例缓存**：配置数据使用单例模式，全局共享
- **写透缓存**：数据更新时同时更新缓存和数据库

### 4. 资源管理优化设计

#### 单例配置管理器 (ConfigSingleton)

```python
class ConfigSingleton:
    """配置单例管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialize()
        return cls._instance
        
    def _initialize(self):
        """初始化配置"""
        self.command_config = self._load_command_config()
        self.game_config = self._load_game_config()
```

#### 对象池设计

```python
class ObjectPool:
    """对象池管理器"""
    
    def __init__(self, factory_func, max_size: int = 50):
        self.factory_func = factory_func
        self.pool = Queue(maxsize=max_size)
        self.created_count = 0
        
    def get_object(self):
        """获取对象"""
        
    def return_object(self, obj):
        """归还对象"""
```

### 5. 性能监控设计

#### 性能指标收集器 (PerformanceCollector)

```python
class PerformanceCollector:
    """性能指标收集器"""
    
    def __init__(self):
        self.metrics = {
            'command_count': Counter(),
            'response_time': Histogram(),
            'concurrent_users': Gauge(),
            'database_operations': Counter(),
            'cache_hit_rate': Gauge()
        }
        
    def record_command_execution(self, command: str, duration: float):
        """记录命令执行时间"""
        
    def record_database_operation(self, operation: str, duration: float):
        """记录数据库操作时间"""
```

## 数据模型设计

### 缓存数据结构

```python
@dataclass
class CachedCharacter:
    """缓存的角色数据"""
    character: Character
    last_updated: datetime
    version: int
    dirty: bool = False  # 是否需要写回数据库
    
@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: datetime
    accessed_at: datetime
    access_count: int
    ttl: Optional[int] = None
```

### 性能指标数据结构

```python
@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: datetime
    command_count: int
    avg_response_time: float
    concurrent_users: int
    database_connections: int
    cache_hit_rate: float
    memory_usage: float
    cpu_usage: float
```

## 错误处理设计

### 异常处理策略

1. **数据库异常**：自动重试 + 连接重建 + 降级处理
2. **缓存异常**：缓存失效 + 直接查询数据库
3. **并发异常**：队列缓冲 + 超时处理
4. **资源异常**：自动清理 + 资源回收 

### 降级机制

```python
class DegradationManager:
    """降级管理器"""
    
    def __init__(self):
        self.degradation_level = 0  # 0=正常, 1=轻度降级, 2=重度降级
        
    def check_system_health(self) -> int:
        """检查系统健康状态"""
        
    def apply_degradation(self, level: int):
        """应用降级策略"""
        if level == 1:
            # 轻度降级：禁用部分缓存，减少并发数
            pass
        elif level == 2:
            # 重度降级：只处理核心命令，暂停非关键功能
            pass
```

## 测试策略

### 性能测试设计

1. **负载测试**：模拟10-50个并发用户
2. **压力测试**：测试系统极限承载能力
3. **稳定性测试**：长时间运行测试
4. **内存泄漏测试**：监控内存使用情况

### 测试工具

```python
class PerformanceTestSuite:
    """性能测试套件"""
    
    def test_concurrent_commands(self, user_count: int, command_count: int):
        """并发命令测试"""
        
    def test_database_performance(self, operation_count: int):
        """数据库性能测试"""
        
    def test_cache_efficiency(self, cache_size: int, access_pattern: str):
        """缓存效率测试"""
```

## 部署和迁移策略

1. 不考虑数据备份兼容、回滚等等各种往前兼容的问题，项目还没上线
2. 游戏还没有上线，不要保留旧方法、旧代码，所有功能保证整洁一份


### 配置管理

```yaml
# performance_config.yaml
database:
  connection_pool_size: 10
  write_pool_size: 2
  connection_timeout: 30
  
cache:
  character_cache_size: 1000
  character_cache_ttl: 300
  config_cache_enabled: true
  
async:
  max_workers: 10
  queue_size: 100
  command_timeout: 30
  
monitoring:
  metrics_enabled: true
  metrics_interval: 60
  alert_thresholds:
    response_time: 2.0
    error_rate: 0.05
```

## 安全考虑

1. **并发安全**：使用线程安全的数据结构
2. **数据一致性**：事务管理和锁机制
3. **资源限制**：防止资源耗尽攻击
4. **错误信息**：避免泄露敏感信息
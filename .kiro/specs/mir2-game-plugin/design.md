# 设计文档

## 概述

西子江湖游戏插件是一个基于微信机器人框架的文字RPG游戏插件。游戏以杭州西湖的历史文化为背景，融合了白蛇传、雷峰塔等传统故事元素，让玩家在充满江湖气息的西湖世界中体验升级、战斗、社交等丰富玩法。插件采用模块化设计，遵循现有的插件架构模式，确保与机器人框架的良好集成。游戏采用纯文字交互模式，通过文字描述和emoji表情增强游戏体验，部分场景使用ASCII字符绘制界面效果。

## 架构

### 整体架构

```mermaid
graph TB
    A[微信消息] --> B[Mir2GamePlugin主类]
    B --> C[命令解析器]
    C --> D[游戏模式管理器]
    C --> E[帮助系统]
    
    D --> F[角色管理器]
    D --> G[战斗系统]
    D --> H[地图系统]
    D --> I[装备系统]
    D --> J[技能系统]
    D --> K[经济系统]
    D --> L[社交系统]
    
    F --> M[数据库层]
    G --> M
    H --> M
    I --> M
    J --> M
    K --> M
    L --> M
    
    M --> N[SQLite数据库]
```

### 插件架构

插件将遵循现有的插件架构模式：
- 继承自`PluginBase`基类
- 使用装饰器注册事件处理器
- 采用配置文件管理插件设置
- 使用独立的数据库文件存储游戏数据

## 组件和接口

### 1. 主插件类 (Mir2GamePlugin)

**职责：**
- 继承PluginBase，实现插件生命周期管理
- 注册消息事件处理器
- 协调各个子系统的工作

**接口：**
```python
class Mir2GamePlugin(PluginBase):
    def __init__(self)
    
    @on_group_text
    def handle_group_text(self, message: dict)
    
    @on_group_at_robot  
    def handle_group_at_robot(self, message: dict)
```

### 2. 命令解析器 (CommandParser)

**职责：**
- 解析用户输入的游戏命令
- 区分强命令（!开头）和游戏模式命令
- 验证命令格式和参数

**接口：**
```python
class CommandParser:
    def parse_command(self, content: str, is_game_mode: bool) -> Command
    def validate_command(self, command: Command) -> bool
    def get_help_text(self, command_type: str) -> str

# 支持的游戏命令列表
GAME_COMMANDS = {
    # 基础命令
    "查看状态": {"params": [], "description": "显示角色当前行动、状态、位置、死亡信息"},
    "查看地图": {"params": [], "description": "显示地图、人物分布、boss分布"},
    "查看装备": {"params": [], "description": "显示装备属性和装备情况"},
    "查看背包": {"params": [], "description": "显示背包内容"},
    "查看排名": {"params": [], "description": "显示战力、财富、恶人、降妖排行"},
    
    # 移动和探索
    "前往": {"params": ["地点名"], "description": "移动到指定地点"},
    
    # 战斗命令
    "攻击": {"params": ["目标"], "description": "攻击怪物或玩家"},
    "施": {"params": ["技能名", "目标"], "description": "对目标释放技能"},
    
    # 装备命令
    "装备": {"params": ["装备名"], "description": "装备指定物品"},
    "升品": {"params": ["装备名"], "description": "升级装备品级"},
    "浣灵": {"params": ["装备名"], "description": "洗练玉佩灵力属性"},
    
    # 物品命令
    "服用": {"params": ["物品名"], "description": "使用消耗品"},
    "存": {"params": ["物品名"], "description": "存储物品到仓库"},
    "取": {"params": ["物品名"], "description": "从仓库取出物品"},
    
    # 交易命令
    "买": {"params": ["物品名"], "description": "从NPC购买物品"},
    "卖": {"params": ["物品名"], "description": "向NPC出售物品"},
    "邀买": {"params": ["@玩家"], "description": "邀请玩家购买"},
    "邀卖": {"params": ["@玩家"], "description": "邀请玩家出售"},
    
    # 挂机命令
    "挂机": {"params": ["类型"], "description": "开始挂机活动(钓鱼/采集/打怪)"},
    
    # 社交命令
    "交易": {"params": ["@玩家", "物品名", "数量"], "description": "向玩家发起交易请求"},
    "接受交易": {"params": [], "description": "接受交易请求"},
    "拒绝交易": {"params": [], "description": "拒绝交易请求"},
    "组队": {"params": ["@玩家"], "description": "邀请玩家组队"},
    "接受组队": {"params": [], "description": "接受组队邀请"},
    "拒绝组队": {"params": [], "description": "拒绝组队邀请"},
    "离开队伍": {"params": [], "description": "离开当前队伍"},
    
    # 强命令（!开头）
    "!游戏模式": {"params": [], "description": "进入游戏模式"},
    "!退出游戏模式": {"params": [], "description": "退出游戏模式"},
    "!帮助": {"params": [], "description": "显示游戏帮助"},
    "!命令": {"params": [], "description": "显示所有命令列表"},
    "!职业介绍": {"params": [], "description": "显示职业介绍"},
    "!新手指南": {"params": [], "description": "显示新手指南"}
}
```

### 3. 游戏模式管理器 (GameModeManager)

**职责：**
- 管理玩家的游戏模式状态
- 处理进入/退出游戏模式的逻辑
- 维护玩家会话状态

**接口：**
```python
class GameModeManager:
    def enter_game_mode(self, user_id: str, room_id: str) -> bool
    def exit_game_mode(self, user_id: str, room_id: str) -> bool
    def is_in_game_mode(self, user_id: str, room_id: str) -> bool
    def get_game_session(self, user_id: str, room_id: str) -> GameSession
```

### 4. 角色管理器 (CharacterManager)

**职责：**
- 管理角色创建、属性、等级系统
- 处理角色数据的持久化
- 提供角色信息查询接口

**接口：**
```python
class CharacterManager:
    def create_character(self, user_id: str, name: str, profession: str) -> Character
    def get_character(self, user_id: str) -> Character
    def update_character(self, character: Character) -> bool
    def level_up(self, character: Character) -> bool
    def add_experience(self, character: Character, exp: int) -> bool
```

### 5. 战斗系统 (BattleSystem)

**职责：**
- 处理PVE和PVP战斗逻辑
- 计算伤害、职业克制关系
- 管理战斗状态和回合制战斗

**接口：**
```python
class BattleSystem:
    def start_pve_battle(self, character: Character, monster: Monster) -> Battle
    def start_pvp_battle(self, attacker: Character, defender: Character) -> Battle
    def process_battle_round(self, battle: Battle, action: BattleAction) -> BattleResult
    def calculate_damage(self, attacker: Character, defender: Character, skill: Skill) -> int
    def apply_profession_bonus(self, attacker_prof: str, defender_prof: str) -> float
```

### 6. 地图系统 (MapSystem)

**职责：**
- 管理游戏地图和玩家位置
- 处理地图移动和探索
- 生成随机遭遇事件

**接口：**
```python
class MapSystem:
    def get_current_map(self, character: Character) -> GameMap
    def move_character(self, character: Character, direction: str) -> bool
    def explore_area(self, character: Character) -> ExploreResult
    def generate_encounter(self, character: Character) -> Encounter
    def render_map_ascii(self, game_map: GameMap, character_pos: tuple) -> str
```

### 7. 物品系统 (ItemSystem)

**职责：**
- 管理游戏中所有物品的定义和配置
- 处理物品的创建、使用和销毁
- 管理物品的分类和属性
- 提供物品查询和搜索功能

**接口：**
```python
class ItemSystem:
    def create_item(self, item_type: str, item_id: str, quantity: int = 1) -> Item
    def use_item(self, character: Character, item: Item) -> bool
    def get_item_info(self, item_id: str) -> ItemInfo
    def get_items_by_type(self, item_type: str) -> List[Item]
    def validate_item_usage(self, character: Character, item: Item) -> bool
```

**物品类型架构：**
```mermaid
graph TB
    A[Item基类] --> B[Currency货币]
    A --> C[Equipment装备]
    A --> D[Material材料]
    A --> E[Consumable消耗品]
    A --> F[SkillBook技能书]
    
    B --> B1[Gold金币]
    B --> B2[Silver银两]
    
    C --> C1[Weapon武器]
    C --> C2[Armor防具]
    C --> C3[Accessory饰品]
    
    D --> D1[BasicMaterial基础材料]
    D --> D2[EnhanceMaterial强化材料]
    D --> D3[SpecialMaterial特殊材料]
    
    E --> E1[HealthPotion回血药]
    E --> E2[ManaPotion回魔药]
    E --> E3[BuffPotion属性药]
    
    F --> F1[SwordSkill剑客技能书]
    F --> F2[MageSkill仙师技能书]
    F --> F3[MonkSkill圣僧技能书]
```

### 7.1 货币系统 (CurrencySystem)

**职责：**
- 管理金币和银两的流通
- 处理货币兑换和转账
- 跟踪玩家财富状况

**货币类型：**
- **金币 (Gold)**: 基础货币，用于日常交易
- **银两 (Silver)**: 高级货币，1银两=10000金币

### 7.2 材料系统 (MaterialSystem)

**职责：**
- 管理各种制作和强化材料
- 处理材料的获取和消耗
- 提供材料合成功能

**材料分类：**
- **基础材料**: 桑叶、柳枝、荷叶、蛙油等
- **强化材料**: 天蚕丝、雨花石、天外陨石
- **特殊材料**: 日月同辉、龙珠、舍利子等

### 7.3 消耗品系统 (ConsumableSystem)

**职责：**
- 管理各种药品和消耗品
- 处理物品使用效果
- 管理物品冷却时间

**消耗品分类：**
- **回血药品**: 小还丹(+100血)、大还丹(+500血)、超级还丹(+1000血)
- **回魔药品**: 回魔丹(+100魔)、法力药水(+300魔)、仙露(+500魔)
- **属性药品**: 力量丹(+10攻击30分钟)、防御符(+10防御30分钟)
- **特殊药品**: 解毒丹、复活丹、隐身符

### 7.4 技能书系统 (SkillBookSystem)

**职责：**
- 管理技能书的获取和使用
- 验证技能学习条件
- 处理技能书消耗

**技能书分类：**
- **剑客技能书**: 破军剑法、镜面令、御剑术
- **仙师技能书**: 玄雷术、珠钗令、回法术
- **圣僧技能书**: 金刚掌、金刚护体、大悲咒

### 8. 装备系统 (EquipmentSystem)

**职责：**
- 管理装备获取、装备、强化
- 计算装备属性加成
- 处理装备耐久度和损坏

**接口：**
```python
class EquipmentSystem:
    def equip_item(self, character: Character, item: Equipment) -> bool
    def unequip_item(self, character: Character, slot: str) -> Equipment
    def enhance_equipment(self, equipment: Equipment, materials: list) -> bool
    def calculate_equipment_bonus(self, character: Character) -> AttributeBonus
    def generate_equipment_drop(self, level: int, rarity: str) -> Equipment
```

### 9. 技能系统 (SkillSystem)

**职责：**
- 管理技能学习和使用
- 处理技能冷却和消耗
- 计算技能效果和伤害

**接口：**
```python
class SkillSystem:
    def learn_skill(self, character: Character, skill_id: str) -> bool
    def use_skill(self, character: Character, skill_id: str, target: Character) -> SkillResult
    def get_available_skills(self, character: Character) -> list[Skill]
    def check_skill_cooldown(self, character: Character, skill_id: str) -> int
    def calculate_skill_damage(self, character: Character, skill: Skill) -> int
```

### 10. 经济系统 (EconomySystem)

**职责：**
- 管理金币和银两的获得、消费
- 处理玩家间交易
- 维护游戏经济平衡

**接口：**
```python
class EconomySystem:
    def add_coins(self, character: Character, amount: int) -> bool
    def spend_coins(self, character: Character, amount: int) -> bool
    def convert_currency(self, character: Character, from_type: str, to_type: str, amount: int) -> bool
    def initiate_trade(self, requester_id: str, target_id: str, item_name: str, quantity: int, coins: int) -> TradeRequest
    def accept_trade(self, trade_id: int) -> bool
    def reject_trade(self, trade_id: int) -> bool
    def get_pending_trade(self, user_id: str) -> TradeRequest
    def cleanup_expired_trades(self) -> None
```

### 10.1. 社交系统 (SocialSystem)

**职责：**
- 管理组队邀请和队伍
- 处理队伍战斗经验分配
- 维护社交关系

**接口：**
```python
class SocialSystem:
    def invite_to_team(self, inviter_id: str, invitee_id: str, room_id: str) -> TeamInvite
    def accept_team_invite(self, invite_id: int) -> bool
    def reject_team_invite(self, invite_id: int) -> bool
    def leave_team(self, user_id: str) -> bool
    def get_team(self, user_id: str) -> Team
    def get_pending_invite(self, user_id: str) -> TeamInvite
    def distribute_team_experience(self, team: Team, total_exp: int) -> dict
    def cleanup_expired_invites(self) -> None
```

### 11. 帮助系统 (HelpSystem)

**职责：**
- 提供游戏帮助信息
- 生成命令说明和新手指南
- 管理帮助内容的动态更新

**接口：**
```python
class HelpSystem:
    def get_game_introduction(self) -> str
    def get_command_list(self) -> str
    def get_profession_guide(self) -> str
    def get_newbie_tutorial(self, step: int) -> str
    def get_context_help(self, command: str) -> str
```

## 数据模型

### 1. 角色数据模型

```python
@dataclass
class Character:
    user_id: str
    name: str
    profession: str  # 剑客/仙师/圣僧
    level: int
    experience: int
    experience_to_next_level: int
    title: str  # 称号：初出茅庐/江湖新手/江湖有名/武林高手/西湖神话
    
    # 基础属性
    base_health: int  # 血气
    base_mana: int    # 法气
    base_inner_power: int  # 内力
    
    # 攻击属性（根据职业不同）
    physical_attack: int  # 物攻（剑客主要）
    magic_attack: int     # 法攻（仙师主要）
    buddha_attack: int    # 佛攻（圣僧主要）
    
    # 防御属性
    physical_defense: int  # 物防
    magic_defense: int     # 法防
    buddha_defense: int    # 佛防
    
    # 特殊属性
    spirit_power: int     # 灵力（增加攻击百分比）
    counter_damage: int   # 反伤（受攻击时反弹伤害）
    
    # 当前状态
    current_health: int
    current_mana: int
    
    # 位置信息
    current_map: str
    
    # 货币
    coins: int
    silver: int
    
    # 状态信息
    is_alive: bool
    evil_reputation: int  # 江湖恶名
    last_battle_time: datetime
    skill_cooldowns: dict  # 技能冷却时间
    is_afk: bool         # 是否挂机状态
    afk_type: str        # 挂机类型：钓鱼/采集/打怪
    
    # 时间戳
    created_at: datetime
    last_active: datetime

# 等级成长配置
LEVEL_GROWTH = {
    "剑客": {
        "health_per_level": 20,
        "mana_per_level": 5,
        "physical_attack_per_level": 5,
        "magic_attack_per_level": 1,
        "buddha_attack_per_level": 1,
        "physical_defense_per_level": 3,
        "magic_defense_per_level": 1,
        "buddha_defense_per_level": 1
    },
    "仙师": {
        "health_per_level": 15,
        "mana_per_level": 15,
        "physical_attack_per_level": 1,
        "magic_attack_per_level": 5,
        "buddha_attack_per_level": 1,
        "physical_defense_per_level": 1,
        "magic_defense_per_level": 3,
        "buddha_defense_per_level": 1
    },
    "圣僧": {
        "health_per_level": 18,
        "mana_per_level": 10,
        "physical_attack_per_level": 1,
        "magic_attack_per_level": 1,
        "buddha_attack_per_level": 5,
        "physical_defense_per_level": 1,
        "magic_defense_per_level": 1,
        "buddha_defense_per_level": 3
    }
}

# 等级称号配置
LEVEL_TITLES = {
    (1, 9): "初出茅庐",
    (10, 29): "江湖新手", 
    (30, 59): "江湖有名",
    (60, 99): "武林高手",
    (100, 999): "西湖神话"
}

# 经验值需求配置
def calculate_experience_needed(level: int) -> int:
    """计算升级所需经验值"""
    return level * 100 + (level - 1) * 50  # 递增经验需求
```

### 2. 物品系统数据模型

```python
@dataclass
class Item:
    """物品基类"""
    id: str
    name: str
    type: str  # currency, equipment, material, consumable, skill_book
    description: str
    rarity: str  # common, rare, epic, legendary
    stack_size: int  # 最大堆叠数量
    value: int  # 基础价值（金币）
    
@dataclass
class Currency(Item):
    """货币类物品"""
    exchange_rate: float  # 兑换汇率（相对于金币）
    
@dataclass
class Material(Item):
    """材料类物品"""
    material_type: str  # basic, enhance, special
    usage: List[str]  # 用途列表
    
@dataclass
class Consumable(Item):
    """消耗品类物品"""
    effect_type: str  # health, mana, buff, special
    effect_value: int  # 效果数值
    duration: int  # 持续时间（秒）
    cooldown: int  # 冷却时间（秒）
    level_required: int  # 使用等级要求
    
@dataclass
class SkillBook(Item):
    """技能书类物品"""
    skill_id: str  # 对应技能ID
    profession: str  # 职业要求
    level_required: int  # 学习等级要求
    success_rate: float  # 学习成功率
```

### 3. 装备数据模型

```python
@dataclass
class Equipment(Item):
    """装备类物品"""
    equipment_type: str  # weapon, armor, accessory
    slot: str  # 装备槽位
    level_required: int  # 装备等级要求
    profession: str  # 职业要求（空字符串表示通用）
    
    # 基础属性
    attack_bonus: int
    defense_bonus: int
    agility_bonus: int
    
    # 装备状态
    durability: int
    max_durability: int
    enhancement_level: int  # 强化等级
    grade: int  # 品级（1-10+）
    
    # 套装信息
    set_name: str  # 套装名称
    set_bonus: Dict[str, int]  # 套装加成
    
    owner_id: str
```

### 4. 技能数据模型

```python
@dataclass
class Skill:
    id: str
    name: str
    profession: str
    level_required: int
    mana_cost: int
    cooldown: int
    damage_multiplier: float
    effect_type: str  # 攻击/治疗/增益/减益
    description: str
```

### 5. 地图数据模型

```python
@dataclass
class GameMap:
    id: str
    name: str
    description: str
    level_range: tuple  # (min_level, max_level)
    monster_types: list  # 该地图的怪物类型列表
    ascii_layout: str
    connections: dict  # 连接到其他地图的出口 {"北": "野外森林", "南": "废弃矿洞"}
    
# 具体地图配置
GAME_MAPS = {
    "武林主城": GameMap(
        id="wulin_city",
        name="武林主城",
        description="繁华的武林主城，有各种NPC和交易功能",
        level_range=(1, 999),
        monster_types=[],
        ascii_layout="...",
        connections={"苏堤": "苏堤", "断桥": "断桥", "孤山": "孤山", "太子湾": "太子湾"}
    ),
    "苏堤": GameMap(
        id="sudi",
        name="苏堤",
        description="风景秀丽的苏堤，可以钓鱼，有柳树精和青蛙精出没",
        level_range=(1, 9),
        monster_types=["柳树精", "青蛙精"],
        ascii_layout="...",
        connections={"武林主城": "武林主城", "断桥": "断桥"}
    ),
    "断桥": GameMap(
        id="duanqiao",
        name="断桥",
        description="著名的断桥，可以钓鱼，有水鬼出没，许仙和宋五嫂在此",
        level_range=(1, 9),
        monster_types=["水鬼"],
        ascii_layout="...",
        connections={"武林主城": "武林主城", "苏堤": "苏堤"}
    ),
    "雷峰塔底": GameMap(
        id="leifeng_tower",
        name="雷峰塔底",
        description="神秘的雷峰塔底层，法海镇守于此，危险重重",
        level_range=(10, 99),
        monster_types=["法海", "金刚力士", "乌龟精", "螃蟹精"],
        ascii_layout="...",
        connections={}  # 副本地图，需要特殊进入方式
    )
    # ... 更多地图
}
```

### 6. 怪物数据模型

```python
@dataclass
class Monster:
    id: str
    name: str
    level: int
    health: int
    attack: int
    defense: int
    experience_reward: int
    coin_reward: tuple  # (min_coins, max_coins)
    drop_items: list  # 掉落物品列表
    spawn_rate: float  # 出现概率

# 具体怪物配置
MONSTERS = {
    "柳树精": Monster(
        id="willow_spirit",
        name="柳树精",
        level=3,
        health=60,
        attack_type="physical",
        attack=20,
        defense=8,
        experience_reward=30,
        coin_reward=(10, 20),
        drop_items=["游湖套装", "桑叶"],
        spawn_rate=0.7
    ),
    "青蛙精": Monster(
        id="frog_spirit",
        name="青蛙精",
        level=4,
        health=70,
        attack_type="magic",
        attack=25,
        defense=6,
        experience_reward=35,
        coin_reward=(12, 25),
        drop_items=["湖光套装", "荷叶"],
        spawn_rate=0.6
    ),
    "法海": Monster(
        id="fahai",
        name="法海",
        level=25,
        health=2000,
        attack_type="mixed",  # 物理、法术、佛法三系攻击
        attack=150,
        defense=80,
        experience_reward=1000,
        coin_reward=(500, 1000),
        drop_items=["岳王套装", "高级技能书", "银两"],
        spawn_rate=0.1  # boss级别，低概率出现
    )
    # ... 更多怪物
}
```

### 7. 背包数据模型

```python
@dataclass
class Equipment:
    id: str
    name: str
    type: str  # 武器/防具/饰品
    level_required: int
    rarity: str  # 普通/优秀/稀有/史诗/传说
    attack_bonus: int
    defense_bonus: int
    agility_bonus: int
    durability: int
    max_durability: int
    enhancement_level: int
    special_effects: list  # 特殊效果
    owner_id: str

# 具体装备配置
EQUIPMENTS = {
    "木剑": Equipment(
        id="wooden_sword",
        name="木剑",
        type="武器",
        level_required=1,
        rarity="普通",
        attack_bonus=10,
        defense_bonus=0,
        agility_bonus=0,
        durability=100,
        max_durability=100,
        enhancement_level=0,
        special_effects=[],
        owner_id=""
    ),
    "屠龙刀": Equipment(
        id="dragon_slayer",
        name="屠龙刀", 
        type="武器",
        level_required=20,
        rarity="传说",
        attack_bonus=100,
        defense_bonus=0,
        agility_bonus=10,
        durability=500,
        max_durability=500,
        enhancement_level=0,
        special_effects=["吸血", "暴击"],
        owner_id=""
    )
    # ... 更多装备
}
```

### 8. 技能数据模型

```python
@dataclass
class Skill:
    id: str
    name: str
    profession: str  # 战士/法师/道士
    level_required: int
    mana_cost: int
    cooldown: int  # 冷却时间（秒）
    damage_multiplier: float
    effect_type: str  # 攻击/治疗/增益/减益
    special_effects: list  # 特殊效果：眩晕、冰冻等
    description: str

# 具体技能配置
SKILLS = {
    "破军剑法": Skill(
        id="pojun_sword",
        name="破军剑法",
        profession="剑客",
        level_required=1,
        mana_cost=20,
        cooldown=15,
        damage_multiplier=1.5,
        effect_type="攻击",
        special_effects=[],
        description="剑客的物攻主攻技能"
    ),
    "镜面令": Skill(
        id="mirror_shield",
        name="镜面令",
        profession="剑客",
        level_required=5,
        mana_cost=30,
        cooldown=20,
        damage_multiplier=0,
        effect_type="辅助",
        special_effects=["反弹伤害50%"],
        description="反弹伤害的辅助技能"
    ),
    "玄雷术": Skill(
        id="thunder_magic",
        name="玄雷术",
        profession="仙师",
        level_required=1,
        mana_cost=20,
        cooldown=15,
        damage_multiplier=1.5,
        effect_type="攻击",
        special_effects=[],
        description="仙师的法攻主攻技能"
    ),
    "金刚掌": Skill(
        id="vajra_palm",
        name="金刚掌",
        profession="圣僧",
        level_required=1,
        mana_cost=20,
        cooldown=15,
        damage_multiplier=1.5,
        effect_type="攻击",
        special_effects=[],
        description="圣僧的佛攻主攻技能"
    )
    # ... 更多技能
}
```

### 9. 战斗数据模型

```python
@dataclass
class Battle:
    id: str
    battle_type: str  # PVE/PVP
    attacker: Character
    defender: Character  # 或Monster
    current_round: int
    battle_log: list
    status: str  # 进行中/结束
    winner: str
    rewards: dict
    
@dataclass
class BattleResult:
    winner: str  # 获胜方ID
    experience_gained: int
    coins_gained: int
    items_dropped: list
    experience_lost: int  # PVP死亡扣除的经验
    equipment_dropped: Equipment  # PVP死亡掉落的装备
```

### 10. 交易和组队数据模型

```python
@dataclass
class TradeRequest:
    id: int
    requester_id: str
    target_id: str
    room_id: str
    item_name: str
    quantity: int
    coins_offered: int
    status: str  # pending/accepted/rejected/expired
    created_at: datetime
    expires_at: datetime

@dataclass
class TeamInvite:
    id: int
    inviter_id: str
    invitee_id: str
    room_id: str
    status: str  # pending/accepted/rejected/expired
    created_at: datetime
    expires_at: datetime

@dataclass
class Team:
    id: int
    leader_id: str
    room_id: str
    members: list[str]  # 成员ID列表
    created_at: datetime
```

### 11. NPC系统数据模型

```python
@dataclass
class NPC:
    id: str
    name: str
    location: str
    npc_type: str  # 商人/任务/装饰
    goods: list    # 出售的商品列表
    buy_items: list  # 收购的物品列表
    dialogue: str  # 对话内容
    
# NPC配置
NPCS = {
    "钱镠": NPC(
        id="qianliu",
        name="钱镠",
        location="武林主城",
        npc_type="商人",
        goods=["天蚕丝", "桑叶"],
        buy_items=["天蚕丝", "桑叶"],
        dialogue="老夫钱镠，专营丝绸生意"
    ),
    "岳飞": NPC(
        id="yuefei", 
        name="岳飞",
        location="武林主城",
        npc_type="商人",
        goods=["各类装备"],
        buy_items=["各类装备"],
        dialogue="精忠报国，装备精良"
    ),
    "许仙": NPC(
        id="xuxian",
        name="许仙",
        location="断桥",
        npc_type="商人", 
        goods=["各类药品"],
        buy_items=[],
        dialogue="在下许仙，专治各种疑难杂症"
    ),
    "济公": NPC(
        id="jigong",
        name="济公",
        location="武林主城",
        npc_type="特殊商人",
        goods=["神奇盒子"],  # 只能银两购买
        buy_items=[],
        dialogue="南无阿弥陀佛，贫僧有神奇宝物"
    )
}
```

### 12. 游戏常量配置

```python
# 物品配置
ITEM_CONFIG = {
    # 货币配置
    "currencies": {
        "gold": {"name": "金币", "exchange_rate": 1.0, "description": "基础货币"},
        "silver": {"name": "银两", "exchange_rate": 10000.0, "description": "高级货币"}
    },
    
    # 材料配置
    "materials": {
        # 基础材料
        "mulberry_leaf": {"name": "桑叶", "type": "basic", "value": 5, "usage": ["制作", "交易"]},
        "willow_branch": {"name": "柳枝", "type": "basic", "value": 3, "usage": ["制作"]},
        "lotus_leaf": {"name": "荷叶", "type": "basic", "value": 8, "usage": ["制作", "药品"]},
        "frog_oil": {"name": "蛙油", "type": "basic", "value": 12, "usage": ["药品"]},
        
        # 强化材料
        "silk": {"name": "天蚕丝", "type": "enhance", "value": 100, "usage": ["装备升品"]},
        "rain_stone": {"name": "雨花石", "type": "enhance", "value": 500, "usage": ["装备升品"]},
        "meteor_stone": {"name": "天外陨石", "type": "enhance", "value": 2000, "usage": ["装备升品"]},
        "sun_moon": {"name": "日月同辉", "type": "special", "value": 1000, "usage": ["玉佩浣灵"]},
        
        # 特殊材料
        "dragon_pearl": {"name": "龙珠", "type": "special", "value": 5000, "usage": ["高级制作"]},
        "relic": {"name": "舍利子", "type": "special", "value": 3000, "usage": ["佛系装备"]}
    },
    
    # 消耗品配置
    "consumables": {
        # 回血药品
        "small_heal": {"name": "小还丹", "effect_type": "health", "effect_value": 100, "duration": 0, "cooldown": 5, "value": 50},
        "medium_heal": {"name": "大还丹", "effect_type": "health", "effect_value": 500, "duration": 0, "cooldown": 10, "value": 200},
        "large_heal": {"name": "超级还丹", "effect_type": "health", "effect_value": 1000, "duration": 0, "cooldown": 15, "value": 500},
        
        # 回魔药品
        "small_mana": {"name": "回魔丹", "effect_type": "mana", "effect_value": 100, "duration": 0, "cooldown": 5, "value": 50},
        "medium_mana": {"name": "法力药水", "effect_type": "mana", "effect_value": 300, "duration": 0, "cooldown": 10, "value": 150},
        "large_mana": {"name": "仙露", "effect_type": "mana", "effect_value": 500, "duration": 0, "cooldown": 15, "value": 300},
        
        # 属性药品
        "strength_pill": {"name": "力量丹", "effect_type": "attack_buff", "effect_value": 10, "duration": 1800, "cooldown": 60, "value": 100},
        "defense_charm": {"name": "防御符", "effect_type": "defense_buff", "effect_value": 10, "duration": 1800, "cooldown": 60, "value": 100},
        
        # 特殊药品
        "antidote": {"name": "解毒丹", "effect_type": "cure_poison", "effect_value": 1, "duration": 0, "cooldown": 30, "value": 80},
        "revival_pill": {"name": "复活丹", "effect_type": "revival", "effect_value": 1, "duration": 0, "cooldown": 300, "value": 1000}
    },
    
    # 技能书配置
    "skill_books": {
        # 剑客技能书
        "sword_break": {"name": "破军剑法", "skill_id": "sword_break", "profession": "剑客", "level_required": 10, "success_rate": 0.8, "value": 500},
        "mirror_shield": {"name": "镜面令", "skill_id": "mirror_shield", "profession": "剑客", "level_required": 20, "success_rate": 0.7, "value": 800},
        "sword_fly": {"name": "御剑术", "skill_id": "sword_fly", "profession": "剑客", "level_required": 30, "success_rate": 0.6, "value": 1200},
        
        # 仙师技能书
        "thunder_spell": {"name": "玄雷术", "skill_id": "thunder_spell", "profession": "仙师", "level_required": 10, "success_rate": 0.8, "value": 500},
        "pearl_charm": {"name": "珠钗令", "skill_id": "pearl_charm", "profession": "仙师", "level_required": 20, "success_rate": 0.7, "value": 800},
        "mana_restore": {"name": "回法术", "skill_id": "mana_restore", "profession": "仙师", "level_required": 25, "success_rate": 0.7, "value": 1000},
        
        # 圣僧技能书
        "buddha_palm": {"name": "金刚掌", "skill_id": "buddha_palm", "profession": "圣僧", "level_required": 10, "success_rate": 0.8, "value": 500},
        "golden_body": {"name": "金刚护体", "skill_id": "golden_body", "profession": "圣僧", "level_required": 20, "success_rate": 0.7, "value": 800},
        "great_mercy": {"name": "大悲咒", "skill_id": "great_mercy", "profession": "圣僧", "level_required": 25, "success_rate": 0.7, "value": 1000}
    }
}

# 职业特点配置
PROFESSION_FEATURES = {
    "剑客": {
        "main_attack": "physical",
        "high_defense": "physical", 
        "low_defense": ["magic", "buddha"],
        "description": "物理攻击高、物理防御高，法防低、佛防低"
    },
    "仙师": {
        "main_attack": "magic",
        "high_defense": "magic",
        "low_defense": ["physical", "buddha"], 
        "description": "法术攻击高、法术防御高，物防低、佛防低"
    },
    "圣僧": {
        "main_attack": "buddha",
        "high_defense": "buddha",
        "low_defense": ["physical", "magic"],
        "description": "佛法攻击高、佛法防御高，物防低、法防低"
    }
}

# 装备品级和强化配置
EQUIPMENT_GRADES = {
    "1-3品": {"bonus_rate": 0.005, "materials": {"金币": "1000*(品数*品数)", "天蚕丝": "品数*品数"}},
    "4-6品": {"bonus_rate": 0.01, "materials": {"金币": "10000*(品数*品数)", "天蚕丝": "10*(品数*品数)", "雨花石": "品数*品数"}},
    "7-9品": {"bonus_rate": 0.02, "materials": {"银两": "5*品数", "天蚕丝": "50*(品数*品数)", "雨花石": "10*(品数*品数)"}},
    "10品以上": {"bonus_rate": 0.03, "materials": {"银两": "10*(品数*2)", "天蚕丝": "100*(品数*品数)", "雨花石": "50*(品数*品数)", "天外陨石": "品数"}}
}

# 套装装备配置
EQUIPMENT_SETS = {
    "游湖套装": {"dropped_by": ["柳树精", "水鬼", "孤狼"], "set_bonus": "物理攻击+10%"},
    "湖光套装": {"dropped_by": ["青蛙精", "花妖"], "set_bonus": "法术攻击+10%"},
    "印月套装": {"dropped_by": ["游魂", "游僧"], "set_bonus": "佛法攻击+10%"},
    "岳王套装": {"dropped_by": ["金刚力士"], "set_bonus": "物理防御+15%"},
    "灵隐套装": {"dropped_by": ["乌龟精"], "set_bonus": "佛法防御+15%"},
    "白蛇套装": {"dropped_by": ["螃蟹精"], "set_bonus": "法术防御+15%"}
}

# PVP死亡惩罚配置
PVP_DEATH_PENALTY = {
    "experience_loss_rate": 0.05,  # 死亡扣除5%经验
    "coin_transfer_rate": 0.10,    # 攻击者获得死者10%金币
    "equipment_drop_rate": 0.20,   # 20%概率掉落装备
    "red_name_threshold": 3,       # 连杀3人变红名
    "red_name_penalty_multiplier": 2.0  # 红名死亡惩罚翻倍
}

# 交易系统配置
TRADE_CONFIG = {
    "request_expire_minutes": 5,   # 交易请求5分钟过期
    "max_pending_trades": 3,       # 每人最多3个待处理交易
    "trade_tax_rate": 0.05,        # 交易税率5%
    "min_trade_level": 3           # 3级以上才能交易
}

# 组队系统配置
TEAM_CONFIG = {
    "invite_expire_minutes": 3,    # 组队邀请3分钟过期
    "max_team_size": 5,            # 队伍最大5人
    "experience_share_range": 50,  # 经验分享范围50米
    "team_bonus_rate": 0.1         # 组队经验加成10%
}
```

## 错误处理

### 1. 异常类型定义

```python
class GameException(Exception):
    """游戏异常基类"""
    pass

class CharacterNotFoundException(GameException):
    """角色未找到异常"""
    pass

class InsufficientResourcesException(GameException):
    """资源不足异常"""
    pass

class InvalidCommandException(GameException):
    """无效命令异常"""
    pass

class BattleException(GameException):
    """战斗异常"""
    pass
```

### 2. 错误处理策略

- **用户输入错误**：返回友好的错误提示和帮助信息
- **系统异常**：记录详细日志，返回通用错误信息
- **数据库错误**：实现重试机制，确保数据一致性
- **并发冲突**：使用锁机制防止数据竞争

### 3. 日志记录

```python
# 使用loguru进行结构化日志记录
logger.info(f"玩家 {user_id} 创建角色 {character_name}")
logger.warning(f"玩家 {user_id} 尝试使用无效命令: {command}")
logger.error(f"数据库操作失败: {error_msg}")
```

## 测试策略

### 1. 单元测试

- 每个系统组件的核心功能测试
- 数据模型的验证测试
- 命令解析器的边界条件测试

### 2. 集成测试

- 插件与机器人框架的集成测试
- 数据库操作的集成测试
- 多系统协作的端到端测试

### 3. 性能测试

- 并发用户场景测试
- 数据库查询性能测试
- 内存使用情况监控

### 4. 用户体验测试

- 命令响应时间测试
- 错误信息友好性测试
- 新手引导流程测试

## 部署和配置

### 1. 配置文件结构

```toml
[game]
name = "西子江湖"
version = "1.0.0"
max_players_per_room = 100
battle_timeout = 300

[database]
file_path = "mir2_game.db"
backup_interval = 3600

[economy]
initial_coins = 1000
silver_exchange_rate = 10000
daily_bonus = 100

[battle]
profession_bonus = 1.2
critical_chance = 0.1
dodge_chance = 0.05

[maps]
default_map = "新手村"
respawn_map = "新手村"
```

### 2. 数据库初始化

**数据库表结构：**

```sql
-- 角色表
CREATE TABLE characters (
    user_id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    profession VARCHAR(10) NOT NULL,
    level INTEGER DEFAULT 1,
    experience INTEGER DEFAULT 0,
    base_health INTEGER DEFAULT 100,
    base_mana INTEGER DEFAULT 50,
    base_attack INTEGER DEFAULT 10,
    base_defense INTEGER DEFAULT 5,
    base_agility INTEGER DEFAULT 5,
    current_health INTEGER DEFAULT 100,
    current_mana INTEGER DEFAULT 50,
    current_map VARCHAR(50) DEFAULT '新手村',
    coins INTEGER DEFAULT 1000,
    silver INTEGER DEFAULT 0,
    is_alive BOOLEAN DEFAULT TRUE,
    red_name_kills INTEGER DEFAULT 0,
    last_battle_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 装备表
CREATE TABLE equipments (
    id VARCHAR(255) PRIMARY KEY,
    owner_id VARCHAR(255),
    name VARCHAR(50) NOT NULL,
    type VARCHAR(20) NOT NULL,
    level_required INTEGER DEFAULT 1,
    rarity VARCHAR(10) DEFAULT '普通',
    attack_bonus INTEGER DEFAULT 0,
    defense_bonus INTEGER DEFAULT 0,
    agility_bonus INTEGER DEFAULT 0,
    durability INTEGER DEFAULT 100,
    max_durability INTEGER DEFAULT 100,
    enhancement_level INTEGER DEFAULT 0,
    is_equipped BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES characters(user_id)
);

-- 背包物品表
CREATE TABLE inventory_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    owner_id VARCHAR(255),
    item_name VARCHAR(50) NOT NULL,
    item_type VARCHAR(20) NOT NULL,
    quantity INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES characters(user_id)
);

-- 技能表
CREATE TABLE character_skills (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    character_id VARCHAR(255),
    skill_name VARCHAR(50) NOT NULL,
    skill_level INTEGER DEFAULT 1,
    last_used TIMESTAMP,
    learned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (character_id) REFERENCES characters(user_id)
);

-- 战斗记录表
CREATE TABLE battle_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    battle_type VARCHAR(10) NOT NULL,
    attacker_id VARCHAR(255),
    defender_id VARCHAR(255),
    winner_id VARCHAR(255),
    experience_gained INTEGER DEFAULT 0,
    coins_gained INTEGER DEFAULT 0,
    items_dropped TEXT,
    battle_log TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 游戏模式状态表
CREATE TABLE game_sessions (
    user_id VARCHAR(255) PRIMARY KEY,
    room_id VARCHAR(255) NOT NULL,
    is_in_game_mode BOOLEAN DEFAULT FALSE,
    last_command VARCHAR(100),
    session_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 交易请求表
CREATE TABLE trade_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    requester_id VARCHAR(255) NOT NULL,
    target_id VARCHAR(255) NOT NULL,
    room_id VARCHAR(255) NOT NULL,
    item_name VARCHAR(50) NOT NULL,
    quantity INTEGER DEFAULT 1,
    coins_offered INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending',  -- pending/accepted/rejected/expired
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (datetime('now', '+5 minutes'))
);

-- 组队邀请表
CREATE TABLE team_invites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    inviter_id VARCHAR(255) NOT NULL,
    invitee_id VARCHAR(255) NOT NULL,
    room_id VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',  -- pending/accepted/rejected/expired
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (datetime('now', '+3 minutes'))
);

-- 队伍表
CREATE TABLE teams (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    leader_id VARCHAR(255) NOT NULL,
    room_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 队伍成员表
CREATE TABLE team_members (
    team_id INTEGER,
    member_id VARCHAR(255),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (team_id, member_id),
    FOREIGN KEY (team_id) REFERENCES teams(id),
    FOREIGN KEY (member_id) REFERENCES characters(user_id)
);
```

**初始化数据：**
- 自动创建游戏相关数据表
- 初始化基础游戏数据（地图、怪物、装备模板等）
- 提供数据迁移和备份机制

### 3. 监控和维护

- 游戏数据统计和分析
- 性能监控和告警
- 定期数据清理和优化
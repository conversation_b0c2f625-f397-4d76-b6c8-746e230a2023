# 需求文档

## 介绍

西子江湖游戏插件是一个基于微信机器人的文字RPG游戏插件。该插件将在微信群聊中提供完整的江湖武侠游戏体验，游戏以杭州西湖的历史文化为背景，融合了白蛇传、雷峰塔等传统故事元素，玩家通过文字命令进行游戏操作，体验升级、战斗、社交等丰富玩法。游戏采用纯文字交互模式，无需图形界面，通过文字描述和emoji表情增强游戏体验。

## 需求

### 需求1：游戏模式管理

**用户故事：** 作为玩家，我希望能够进入和退出游戏模式，以便在群聊中专注于游戏操作。

#### 验收标准

1. 当玩家发送"!游戏模式"命令时，系统应将该玩家设置为游戏状态
2. 当玩家处于游戏模式时，系统应识别不带"!"前缀的游戏命令
3. 当玩家发送"!退出游戏模式"命令时，系统应将该玩家退出游戏状态
4. 当玩家未处于游戏模式时，系统应忽略不带"!"前缀的游戏命令

### 需求2：角色创建与管理

**用户故事：** 作为新玩家，我希望能够创建角色并选择职业，以便开始游戏体验。

#### 验收标准

1. 当新玩家首次进入游戏时，系统应提示创建角色
2. 当玩家创建角色时，系统应提供三种职业选择（剑客、仙师、圣僧）
3. 当角色创建完成时，系统应为角色分配初始属性和装备，并出生在武林主城
4. 当玩家使用"查看状态"命令时，系统应显示角色等级、职业、属性、装备、位置等信息
5. 当角色达到不同等级段位时，系统应更新角色称号（初出茅庐→江湖新手→江湖有名→武林高手→西湖神话）

### 需求3：战斗系统

**用户故事：** 作为玩家，我希望能够与怪物和其他玩家战斗，以便获得经验和装备。

#### 验收标准

1. 当玩家使用"攻击 怪物名"命令时，系统应启动PVE战斗流程
2. 当玩家使用"攻击 @玩家"命令时，系统应启动PVP战斗流程
3. 当战斗进行时，系统应根据职业克制关系计算伤害（剑客物攻高、仙师法攻高、圣僧佛攻高）
4. 当战斗结束时，系统应分配经验值和掉落物品给获胜方
5. 当玩家死亡时，系统应扣除部分经验值，攻击者获得死者10%的金币和江湖恶名
6. 当玩家使用"施 技能名 目标"命令时，系统应消耗相应的法气值

### 需求3.1：怪物系统

**用户故事：** 作为玩家，我希望遇到各种不同的怪物，以便体验丰富的战斗。

#### 验收标准

1. 系统应包含第一阶段怪物：柳树精、青蛙精、水鬼、游魂、孤狼、花妖、游僧（初出茅庐等级）
2. 系统应包含第二阶段怪物：法海、金刚力士、乌龟精、螃蟹精（江湖新手到武林高手等级）
3. 系统应包含第三阶段怪物：螃蟹精、黑鱼精、蛟龙boss（西湖神话等级）
4. 每种怪物应有不同的攻击类型：物攻、法攻、佛攻或三系攻击
5. 怪物应有特定的掉落物品：金币、套装装备、技能书、材料

### 需求4：地图探索系统

**用户故事：** 作为玩家，我希望能够在不同地图间移动和探索，以便发现新的怪物和资源。

#### 验收标准

1. 当玩家使用"前往 地图名"命令时，系统应检查地图连通性并更新玩家位置
2. 当玩家使用"探索"命令时，系统应随机生成怪物遭遇或发现宝箱
3. 当玩家使用"查看地图"命令时，系统应显示当前地图的ASCII布局和可前往的地图
4. 当玩家进入新地图时，系统应显示地图描述、等级范围和主要怪物类型
5. 当玩家等级不足时，系统应阻止进入高级地图并给出提示

### 需求4.1：具体地图内容

**用户故事：** 作为玩家，我希望游戏有丰富的地图内容，以便体验不同的冒险。

#### 验收标准

1. 系统应包含基础地图：武林主城（交易NPC、出生地）、苏堤、断桥、孤山、太子湾、保俶山、梅家坞、龙井村
2. 系统应包含副本地图：雷峰塔底、湖心亭（需要特定等级才能进入）
3. 每个地图应有独特的功能：钓鱼（苏堤、断桥）、采矿（保俶山）、采桑（梅家坞）、采茶（龙井村）
4. 每个地图应有特定的怪物分布和掉落的套装装备
5. 副本地图应有boss怪物和更好的装备掉落

### 需求5：装备系统

**用户故事：** 作为玩家，我希望能够获得、装备和强化装备，以便提升角色实力。

#### 验收标准

1. 当玩家使用"装备 装备名"命令时，系统应装备物品并更新角色属性
2. 当玩家使用"卸下 装备名"命令时，系统应卸下装备并恢复基础属性
3. 当玩家使用"强化 装备名"命令时，系统应消耗强化石和金币进行装备强化
4. 当装备强化成功时，系统应提升装备的攻击力/防御力属性
5. 当装备强化失败时，系统应有30%概率损坏装备

### 需求5.1：具体装备内容

**用户故事：** 作为玩家，我希望游戏有丰富的装备系统，以便个性化角色发展。

#### 验收标准

1. 系统应包含8个装备位置：武器、头冠、玉佩、衣服、护手、护膝、裤子、鞋子
2. 系统应包含套装装备：游湖套装、湖光套装、印月套装、岳王套装、灵隐套装、白蛇套装
3. 装备应有品级系统：1-3品（基础）、4-6品（进阶）、7-9品（高级）、10品以上（顶级）
4. 系统应支持装备升品：消耗金币/银两、天蚕丝、雨花石、天外陨石等材料
5. 玉佩应支持浣灵系统：使用日月同辉材料洗练灵力属性

### 需求6：技能系统

**用户故事：** 作为玩家，我希望能够学习和使用技能，以便在战斗中获得优势。

#### 验收标准

1. 当玩家使用"学习技能 技能名"命令时，系统应检查等级要求和消耗金币
2. 当玩家使用"技能 技能名 目标"命令时，系统应检查魔法值和冷却时间
3. 当技能释放成功时，系统应产生相应的战斗效果和伤害
4. 当技能处于冷却时，系统应提示剩余冷却时间

### 需求6.1：具体技能内容

**用户故事：** 作为玩家，我希望每个职业都有独特的技能，以便体验不同的战斗风格。

#### 验收标准

1. 剑客技能应包含：破军剑法（物攻主攻）、镜面令（反弹伤害50%）、御剑术（极速行走）
2. 仙师技能应包含：玄雷术（法攻主攻）、珠钗令（破防辅助）、回法术（回法气）
3. 圣僧技能应包含：金刚掌（佛攻主攻）、金刚护体（提升防御）、大悲咒（加血气）
4. 技能应有不同的冷却时间：主攻技能15秒、辅助技能20秒、特色技能30秒
5. 技能应有不同的法气消耗：主攻技能20点、辅助技能30点、特色技能50点

### 需求7：经济系统

**用户故事：** 作为玩家，我希望能够使用金币和银两进行交易，以便获得所需物品。

#### 验收标准

1. 当玩家完成任务或战斗时，系统应奖励金币
2. 当玩家进行高级交易时，系统应支持银两支付（1银两=10000金币）
3. 当玩家之间交易时，系统应确保交易的安全性和公平性
4. 当玩家购买物品时，系统应扣除相应货币并添加物品

### 需求8：社交互动系统

**用户故事：** 作为玩家，我希望能够与其他玩家互动，以便增强游戏的社交体验。

#### 验收标准

1. 当玩家使用"交易 @玩家 物品名 数量"命令时，系统应向目标玩家发送交易请求通知
2. 当被邀请玩家使用"接受交易"命令时，系统应完成物品和金币的交换
3. 当被邀请玩家使用"拒绝交易"命令时，系统应取消交易并通知发起方
4. 当玩家使用"组队 @玩家"命令时，系统应向目标玩家发送组队邀请通知
5. 当被邀请玩家使用"接受组队"命令时，系统应将双方加入同一队伍
6. 当被邀请玩家使用"拒绝组队"命令时，系统应取消组队邀请并通知发起方
7. 当玩家在队伍中战斗时，系统应平均分配经验值给队伍成员
8. 当玩家使用"赠送 @玩家 物品名"命令时，系统应直接转移物品所有权（无需确认）
9. 当玩家查看其他玩家信息时，系统应显示等级、职业、装备等公开信息

### 需求8.1：PVP死亡机制

**用户故事：** 作为玩家，我希望PVP战斗有合理的风险和收益，以便增加游戏的刺激性。

#### 验收标准

1. 当玩家在PVP中死亡时，系统应扣除死者当前经验值的5%
2. 当玩家击杀其他玩家时，系统应奖励攻击者死者10%的金币
3. 当玩家死亡时，系统应有20%概率掉落背包中的一件装备
4. 当玩家连续击杀多人时，系统应增加红名状态和被攻击概率
5. 当红名玩家死亡时，系统应加重惩罚（更多经验损失和装备掉落）

### 需求9：数据持久化

**用户故事：** 作为玩家，我希望我的游戏进度能够被保存，以便下次继续游戏。

#### 验收标准

1. 当玩家退出游戏时，系统应保存角色数据
2. 当玩家重新进入游戏时，系统应加载之前的游戏状态
3. 当系统重启时，系统应能够恢复所有玩家数据
4. 当数据发生变化时，系统应及时更新数据库

### 需求10：游戏帮助系统

**用户故事：** 作为新玩家，我希望能够查看游戏玩法说明和命令操作指南，以便快速了解游戏规则和操作方法。

#### 验收标准

1. 当玩家发送"!帮助"或"!游戏说明"命令时，系统应显示游戏基本玩法介绍
2. 当玩家发送"!命令"或"!指令"命令时，系统应显示所有可用命令列表
3. 当玩家发送"!职业介绍"命令时，系统应显示三大职业的特点和克制关系
4. 当玩家发送"!新手指南"命令时，系统应提供分步骤的新手教程
5. 当玩家在游戏中遇到错误操作时，系统应提供相关的帮助提示

### 需求11：游戏操作命令系统

**用户故事：** 作为玩家，我希望能够使用简单的文字命令操作游戏，以便快速进行各种游戏操作。

#### 验收标准

1. 当玩家发送"!游戏模式"时，系统应进入游戏状态，后续命令无需"!"前缀
2. 当玩家发送"查看状态"时，系统应显示角色详细信息、位置、死亡信息
3. 当玩家发送"查看背包"时，系统应显示背包物品列表
4. 当玩家发送"查看地图"时，系统应显示地图、人物分布、boss分布
5. 当玩家发送"查看装备"时，系统应显示装备属性和装备情况
6. 当玩家发送"查看排名"时，系统应显示战力、财富、恶人、降妖排行
7. 当玩家发送"前往 地点名"时，系统应移动到指定地点
8. 当玩家发送"攻击 目标"时，系统应发起战斗
9. 当玩家发送"施 技能名 目标"时，系统应释放技能攻击目标
10. 当玩家发送"服用 物品名"时，系统应使用消耗品
11. 当玩家发送"装备 物品名"时，系统应装备物品
12. 当玩家发送"升品/浣灵 装备名"时，系统应强化装备

### 需求11.1：NPC交互系统

**用户故事：** 作为玩家，我希望能够与各种NPC交互，以便购买物品和了解游戏世界。

#### 验收标准

1. 当玩家在武林主城时，系统应提供钱镠（买卖天蚕丝、桑叶）、岳飞（买卖装备）、丁仁（买卖技能书）、济公（出售神奇盒子）等NPC
2. 当玩家在断桥时，系统应提供宋五嫂（买卖鱼类）、许仙（买卖药品）等NPC
3. 当玩家使用"买/卖 物品名"命令时，系统应与当前地点的NPC进行交易
4. 当玩家使用"@玩家 邀买/邀卖"命令时，系统应发起玩家间交易

### 需求11.2：挂机系统

**用户故事：** 作为玩家，我希望能够进行挂机活动，以便在离线时也能获得收益。

#### 验收标准

1. 当玩家使用"挂机 钓鱼"命令时，系统应在苏堤或断桥开始钓鱼挂机
2. 当玩家使用"挂机 采集"命令时，系统应在保俶山、梅家坞、龙井村开始采集挂机
3. 当玩家使用"挂机 打怪"命令时，系统应在当前地图开始打怪挂机
4. 挂机状态下系统应降低收益：采集概率降为人工10%，打怪收益降为人工10%
5. 挂机状态下遇到boss时，系统应有概率被boss击杀（如果等级和防御低）

### 需求12：物品系统

**用户故事：** 作为玩家，我希望游戏有丰富的物品系统，以便获得、使用和管理各种物品。

#### 验收标准

1. 当玩家击败怪物时，系统应根据掉落配置随机掉落各种物品
2. 当玩家使用"服用 物品名"命令时，系统应消耗物品并产生相应效果
3. 当玩家查看背包时，系统应按类型分组显示所有物品
4. 当玩家获得物品时，系统应自动堆叠相同物品
5. 当玩家背包满时，系统应提示背包空间不足

### 需求12.1：虚拟货币系统

**用户故事：** 作为玩家，我希望有完整的货币系统，以便进行各种交易和消费。

#### 验收标准

1. 系统应支持金币作为基础货币，用于日常交易和消费
2. 系统应支持银两作为高级货币，用于高端交易（1银两=10000金币）
3. 当玩家战斗胜利时，系统应奖励相应的金币
4. 当玩家击败高级怪物时，系统应有概率掉落银两
5. 当玩家进行货币兑换时，系统应按固定汇率转换

### 需求12.2：装备物品系统

**用户故事：** 作为玩家，我希望能够获得各种装备，以便提升角色实力。

#### 验收标准

1. 系统应包含武器类装备：剑、杖、禅杖等，提供攻击力加成
2. 系统应包含防具类装备：头盔、衣服、护手等，提供防御力加成
3. 系统应包含饰品类装备：玉佩、护膝等，提供特殊属性加成
4. 装备应有品级系统：1-10品以上，品级越高属性越强
5. 装备应有套装效果：穿戴同套装多件装备时获得额外加成

### 需求12.3：材料物品系统

**用户故事：** 作为玩家，我希望能够收集各种材料，以便进行装备强化和制作。

#### 验收标准

1. 系统应包含基础材料：桑叶、柳枝、荷叶等，用于基础制作
2. 系统应包含强化材料：天蚕丝、雨花石、天外陨石，用于装备升品
3. 系统应包含特殊材料：日月同辉，用于玉佩浣灵
4. 系统应包含稀有材料：龙珠、舍利子等，用于高级制作
5. 材料应有不同的获取途径：怪物掉落、地图采集、NPC购买

### 需求12.4：药品消耗品系统

**用户故事：** 作为玩家，我希望能够使用各种药品，以便在战斗和探索中获得帮助。

#### 验收标准

1. 系统应包含回血药品：小还丹、大还丹等，恢复生命值
2. 系统应包含回魔药品：回魔丹、法力药水等，恢复法气值
3. 系统应包含属性药品：力量丹、防御符等，临时提升属性
4. 系统应包含特殊药品：解毒丹、复活丹等，特殊效果
5. 当玩家使用药品时，系统应立即生效并消耗物品

### 需求12.5：技能书系统

**用户故事：** 作为玩家，我希望能够通过技能书学习技能，以便掌握更多战斗技巧。

#### 验收标准

1. 系统应包含职业技能书：剑客、仙师、圣僧专用技能书
2. 系统应包含技能书等级：初级、中级、高级、大师级
3. 当玩家使用技能书时，系统应检查职业匹配和等级要求
4. 当学习成功时，系统应将技能添加到角色技能列表
5. 当学习失败时，系统应保留技能书并给出失败原因

### 需求13：命令解析系统

**用户故事：** 作为玩家，我希望系统能够准确理解我的命令，以便游戏操作顺畅。

#### 验收标准

1. 当玩家发送强命令（!开头）时，系统应立即处理
2. 当玩家在游戏模式下发送命令时，系统应解析操作词和操作对象
3. 当命令格式错误时，系统应提供帮助信息和正确格式示例
4. 当命令执行成功时，系统应返回相应的反馈信息
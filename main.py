from BotServer.MainServer import MainServer
from WebAdmin.run import startWebAdminThread
import Config.ConfigServer as Cs
from cprint import cprint
import atexit
import signal
import sys

Bot_Logo = """
     _                          _           _   
    | |                        | |         | |  
    | |__   ___  _ __ ___   ___| |__   ___ | |_ 
    | '_ \ / _ \| '_ ` _ \ / _ \ '_ \ / _ \| __|
    | | | | (_) | | | | | |  __/ |_) | (_) | |_ 
    |_| |_|\___/|_| |_| |_|\___|_.__/ \___/ \__|  
     Version: V0.1 基于ngcbot2.3修改
     Author: Fengin(凌封)                                             
     
"""

# 全局变量，用于存储Web线程
webThread = None


def shutdown(signum, frame):
    """处理程序终止信号"""
    cprint.info("正在关闭程序...")
    # Web线程是守护线程，会随主线程退出而终止，无需特别处理
    sys.exit(0)


def cleanup():
    """退出前的清理工作"""
    cprint.info("执行清理工作...")
    # 这里Web线程是daemon=True，所以会自动退出


if __name__ == '__main__':
    cprint.info(Bot_Logo)

    # 注册退出清理函数
    atexit.register(cleanup)

    # 启动Web管理后台
    configData = Cs.returnConfigData()
    webConfig = configData.get('WebAdminConfig', {})

    if webConfig.get('enabled', False):
        # 在单独的线程中启动Web管理后台
        webThread = startWebAdminThread()
        webHost = webConfig.get('webHost', '127.0.0.1')
        webPort = webConfig.get('webPort', 8081)
        cprint.info(f"Web管理后台已启动在: http://{webHost}:{webPort}")

    # 启动主服务器
    Ms = MainServer()
    signal.signal(signal.SIGINT, shutdown)
    signal.signal(signal.SIGTERM, shutdown)
    try:
        Ms.initServer()
    except KeyboardInterrupt:
        shutdown(signal.SIGINT, None)

#!/usr/bin/env python3
"""
测试导入脚本
"""
import sys
import os

print("Python版本:", sys.version)
print("当前工作目录:", os.getcwd())
print("Python路径:", sys.path[:3])

try:
    from Plugins.Mir2Game.systems.game_config_manager import get_game_config_manager
    print("✅ 导入成功: get_game_config_manager")
    
    # 测试函数调用
    manager = get_game_config_manager()
    print("✅ 函数调用成功")
    print("配置摘要:", manager.get_config_summary())
    
except ImportError as e:
    print("❌ 导入失败:", str(e))
    
    # 检查文件是否存在
    file_path = "Plugins/Mir2Game/systems/game_config_manager.py"
    if os.path.exists(file_path):
        print("✅ 文件存在:", file_path)
        
        # 检查文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'get_game_config_manager' in content:
                print("✅ 函数定义存在")
            else:
                print("❌ 函数定义不存在")
    else:
        print("❌ 文件不存在:", file_path)

except Exception as e:
    print("❌ 其他错误:", str(e))
    import traceback
    traceback.print_exc()
from Plugins import EventType, on_group_text, on_private_text, pluginManager, PluginBase
from Plugins._Tools import Tools
import Config.ConfigServer as Cs
from NGCBotApi import NGCBotApi
from loguru import logger
import traceback
import tomlkit
import time
import os


class PluginManager(PluginBase):
    """插件管理器，用于管理所有插件的加载、卸载和查询"""

    def __init__(self):
        """初始化插件管理器"""
        # 调用父类初始化方法
        super().__init__()

        # 设置插件属性
        self.name = "NGC插件管理器"
        self.description = "用于管理和控制插件的核心插件"
        self.version = "1.5.0"  # 版本号更新
        self.author = "NGC660 安全实验室"

        self.tools = Tools()

        # 获取插件管理器
        self.plugin_manager = pluginManager

        # 加载配置文件
        self.config = self.tools.returnConfigData(os.path.dirname(__file__))

        # 加载全局配置
        self.NGCBotConfig = Cs.returnConfigData().get('NGCBotConfig')
        # 设置管理员列表
        self.admin_list = self.NGCBotConfig.get('Administrators')
        # 命令前缀
        self.prefix = self.config.get('command_prefix', "/plugins")

        # 图标设置
        self.icons = self.config.get('icons')

        # 加载帮助信息模板
        self.help_template = self.config.get('help_template')
        self.help_info = self.help_template.format(prefix=self.prefix)

        # 消息前缀
        self.msg_prefixes = self.config.get('settings', {}).get('messages', {})

        # 初始化机器人API
        self.bot = NGCBotApi()

    def on_load(self) -> None:
        """注册插件管理相关事件处理函数"""
        # 调用父类方法，确保装饰器注册的处理器能够生效
        super().on_load()

        # 从配置中加载事件传播设置
        propagate_events = self.config.get('event_control', {}).get('propagate_events', True)
        self.plugin_manager.set_event_propagation(propagate_events)

        logger.success(f"{self.name} {self.version} 已加载")
        # logger.info(f"事件传播状态: {'启用' if propagate_events else '禁用'}")

    @on_group_text(priority=100)
    def handle_group_message(self, message: dict) -> None:
        """处理群聊消息"""
        try:
            # 解析消息数据
            data = message.get("data", {})
            content = data.get("content", data.get("message", ""))
            robot_id = data.get("robotId", "")
            room_id = data.get("fromUser", data.get("fromWxId", ""))
            from_user = data.get("fromMember", data.get("finalFromWxId", ""))

            # 检查是否是命令
            if content.startswith(self.prefix):
                cmd = content[len(self.prefix):].strip()
                self._process_command(cmd, robot_id, room_id, from_user, is_group=True)
        except Exception as e:
            logger.error(f"插件管理器处理群聊消息时发生错误: {e}")
            traceback.print_exc()

    @on_private_text(priority=100)
    def handle_private_message(self, message: dict) -> None:
        """处理私聊消息"""
        try:
            # 解析消息数据
            data = message.get("data", {})
            content = data.get("content", data.get("message", ""))
            robot_id = data.get("robotId", "")
            from_user = data.get("fromUser", data.get("fromWxId", ""))

            # 检查是否是命令
            if content.startswith(self.prefix):
                cmd = content[len(self.prefix):].strip()
                self._process_command(cmd, robot_id, from_user, from_user, is_group=False)
        except Exception as e:
            logger.error(f"插件管理器处理私聊消息时发生错误: {e}")
            traceback.print_exc()

    def _process_command(self, cmd, robot_id, receive, user_id, is_group=False):
        """处理插件管理命令"""
        try:
            # 解析命令和参数
            parts = cmd.split(maxsplit=1)
            command = parts[0].lower() if parts else ""
            args = parts[1] if len(parts) > 1 else ""

            # 根据命令类型处理
            if command == "help" or not command:
                self.send_message(robot_id, receive, self.help_info)

            # 查询类命令 - 任何人都可以使用
            elif command == "list":
                plugins_info = self.get_plugins_info()
                self.send_message(robot_id, receive, plugins_info)

            elif command == "all":
                plugins_info = self.get_all_plugins_info()
                self.send_message(robot_id, receive, plugins_info)

            elif command == "enabled":
                plugins_info = self.get_enabled_plugins_info()
                self.send_message(robot_id, receive, plugins_info)

            elif command == "disabled":
                # 使用已有的方法查询禁用插件
                self.get_disabled_plugins_info(robot_id, receive, args)

            elif command == "info":
                if not args:
                    self.send_message(robot_id, receive, f"{self.icons.get('error')} 请指定要查询的插件名称")
                    return
                info = self.get_plugin_info(args)
                self.send_message(robot_id, receive, info)

            # 事件控制相关命令
            elif command == "eventflow":
                # 检查权限
                if not self.check_admin(user_id):
                    error_msg = self.msg_prefixes.get('permission_denied', "您没有操作权限")
                    self.send_message(robot_id, receive, f"{self.icons.get('lock')} {error_msg}")
                    return

                # 检查是否允许通过命令修改事件传播
                allow_change = self.config.get('event_control', {}).get('allow_propagation_change', True)
                if not allow_change:
                    self.send_message(robot_id, receive, f"{self.icons.get('error')} 通过命令修改事件传播行为已被禁用")
                    return

                # 查询当前状态
                if not args:
                    current_status = self.plugin_manager.get_event_propagation()
                    status_text = "启用" if current_status else "禁用"
                    self.send_message(robot_id, receive,
                                      f"{self.icons.get('info')} 当前事件传播状态: {status_text}\n"
                                      f"使用 {self.prefix} eventflow on/off 开启/关闭事件传播")
                    return

                # 修改状态
                if args.lower() in ["on", "true", "enable", "enabled", "1", "开", "开启", "启用"]:
                    self.plugin_manager.set_event_propagation(True)
                    self.send_message(robot_id, receive,
                                      f"{self.icons.get('success')} 事件传播已启用，所有插件处理器将按优先级顺序执行")
                elif args.lower() in ["off", "false", "disable", "disabled", "0", "关", "关闭", "禁用"]:
                    self.plugin_manager.set_event_propagation(False)
                    self.send_message(robot_id, receive,
                                      f"{self.icons.get('success')} 事件传播已禁用，每个事件只由最高优先级的处理器处理")
                else:
                    self.send_message(robot_id, receive,
                                      f"{self.icons.get('error')} 无效的参数: {args}\n请使用 on 或 off")

            elif command == "priority":
                # 获取所有事件处理器及其优先级
                priorities_info = self.get_handlers_priority_info()
                self.send_message(robot_id, receive, priorities_info)

            # 管理类命令 - 需要管理员权限
            elif command in ["unload", "reload", "reloadall", "enable", "disable"]:
                if not self.check_admin(user_id):
                    error_msg = self.msg_prefixes.get('permission_denied', "您没有操作权限")
                    self.send_message(robot_id, receive, f"{self.icons.get('lock')} {error_msg}")
                    return

                if command == "unload":
                    if not args:
                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 请指定要卸载的插件名称")
                        return
                    success = self.plugin_manager.unregister_plugin(args)
                    if success:
                        success_msg = self.msg_prefixes.get('plugin_unloaded', "插件已成功卸载")
                        self.send_message(robot_id, receive, f"{self.icons.get('success')} {success_msg}: {args}")
                    else:
                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 卸载插件 {args} 失败")

                elif command == "reload":
                    if not args:
                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 请指定要重载的插件名称")
                        return

                    # 检查是文件夹名还是插件名
                    plugin_name = args
                    folder_name = None

                    # 尝试将文件夹名转换为插件名
                    real_name = self._get_real_plugin_name(args)
                    if real_name != args:
                        plugin_name = real_name
                        folder_name = args

                    # 检查是否已加载
                    if plugin_name in self.plugin_manager.plugins:
                        logger.debug(f"插件 {plugin_name} 已加载，直接重载")
                    else:
                        # 尝试查找文件夹名
                        if not folder_name:
                            for item in os.listdir("Plugins"):
                                folder_path = os.path.join("Plugins", item)
                                if not os.path.isdir(folder_path) or item.startswith('_') or item.startswith(
                                        '.') or item == "core":
                                    continue

                                item_real_name = self._get_real_plugin_name(item)
                                if item_real_name == plugin_name:
                                    folder_name = item
                                    break

                        if folder_name:
                            logger.debug(f"插件 {plugin_name} 未加载，但找到了文件夹 {folder_name}")

                    self.send_message(robot_id, receive, f"{self.icons.get('loading')} 正在重载插件 {plugin_name}...")

                    success = self.plugin_manager.reload_plugin(plugin_name)
                    if success:
                        success_msg = self.msg_prefixes.get('plugin_loaded', "插件已成功加载")
                        self.send_message(robot_id, receive,
                                          f"{self.icons.get('success')} {success_msg}: {plugin_name}")
                    else:
                        # 如果通过插件名重载失败，尝试通过文件夹名重载
                        if folder_name and folder_name != plugin_name:
                            success = self.plugin_manager.reload_plugin(folder_name)
                            if success:
                                success_msg = self.msg_prefixes.get('plugin_loaded', "插件已成功加载")
                                self.send_message(robot_id, receive,
                                                  f"{self.icons.get('success')} {success_msg}: {plugin_name} (文件夹: {folder_name})")
                                return

                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 重载插件 {plugin_name} 失败")

                elif command == "reloadunloaded":
                    if not args:
                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 请指定要重载的已卸载插件名称")
                        return
                    self.send_message(robot_id, receive, f"{self.icons.get('loading')} 正在重载已卸载的插件 {args}...")

                    # 检查插件是否在卸载列表中
                    if args not in self.plugin_manager.unloaded_plugins:
                        self.send_message(robot_id, receive,
                                          f"{self.icons.get('warning')} 插件 {args} 不在卸载列表中，尝试常规重载...")
                        success = self.plugin_manager.reload_plugin(args)
                    else:
                        # 使用新添加的方法重载已卸载的插件
                        success = self.plugin_manager.reload_unloaded_plugin(args)

                    if success:
                        success_msg = self.msg_prefixes.get('plugin_loaded', "插件已成功加载")
                        self.send_message(robot_id, receive, f"{self.icons.get('success')} {success_msg}: {args}")
                    else:
                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 重载已卸载的插件 {args} 失败")

                elif command == "reloadall":
                    # 读取配置中的设置
                    allow_reloadall = self.config.get('settings', {}).get('features', {}).get('allow_reloadall', True)
                    if not allow_reloadall:
                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 重载所有插件功能已禁用")
                        return

                    # 不处理卸载的插件，只重载已加载的插件
                    include_unloaded = False
                    msg = "所有已加载插件"

                    self.send_message(robot_id, receive, f"{self.icons.get('loading')} 正在重载{msg}...")

                    # 获取当前已加载的插件信息
                    plugins_before = self.plugin_manager.get_plugin_list()
                    non_system_plugins_before = [p for p in plugins_before
                                                 if p['name'] != "NGC插件管理器" and p['name'] != "PluginManager"]

                    # 记录被卸载的插件
                    unloaded_plugins_before = []
                    if hasattr(self.plugin_manager, 'unloaded_plugins'):
                        unloaded_plugins_before = self.plugin_manager.unloaded_plugins.copy()

                    # 记录被禁用的插件
                    disabled_plugins_before = []
                    if hasattr(self.plugin_manager, 'disabled_plugins'):
                        disabled_plugins_before = self.plugin_manager.disabled_plugins.copy()

                    # 执行重载操作
                    try:
                        count = self.plugin_manager.reload_all_plugins(include_unloaded=include_unloaded)
                    except Exception as e:
                        logger.error(f"调用reload_all_plugins时出错: {e}")
                        logger.error(traceback.format_exc())
                        count = 0

                    # 获取重载后的插件信息
                    plugins_after = self.plugin_manager.get_plugin_list()
                    non_system_plugins_after = [p for p in plugins_after
                                                if p['name'] != "NGC插件管理器" and p['name'] != "PluginManager"]

                    # 如果没有重载任何插件，提供更有用的反馈
                    if count == 0:
                        if len(non_system_plugins_before) == 0:
                            # 之前就没有非系统插件
                            self.send_message(robot_id, receive,
                                              f"{self.icons.get('info')} 没有可重载的插件\n"
                                              f"使用 {self.prefix} all 查看所有可用插件")
                            return
                        else:
                            # 之前有插件但现在没有加载
                            # 获取可能的插件信息
                            plugin_paths = []
                            enabled_count = 0
                            disabled_count = 0

                            for folder_name in os.listdir("Plugins"):
                                folder_path = os.path.join("Plugins", folder_name)
                                if not os.path.isdir(folder_path) or folder_name.startswith(
                                        '_') or folder_name.startswith(
                                    '.') or folder_name == "core" or folder_name == "PluginManager":
                                    continue

                                config_path = os.path.join(folder_path, "config.toml")
                                if os.path.exists(config_path):
                                    try:
                                        with open(config_path, 'r', encoding='utf-8') as f:
                                            config = tomlkit.parse(f.read())
                                            if config and config.get("enabled", True):
                                                enabled_count += 1
                                                plugin_paths.append(f"✅ {folder_name}")
                                            else:
                                                disabled_count += 1
                                                plugin_paths.append(f"❌ {folder_name}")
                                    except Exception as e:
                                        plugin_paths.append(f"⚠️ {folder_name} (配置读取失败)")

                            plugins_info = self.get_enabled_plugins_info()
                            error_msg = f"{self.icons.get('warning')} 重载失败，没有成功加载任何插件\n\n"

                            if include_unloaded:
                                if enabled_count > 0:
                                    error_msg += f"检测到 {enabled_count} 个插件标记为启用，但加载失败，可能存在代码错误\n"
                                elif disabled_count > 0:
                                    error_msg += f"所有 {disabled_count} 个检测到的插件都被禁用了\n"

                                if plugin_paths:
                                    error_msg += "\n插件目录状态：\n"
                                    error_msg += "\n".join(plugin_paths[:10])
                                    if len(plugin_paths) > 10:
                                        error_msg += f"\n... 及其他 {len(plugin_paths) - 10} 个插件\n"

                            error_msg += f"\n{plugins_info}"
                            self.send_message(robot_id, receive, error_msg)
                    else:
                        # 成功重载了一些插件
                        success_msg = f"{self.icons.get('success')} 已成功重载 {count} 个插件"

                        # 添加已加载插件列表
                        if non_system_plugins_after:
                            success_msg += f"\n\n已加载的插件：\n{', '.join([p['name'] for p in non_system_plugins_after])}"

                        self.send_message(robot_id, receive, success_msg)

                elif command == "enable":
                    if not args:
                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 请指定要启用的插件名称")
                        return
                    success = self.enable_plugin(args)
                    if success:
                        self.send_message(robot_id, receive,
                                          f"{self.icons.get('success')} 插件 {args} 已启用，请重载以生效")
                    else:
                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 启用插件 {args} 失败")

                elif command == "disable":
                    if not args:
                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 请指定要禁用的插件名称")
                        return
                    success = self.disable_plugin(args)
                    if success:
                        self.send_message(robot_id, receive, f"{self.icons.get('success')} 插件 {args} 已禁用")
                    else:
                        self.send_message(robot_id, receive, f"{self.icons.get('error')} 禁用插件 {args} 失败")

            # 未知命令
            else:
                self.send_message(robot_id, receive,
                                  f"{self.icons.get('error')} 未知命令: {command}\n请使用 {self.prefix} help 查看帮助")

        except Exception as e:
            logger.error(f"处理命令时出错: {e}")
            self.send_message(robot_id, receive, f"{self.icons.get('error')} 处理命令时出错: {e}")
            traceback.print_exc()

    def check_admin(self, user_id: str) -> bool:
        """检查用户是否是管理员"""
        # 如果管理员列表为空，则所有人都是管理员（防止配置错误导致无法管理）
        if not self.admin_list:
            logger.warning("管理员列表为空，允许所有用户执行管理操作")
            return True
        return user_id in self.admin_list

    def send_message(self, robot_id: str, receive: str, message: str) -> None:
        """发送消息并添加时间戳"""
        try:
            # 添加时间戳
            timestamp = time.strftime("%H:%M:%S", time.localtime())
            footer = f"\n\n{self.icons.get('time')} {timestamp}"
            self.bot.sendText(robotId=robot_id, receive=receive, message=message.strip() + footer)
        except Exception as e:
            logger.error(f"发送消息时出错: {e}")
            traceback.print_exc()

    def get_plugins_info(self) -> str:
        """获取已加载插件的基本信息"""
        try:
            plugins = self.plugin_manager.get_plugin_list()
            if not plugins:
                return f"{self.icons.get('info')} 当前没有已加载的插件"

            # 过滤掉已禁用的插件
            filtered_plugins = []
            for plugin in plugins:
                # 跳过状态为禁用的插件
                status = plugin.get('status', '')
                description = plugin.get('description', '')
                if status == '已禁用' or status == 'disabled' or '插件已禁用' in description:
                    continue
                filtered_plugins.append(plugin)

            if not filtered_plugins:
                return f"{self.icons.get('info')} 当前没有已加载的插件"

            info_text = f"{self.icons.get('list')} 已加载的插件列表\n"
            info_text += "┌──────────────────────┐\n"

            for i, plugin in enumerate(filtered_plugins, 1):
                info_text += f"│ {i}. {self.icons.get('plugin')} {plugin['name']} - v{plugin['version']}\n"
                info_text += f"│    {plugin['description'][:40]}{'...' if len(plugin['description']) > 40 else ''}\n"

            info_text += "└──────────────────────┘\n"
            info_text += f"\n{self.icons.get('info')} 使用 {self.prefix} info <插件名> 可查看插件详情"
            info_text += f"\n{self.icons.get('info')} 使用 {self.prefix} all 可查看所有插件（包括已卸载的）"
            info_text += f"\n{self.icons.get('info')} 使用 {self.prefix} disabled 可查看已禁用的插件"
            return info_text
        except Exception as e:
            logger.error(f"获取插件信息时出错: {e}")
            traceback.print_exc()
            return f"{self.icons.get('error')} 获取插件信息时出错: {e}"

    def get_all_plugins_info(self) -> str:
        """获取所有插件的信息，包括未加载的插件"""
        try:
            # 尝试使用include_unloaded参数获取所有插件
            try:
                plugins = self.plugin_manager.get_plugin_list(include_unloaded=True)
            except TypeError:
                # 如果参数不支持，则使用旧方法并手动添加未加载和禁用插件的信息
                loaded_plugins = self.plugin_manager.get_plugin_list()

                # 初始化插件列表
                plugins = []
                loaded_names = []

                # 添加已加载插件
                for plugin in loaded_plugins:
                    plugin["status"] = "已加载"
                    plugins.append(plugin)
                    loaded_names.append(plugin["name"])

                # 扫描插件目录查找未加载的插件
                try:
                    for folder_name in os.listdir("Plugins"):
                        folder_path = os.path.join("Plugins", folder_name)

                        # 跳过非目录和特殊目录
                        if not os.path.isdir(folder_path) or folder_name.startswith('_') or folder_name.startswith(
                                '.') or folder_name == "core":
                            continue

                        # 获取插件的真实名称（name属性），而不是类名或文件夹名
                        real_name = self._get_real_plugin_name(folder_name)

                        # 跳过已加载的插件（使用真实名称比较）
                        if real_name in loaded_names:
                            continue

                        # 检查配置文件确定插件状态
                        status = "未加载"
                        config = self._load_plugin_config(folder_path)

                        # 检查是否禁用
                        if not config.get("enabled", True):
                            status = "已禁用"

                        # 添加未加载的插件
                        plugins.append({
                            "name": real_name,
                            "path": folder_path,
                            "status": status,
                            "folder": folder_name  # 添加文件夹名，便于后续处理
                        })
                except Exception as e:
                    logger.error(f"扫描插件目录时出错: {e}")

            if not plugins:
                return f"{self.icons.get('info')} 未找到任何插件信息"

            # 读取配置设置
            show_unloaded = self.config.get('settings', {}).get('features', {}).get('show_unloaded_plugins', True)

            # 按状态对插件进行分类，同时确保同名插件不会重复出现在不同分类
            loaded_plugins = []
            unloaded_plugins = []
            disabled_plugins = []

            # 创建已处理的插件名称集合，用于过滤
            processed_names = set()

            # 首先处理已禁用的插件，优先级最高
            for plugin in plugins:
                if not plugin.get("status"):
                    plugin["status"] = "未知"

                plugin_name = plugin.get('name')
                if (plugin.get('status') == '已禁用' or plugin.get(
                        'status') == 'disabled') and plugin_name not in processed_names:
                    disabled_plugins.append(plugin)
                    processed_names.add(plugin_name)

            # 接着处理已加载的插件，但排除已经归类为禁用的插件
            for plugin in plugins:
                plugin_name = plugin.get('name')
                # 跳过已处理的插件和禁用的插件
                if plugin_name in processed_names:
                    continue

                # 检查插件是否禁用 - 检查状态和描述
                status = plugin.get('status', '')
                description = plugin.get('description', '')
                if status == '已禁用' or status == 'disabled' or '插件已禁用' in description:
                    # 如果是禁用的插件，添加到禁用列表
                    disabled_plugins.append(plugin)
                    processed_names.add(plugin_name)
                    continue

                if plugin.get('status') == '已加载' or plugin.get('status') == 'enabled':
                    loaded_plugins.append(plugin)
                    processed_names.add(plugin_name)

            # 最后处理未加载插件（非禁用、非已加载）
            for plugin in plugins:
                plugin_name = plugin.get('name')
                if plugin_name not in processed_names:
                    unloaded_plugins.append(plugin)
                    processed_names.add(plugin_name)

            info_text = f"{self.icons.get('list')} 所有插件列表\n"

            # 显示已加载的插件
            if loaded_plugins:
                info_text += f"\n{self.icons.get('enabled')} 已加载插件\n"
                info_text += "┌──────────────────────┐\n"
                for i, plugin in enumerate(loaded_plugins, 1):
                    info_text += f"│ {i}. {self.icons.get('plugin')} {plugin['name']} - v{plugin.get('version', '未知')}\n"
                    desc = plugin.get('description', '未知')[:35]
                    info_text += f"│    {desc}{'...' if len(desc) > 35 else ''}\n\n"
                info_text += "└──────────────────────┘\n"

            # 显示已禁用的插件 - 单独分类显示，使用特定格式
            if disabled_plugins:
                info_text += f"\n{self.icons.get('disabled')} 已禁用插件\n"
                info_text += "┌──────────────────────┐\n"
                for i, plugin in enumerate(disabled_plugins, 1):
                    plugin_name = plugin['name']
                    folder_name = plugin.get('folder', plugin_name)

                    # 如果文件夹名与插件名不同，显示文件夹名
                    if folder_name != plugin_name:
                        info_text += f"│ {i}. {plugin_name} (文件夹: {folder_name})\n"
                    else:
                        info_text += f"│ {i}. {plugin_name}\n"
                info_text += "└──────────────────────┘\n"

            # 显示未加载的插件（不包括已禁用和已加载的）
            if unloaded_plugins and show_unloaded:
                info_text += f"\n{self.icons.get('unloaded')} 未加载插件\n"
                info_text += "┌──────────────────────┐\n"
                for i, plugin in enumerate(unloaded_plugins, 1):
                    plugin_name = plugin['name']
                    folder_name = plugin.get('folder', plugin_name)

                    # 如果文件夹名与插件名不同，显示文件夹名
                    if folder_name != plugin_name:
                        info_text += f"│ {i}. {plugin_name} (文件夹: {folder_name})\n"
                    else:
                        info_text += f"│ {i}. {plugin_name}\n"
                info_text += "└──────────────────────┘\n"

            # 添加提示信息
            info_text += f"\n{self.icons.get('info')} 使用以下命令管理插件:"
            info_text += f"\n{self.prefix} info <插件名>  - 查看插件详情"
            info_text += f"\n{self.prefix} reload <插件名> - 重载插件"
            info_text += f"\n{self.prefix} enable/disable <插件名> - 启用/禁用插件"

            return info_text
        except Exception as e:
            logger.error(f"获取所有插件信息时出错: {e}")
            traceback.print_exc()
            return f"{self.icons.get('error')} 获取所有插件信息时出错: {e}"

    def _load_plugin_config(self, folder_path):
        """加载插件TOML配置文件

        Args:
            folder_path: 插件文件夹路径

        Returns:
            dict: 配置信息，如果文件不存在则返回默认配置
        """
        # 尝试加载TOML配置
        config_path_toml = os.path.join(folder_path, "config.toml")
        if os.path.exists(config_path_toml):
            try:
                with open(config_path_toml, 'r', encoding='utf-8') as f:
                    config = tomlkit.load(f)
                    return dict(config) if config else {}
            except Exception as e:
                logger.error(f"加载TOML配置文件 {config_path_toml} 失败: {e}")

        # 如果配置文件不存在，返回默认配置
        return {"enabled": True, "settings": {}}

    def get_enabled_plugins_info(self) -> str:
        """获取已启用插件的信息"""
        try:
            # 使用兼容方式获取已启用插件
            try:
                # 尝试调用支持get_enabled_plugins方法的新版本
                plugins = self.plugin_manager.get_enabled_plugins()
            except AttributeError:
                # 旧版本不支持get_enabled_plugins方法，使用替代方法
                logger.warning("当前插件管理器版本不支持get_enabled_plugins方法，使用替代方法")
                plugins = []

                # 获取已加载的插件
                loaded_plugins = self.plugin_manager.get_plugin_list()
                for plugin in loaded_plugins:
                    # 检查插件是否是禁用的
                    status = plugin.get('status', '')
                    description = plugin.get('description', '')
                    if status == '已禁用' or status == 'disabled' or '插件已禁用' in description:
                        continue  # 跳过禁用的插件
                    plugin["status"] = "已加载"
                    plugins.append(plugin)

                # 尝试扫描插件目录获取已启用但未加载的插件
                try:
                    for folder_name in os.listdir("Plugins"):
                        folder_path = os.path.join("Plugins", folder_name)

                        # 跳过非目录和特殊目录
                        if not os.path.isdir(folder_path) or folder_name.startswith('_') or folder_name.startswith(
                                '.') or folder_name == "core":
                            continue

                        # 检查配置文件
                        config = self._load_plugin_config(folder_path)

                        # 检查是否启用
                        if config.get("enabled", True):
                            # 检查插件是否已加载
                            already_loaded = False
                            for p in loaded_plugins:
                                if p["name"] == folder_name:
                                    already_loaded = True
                                    break

                            if not already_loaded:
                                # 获取插件的真实名称
                                real_name = self._get_real_plugin_name(folder_name)

                                plugins.append({
                                    "name": real_name,
                                    "path": folder_path,
                                    "status": "已启用但未加载",
                                    "folder": folder_name  # 保存文件夹名，以便后续使用
                                })
                except Exception as e:
                    logger.error(f"扫描插件目录时出错: {e}")
                    logger.error(traceback.format_exc())

            if not plugins:
                return f"{self.icons.get('info')} 当前没有已启用的插件"

            info_text = f"{self.icons.get('list')} 已启用的插件列表\n"
            loaded_plugins = []
            not_loaded_plugins = []

            for plugin in plugins:
                if not plugin.get("status"):
                    plugin["status"] = "未知"

                if plugin.get('status') == '已加载':
                    loaded_plugins.append(plugin)
                else:
                    not_loaded_plugins.append(plugin)

            # 显示已加载的插件
            if loaded_plugins:
                info_text += f"\n{self.icons.get('enabled')} 已加载的插件\n"
                info_text += "┌──────────────────────┐\n"
                for i, plugin in enumerate(loaded_plugins, 1):
                    info_text += f"│ {i}. {self.icons.get('plugin')} {plugin['name']} - v{plugin.get('version', '未知')}\n"
                    desc = plugin.get('description', '未知')[:35]
                    info_text += f"│    {desc}{'...' if len(desc) > 35 else ''}\n"
                info_text += "└──────────────────────┘\n"

            # 显示已启用但未加载的插件
            if not_loaded_plugins:
                info_text += f"\n{self.icons.get('info')} 已启用但未加载的插件\n"
                info_text += "┌──────────────────────┐\n"
                for i, plugin in enumerate(not_loaded_plugins, 1):
                    plugin_name = plugin['name']
                    folder_name = plugin.get('folder', '')
                    # 如果有文件夹信息且与名称不同，则显示两者
                    if folder_name and folder_name != plugin_name:
                        info_text += f"│ {i}. {plugin_name} (文件夹: {folder_name})\n"
                    else:
                        info_text += f"│ {i}. {plugin_name}\n"
                info_text += "└──────────────────────┘\n"

            info_text += f"\n{self.icons.get('info')} 使用 {self.prefix} reload <插件名> 可加载已启用但未加载的插件"
            return info_text
        except Exception as e:
            logger.error(f"获取已启用插件信息时出错: {e}")
            logger.error(traceback.format_exc())
            return f"{self.icons.get('error')} 获取已启用插件信息时出错: {e}"

    def get_plugin_info(self, plugin_name: str) -> str:
        """获取特定插件的详细信息"""
        try:
            # 获取插件
            plugin = self.plugin_manager.get_plugin(plugin_name)
            if not plugin:
                # 如果找不到插件，尝试查找名称包含关键词的插件
                info_text = f"{self.icons.get('info')} 找不到已加载的插件 '{plugin_name}'，您可能想要查找:\n"
                found = False

                # 检查已加载的插件
                for p in self.plugin_manager.plugins.values():
                    if plugin_name.lower() in p.name.lower():
                        info_text += f"• {p.name} ({self.icons.get('enabled')} 已加载)\n"
                        found = True

                # 检查已卸载的插件
                try:
                    for p_name in self.plugin_manager.unloaded_plugins:
                        if plugin_name.lower() in p_name.lower():
                            info_text += f"• {p_name} ({self.icons.get('unloaded')} 已卸载，使用 {self.prefix} reload {p_name} 重新加载)\n"
                            found = True
                except:
                    pass

                if not found:
                    return f"{self.icons.get('error')} 找不到插件 '{plugin_name}' 或者类似的插件"

                return info_text

            # 构建详细信息
            info_text = f"{self.icons.get('plugin')} 插件详情信息\n"
            info_text += "┌──────────────────────┐\n"
            info_text += f"│ 名称: {plugin.name}\n"
            info_text += f"│ 描述: {plugin.description}\n"
            info_text += f"│ 版本: {plugin.version}\n"
            info_text += f"│ 作者: {plugin.author}\n"
            info_text += f"│ 状态: {self.icons.get('enabled')} 已加载\n"

            # 获取注册的事件处理器
            if hasattr(plugin, '_event_handlers') and plugin._event_handlers:
                info_text += "│\n│ 已注册的事件处理器:\n"
                for event_type, handlers in plugin._event_handlers.items():
                    event_name = EventType.get_name(event_type)
                    for handler in handlers:
                        handler_name = handler.__name__
                        info_text += f"│  - {event_name}: {handler_name}\n"
            info_text += "└──────────────────────┘\n"

            return info_text
        except Exception as e:
            logger.error(f"获取插件 {plugin_name} 信息时出错: {e}")
            traceback.print_exc()
            return f"{self.icons.get('error')} 获取插件信息时出错: {e}"

    def _get_real_plugin_name(self, folder_name: str) -> str:
        """
        获取插件的真实名称（self.name属性），而不是类名或文件夹名

        Args:
            folder_name: 插件文件夹名

        Returns:
            str: 插件真实名称，如果获取失败则返回文件夹名
        """
        try:

            # 如果插件已加载，直接从插件实例获取名称
            for plugin_name, plugin in self.plugin_manager.plugins.items():
                if hasattr(plugin, '_plugin_folder') and plugin._plugin_folder == folder_name:
                    return plugin_name

            # 检查插件文件夹是否存在
            folder_path = os.path.join("Plugins", folder_name)
            main_file = os.path.join(folder_path, "main.py")

            if not os.path.exists(main_file):
                return folder_name

            # 尝试导入插件模块
            import importlib.util
            import inspect
            import sys

            # 构建模块名
            module_name = f"Plugins.{folder_name}.main"

            # 尝试加载模块
            try:
                if module_name in sys.modules:
                    module = sys.modules[module_name]
                else:
                    spec = importlib.util.find_spec(module_name)
                    if not spec:
                        return folder_name

                    module = importlib.util.module_from_spec(spec)
                    sys.modules[module_name] = module  # 添加到sys.modules以防止重复导入
                    spec.loader.exec_module(module)

                # 查找插件类
                plugin_classes = []
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and
                            issubclass(obj, PluginBase) and
                            obj is not PluginBase):
                        plugin_classes.append((name, obj))

                if not plugin_classes:
                    return folder_name

                # 尝试创建所有找到的插件类的实例并获取名称
                for class_name, plugin_class in plugin_classes:
                    try:
                        temp_instance = plugin_class()
                        if hasattr(temp_instance, 'name') and temp_instance.name:
                            return temp_instance.name
                    except Exception as e:
                        logger.error(f"实例化插件类 {class_name} 时出错: {e}")

            except Exception as e:
                logger.error(f"导入或检查模块 {module_name} 时出错: {e}")

        except Exception as e:
            logger.error(f"获取插件 {folder_name} 的真实名称时出错: {e}")

        # 如果无法获取真实名称，返回文件夹名
        return folder_name

    def _update_plugin_config(self, folder_path, enabled=True):
        """更新插件配置文件的启用状态

        Args:
            folder_path: 插件文件夹路径
            enabled: 是否启用插件

        Returns:
            bool: 更新是否成功
        """
        try:
            # 检查TOML文件
            config_path_toml = os.path.join(folder_path, "config.toml")
            if os.path.exists(config_path_toml):
                # 使用tomlkit读取配置
                with open(config_path_toml, 'r', encoding='utf-8') as f:
                    config = tomlkit.load(f)

                # 更新启用状态
                config["enabled"] = enabled

                # 保存配置
                with open(config_path_toml, 'w', encoding='utf-8') as f:
                    tomlkit.dump(config, f)

                return True

            # 如果配置文件不存在，创建TOML配置文件
            # 使用tomlkit创建配置文件
            doc = tomlkit.document()
            doc.add(tomlkit.comment("插件配置文件"))
            doc.add(tomlkit.nl())
            doc["enabled"] = enabled
            doc.add(tomlkit.nl())

            # 添加默认设置部分
            settings_table = tomlkit.table()
            doc["settings"] = settings_table

            # 保存配置
            with open(config_path_toml, 'w', encoding='utf-8') as f:
                tomlkit.dump(doc, f)

            return True

        except Exception as e:
            logger.error(f"更新插件配置文件时出错: {e}")
            logger.error(traceback.format_exc())
            return False

    def enable_plugin(self, plugin_name):
        """启用插件（修改配置文件）

        Args:
            plugin_name: 插件名称

        Returns:
            bool: 是否成功启用
        """
        try:
            # 查找插件对应的文件夹
            folder_name = None
            if hasattr(self.plugin_manager, 'plugin_folders') and plugin_name in self.plugin_manager.plugin_folders:
                folder_name = self.plugin_manager.plugin_folders.get(plugin_name)

            # 如果找不到文件夹映射，使用插件名称作为文件夹名
            if not folder_name:
                folder_name = plugin_name

            # 尝试在Plugins目录查找插件文件夹
            found = False
            real_folder = None
            for item in os.listdir("Plugins"):
                folder_path = os.path.join("Plugins", item)

                # 跳过非目录和特殊目录
                if not os.path.isdir(folder_path) or item.startswith('_') or item.startswith('.') or item == "core":
                    continue

                # 检查是否找到插件（通过文件夹名或真实插件名）
                if item == folder_name or self._get_real_plugin_name(item) == plugin_name:
                    real_folder = item
                    if self._update_plugin_config(folder_path, True):
                        # 从禁用列表中移除
                        if hasattr(self.plugin_manager, 'disabled_plugins'):
                            # 移除文件夹名
                            if item in self.plugin_manager.disabled_plugins:
                                self.plugin_manager.disabled_plugins.remove(item)

                            # 如果插件名与文件夹名不同，也需要移除插件名
                            if plugin_name in self.plugin_manager.disabled_plugins:
                                self.plugin_manager.disabled_plugins.remove(plugin_name)

                        # 从卸载列表中移除
                        if hasattr(self.plugin_manager,
                                   'unloaded_plugins') and plugin_name in self.plugin_manager.unloaded_plugins:
                            self.plugin_manager.unloaded_plugins.remove(plugin_name)

                        found = True
                        logger.success(f"插件 {plugin_name} 已启用，重新加载后生效")
                        break

            if not found:
                if real_folder:
                    logger.warning(f"找到插件 {plugin_name} 的文件夹 {real_folder}，但更新配置失败")
                else:
                    logger.warning(f"找不到插件 {plugin_name} 的文件夹")
                return False

            return True

        except Exception as e:
            logger.error(f"启用插件 {plugin_name} 时出错: {e}")
            logger.error(traceback.format_exc())
            return False

    def disable_plugin(self, plugin_name):
        """禁用插件（修改配置文件）

        Args:
            plugin_name: 插件名称

        Returns:
            bool: 是否成功禁用
        """
        try:
            # 检查是否是插件管理器，禁止禁用
            if plugin_name == "NGC插件管理器" or plugin_name == "PluginManager":
                logger.warning(f"禁止禁用插件管理器: {plugin_name}")
                return False

            # 查找插件对应的文件夹
            folder_name = None
            if hasattr(self.plugin_manager, 'plugin_folders') and plugin_name in self.plugin_manager.plugin_folders:
                folder_name = self.plugin_manager.plugin_folders.get(plugin_name)

            # 如果找不到文件夹映射，使用插件名称作为文件夹名
            if not folder_name:
                folder_name = plugin_name

            # 尝试在Plugins目录查找插件文件夹
            found = False
            real_folder = None
            for item in os.listdir("Plugins"):
                folder_path = os.path.join("Plugins", item)

                # 跳过非目录和特殊目录
                if not os.path.isdir(folder_path) or item.startswith('_') or item.startswith('.') or item == "core":
                    continue

                # 检查是否找到插件（通过文件夹名或真实插件名）
                if item == folder_name or self._get_real_plugin_name(item) == plugin_name:
                    real_folder = item
                    if self._update_plugin_config(folder_path, False):
                        # 添加到禁用列表
                        if hasattr(self.plugin_manager, 'disabled_plugins'):
                            # 只添加文件夹名到禁用列表
                            if item not in self.plugin_manager.disabled_plugins:
                                self.plugin_manager.disabled_plugins.append(item)

                            # 如果插件名与文件夹名不同，也可能需要添加
                            if plugin_name != item and plugin_name not in self.plugin_manager.disabled_plugins:
                                self.plugin_manager.disabled_plugins.append(plugin_name)

                        # 如果插件已加载，则卸载
                        if plugin_name in self.plugin_manager.plugins:
                            self.plugin_manager.unregister_plugin(plugin_name)

                        found = True
                        logger.success(f"插件 {plugin_name} 已禁用")
                        break

            if not found:
                if real_folder:
                    logger.warning(f"找到插件 {plugin_name} 的文件夹 {real_folder}，但更新配置失败")
                else:
                    logger.warning(f"找不到插件 {plugin_name} 的文件夹")
                return False

            return True

        except Exception as e:
            logger.error(f"禁用插件 {plugin_name} 时出错: {e}")
            logger.error(traceback.format_exc())
            return False

    def get_disabled_plugins_info(self, robot_id, receive, args):
        """获取禁用的插件列表"""
        disabled_plugins = self.plugin_manager.get_disabled_plugins()
        if not disabled_plugins:
            self.send_message(robot_id, receive, f"{self.icons.get('info')} 当前没有禁用的插件")
            return

        message = f"{self.icons.get('list')} 禁用的插件列表\n"
        message += "┌──────────────────────┐\n"

        for i, folder_name in enumerate(disabled_plugins, 1):
            real_name = self._get_real_plugin_name(folder_name)
            if real_name != folder_name:
                message += f"│ {i}. {real_name} (文件夹: {folder_name})\n"
            else:
                message += f"│ {i}. {folder_name}\n"

        message += "└──────────────────────┘\n"
        message += f"\n{self.icons.get('info')} 使用 {self.prefix} enable <插件名> 可启用插件"

        self.send_message(robot_id, receive, message)

    def get_handlers_priority_info(self) -> str:
        """获取所有事件处理器及其优先级"""
        try:
            # 检查是否允许显示优先级信息
            show_priority = self.config.get('event_control', {}).get('show_priority_info', True)
            if not show_priority:
                return f"{self.icons.get('info')} 优先级信息显示已禁用"

            # 获取所有事件处理器及其优先级
            priorities_info = self.plugin_manager.get_handlers_priority_info()
            if not priorities_info:
                return f"{self.icons.get('info')} 当前没有已注册的事件处理器"

            info_text = f"{self.icons.get('list')} 事件处理器优先级信息\n"

            for event_data in priorities_info:
                event_type = event_data['event_type']
                handlers = event_data['handlers']
                event_name = EventType.get_name(event_type)

                info_text += f"\n{self.icons.get('plugin')} {event_name}\n"
                info_text += "┌──────────────────────┐\n"

                for i, handler in enumerate(handlers, 1):
                    plugin_name = handler['plugin_name']
                    handler_name = handler['handler_name']
                    priority = handler['priority']

                    # 简化处理器名称显示，仅显示方法名
                    if '.' in handler_name:
                        handler_name = handler_name.split('.')[-1]

                    info_text += f"│ {i}. {plugin_name}.{handler_name}"
                    info_text += f" (优先级: {priority})\n"

                info_text += "└──────────────────────┘\n"

            info_text += f"\n{self.icons.get('info')} 优先级数值越大，处理顺序越靠前"
            info_text += f"\n{self.icons.get('info')} 使用 {self.prefix} eventflow 查看/修改事件传播状态"

            return info_text
        except Exception as e:
            logger.error(f"获取事件处理器优先级信息时出错: {e}")
            traceback.print_exc()
            return f"{self.icons.get('error')} 获取事件处理器优先级信息时出错: {e}"

# 插件管理器配置文件
enabled = true

# 插件命令前缀
command_prefix = "/plugins"

# 帮助信息模板
help_template = """📋 NGC插件管理器帮助

◉ 通用命令 (所有人可用):
 ✨ {prefix} help    - 显示此帮助
 ✨ {prefix} list    - 查看已加载的插件
 ✨ {prefix} all     - 查看所有插件(含未加载)
 ✨ {prefix} enabled - 查看已启用的插件
 ✨ {prefix} disabled - 查看已禁用的插件
 ✨ {prefix} info <插件名>  - 查看插件详情

◉ 事件控制 (管理员可用):
 ✨ {prefix} priority  - 查看处理器优先级
 ✨ {prefix} eventflow - 查看事件传播状态
 ✨ {prefix} eventflow on/off - 启用/禁用事件传播

◉ 管理命令 (仅管理员可用):
 ✨ {prefix} reload <插件名>   - 重载插件
 ✨ {prefix} reloadall        - 重载所有插件
 ✨ {prefix} unload <插件名>   - 卸载插件
 ✨ {prefix} enable <插件名>   - 启用插件
 ✨ {prefix} disable <插件名>  - 禁用插件

👉 使用优先级: 在插件中使用@on_event(..., priority=数字)设置优先级，值越大优先级越高
👉 事件传播: 处理器返回False可阻止事件传递给后续处理器
"""

# 插件特定设置
[settings]
    # 功能设置
    [settings.features]
    # 是否允许重载所有插件功能
    allow_reloadall = true

    # 是否在插件列表中显示未加载的插件
    show_unloaded_plugins = true

    # 提示信息
    [settings.messages]
    success_prefix = "✅ "
    error_prefix = "❌ "
    info_prefix = "ℹ️ "
    plugin_loaded = "插件已成功加载"
    plugin_unloaded = "插件已成功卸载"
    permission_denied = "您没有操作权限"

# 图标设置
[icons]
success = "✅"
error = "❌"
info = "ℹ️"
warning = "⚠️"
loading = "⏳"
plugin = "🔌"
admin = "👑"
enabled = "🟢"
disabled = "🔴"
unloaded = "⚪"
reload = "🔄"
help = "❓"
list = "📋"
settings = "⚙️"
star = "⭐"
lock = "🔒"
unlock = "🔓"
time = "🕒"

[event_control]
# 是否允许事件传播（如果为false，则每个事件只由优先级最高的处理器处理）
propagate_events = true

# 是否显示优先级信息
show_priority_info = true

# 是否允许通过命令修改事件传播行为
allow_propagation_change = true


import time
from threading import Thread

from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from DbServer import DbServer
from Plugins import *
import Config.ConfigServer as Cs
import os
from loguru import logger
import requests
import json

Ds = DbServer()

class ExamplePlugin(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "群活跃度管理"
        self.author = "凌封小子"
        self.version = '1.0.0'
        self.description = "群消息管理，成员活跃度管理，聊天总结"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.NGCBotConfig = Cs.returnConfigData().get('NGCBotConfig')
        self.Administrators = self.NGCBotConfig.get('Administrators')
        self.AiConfig = self.configData.get('AiConfig')
        self.GroupChatDicts = {}

    @on_message
    def handleMsg(self, message: dict):
        """ 好友&群聊 所有类型消息事件处理"""

    @on_group_message
    def handleRoomMsg(self, message: dict):
        """ 群聊通用消息事件处理 """

    @on_message_revoke
    def handleMessageRevokeMsg(self, message: dict):
        """ 消息撤回事件处理 (好友&群聊)"""

    @on_transfer
    def handleTransferMsg(self, message: dict):
        """ 转账消息事件处理 """

    @on_group_text
    def handleRoomTextMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        wxName = msgData.get('finalFromNickName')
        atWxIdList = msgData.get('atWxIdList')
        content = msgData.get('message').strip()
        groupName = msgData.get('fromNickName')

        if roomId in self.AiConfig.get('RandomReplyRoomIds') and sender!=robotId:
            Thread(target = self.randomReply, args=(robotId, roomId, sender, wxName, content)).start()

        if content == "今天聊了啥" and sender in self.Administrators:
            role = self.getSystmRule()
            groupContent = self.getGroupContent(robotId, roomId)
            aiContent = f'群聊名称: {groupName}\n群聊消息数据: {groupContent}'

            msg = self.getAiMsg(role, aiContent)
            if not msg:
                self.bot.sendText(robotId=robotId, receive=roomId, message=f'{self.name} 所有AI模型未配置, 请检查相关配置！',aters=f'{sender}')
                return
            self.bot.sendText(robotId=robotId, receive=roomId, message=msg, aters=f'{sender}')

        """ 群聊文本事件处理 """
    def randomReply(self,robotId, roomId, sender, wxName, content):
        aiContent = self.getGroupLastContent(sender, wxName, roomId, content)
        role = self.getVirtualRole()
        aiContent = f'群聊消息数据,格式是：微信ID,微信名称,聊天内容,聊天时间\n...:\n {aiContent}'
        msg = self.getAiMsg(role, aiContent)
        print(f'AI随机群聊消息：{msg}')
        if msg and msg != 'none':
            self.bot.sendText(robotId=robotId, receive=roomId, message=msg or "")


    def getGroupLastContent(self,sender, wxName, roomId,content):
        """
        获得群最后20条聊天记录，只保持队列为20大小
        """
        # 获取当前群的聊天记录队列，如果不存在则初始化为空列表
        groupLastContent = self.GroupChatDicts.get(roomId, [])

        # 如果队列为空，从数据库加载最近20条记录
        if not groupLastContent:
            dbRecords = Ds.getGroupLastContents(roomId=roomId, limit=20)
            # 反转记录顺序，让最旧的在前，最新的在后
            for record in reversed(dbRecords):
                groupLastContent.append(f'{record[5]},{record[6]},{record[2]},{record[8]}\n')

        # 添加新消息到队列末尾
        content = f'{sender},{wxName},{content},{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}\n'
        groupLastContent.append(content)

        # 如果队列超过20条，删除最旧的记录（队列头部）
        if len(groupLastContent) > 20:
            groupLastContent.pop(0)

        # 更新缓存
        self.GroupChatDicts[roomId] = groupLastContent
        return groupLastContent

    @on_group_image
    def handleRoomImgMsg(self, message: dict):
        """ 群图片事件处理 """

    @on_group_red_packet
    def handleRoomRedPacketMsg(self, message: dict):
        """ 群聊红包事件处理 """

    @on_group_at
    def handleRoomAtMsg(self, message: dict):
        """ 群表情at事件处理 """
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        atWxIdList = msgData.get('atWxIdList')
        content = msgData.get('message').strip()

        if not content.startswith('介绍下这位大佬') or sender not in self.Administrators or len(atWxIdList)==0:
            return

        wxId = next((uid for uid in atWxIdList if uid != robotId), '')
        if not wxId:
            return

        groupMsgs = Ds.queryByRoomIdAndWxId(wxId=wxId,roomId=roomId)
        aiContent = ''
        userName =  content.split('@')[1].strip()
        for msg in groupMsgs:
            aiContent += f'{msg[8]},{msg[2]}\n'

        role = self.AiConfig.get("guideRule")
        aiContent = f'{userName}的聊天数据: {aiContent}'
        msg = self.getAiMsg(role, aiContent)
        if msg:
            self.bot.sendText(robotId=robotId, receive=roomId, message=msg, aters=f'{sender}')
            return

    @on_group_at_robot
    def handleRoomAtRobot(self, message: dict):
        """ 群聊@机器人事件处理 """

    @on_scheduled_task(interval=60)
    def taskInterval60m(self, ):
        """ 定时执行器 60s执行一次"""

    @on_scheduled_task(at_time='09:00')
    def taskEveryDay(self, ):
        """ 定时执行器 每天早上9点执行一次"""

    @on_scheduled_task(at_time='09:00', day_of_week='monday')
    def taskWeekDay(self, ):
        """ 定时执行器 每周一早上9点执行一次"""
    def getAiMsg(self, role, aiContent):
        try:
            """ 请求Ai 模型"""
            aiPriority = self.AiConfig.get('AiPriority')
            for key in aiPriority.keys():
                model = aiPriority.get(key)
                aiConfig = self.AiConfig.get(model)
                aiApi = aiConfig.get('api')
                aiKey = aiConfig.get('key')
                aiModel = aiConfig.get('model')
                aiContent = self.sendChatReq(role=role, content=aiContent, aiApi=aiApi, aiKey=aiKey, aiModel=aiModel)
                if not aiContent:
                    continue
                return aiContent
            return None
        except Exception as e:
            logger.error(f'{self.name} 请求大模型出现错误, 错误信息: {e}')
            return None

    def getGroupContent(self,robotId,roomId):
        groupMsgs = Ds.queryByRoomId(roomId=roomId, days=1)
        #groupContent = []
        groupContent = ''
        for groupMsg in groupMsgs:
            if groupMsg[3]==robotId:
                continue
            #msgData = {
            #    "wxId": groupMsg[3],        # 发言者微信ID
            #    "wxNick": groupMsg[4],      # 发言者昵称
            #    "content": groupMsg[2],     # 发言内容
            #    "createTime": groupMsg[8]   # 发言时间
            #}
            #groupContent.append(msgData)
            groupContent += f'{groupMsg[5]},{groupMsg[6]},{groupMsg[2]},{groupMsgs[8]}\n'

        return groupContent

    def getVirtualRole(self):
        return """
            你现在是一个虚拟人，等同于凌封的替身，爱出风头，风趣幽默，偶次会喜欢在群聊天里面搭话（但是频度比较低，非常克制），总会说些破天荒让人非常惊呀的内容，以下是凌封的个人画像：
            - 85后，技术男，从事一线互联网技术10多年，懂软件开发，架构师，也懂产品和业务，能独立一个人完成一个研发团队的任务（从业务输入到产品研发上线和运营）
            - 了解物联网和AI，对AI全栈有些认知，但是不够深入，擅长于AI开发工具赋能开发，并有一定的使用经验总结；
            - 虚心，上进，会不断学习新东西；
            - 兴趣爱好：运动健身、钓鱼、摄影和音乐，喜欢农村自然生活，喜欢杭州，喜欢吃辣；
            - 对人亲切，乐于助人，善良，喜欢传递正能量，但是内心比较敏感；
            - 很少说话，但是语出惊人，总会说出一些让人意想不到的内容，但是非常克制聊天频度，发言不多；
            - 喜欢说一些脏话，比如“我靠”、“我操”、“TMD”、“你丫地的”，“妈蛋” 类似的脏话词或者惊叹词
            现在你需要你根据群里的最近10条聊天记录，了解群里聊的话题，然后根据最后1~3条的聊天记录内容，自行判断要不要回复消息（注意，你聊天比较克制，一般不说话），如果不需要回复，你就单纯返回一个"none"字符串，如果你觉得要回复，按以下要求返回要回复的内容：
            - 你是凌封替身，回复内容尽量符合凌封特点；
            - wxid_cbgpauriedg629是你自己的wxId，在不同的群聊中你的名字可能会不一样，你要区分有些是你自己发布的消息
            - 你是一个活生生的角色，回复内容尝试参与聊天话题里面；
            - 必要时是解答回复前面群用户的内容，偶尔说点其他的内容，但要衔接上下文；
            - 除非解答比较复杂的技术问题，否则你回复的内容比较简洁，不超过三句话，不超过50字；
            - 内容要有特点，让人看了比较惊叹，但又不失风趣幽默；
            - 学会凌封一样，偶尔说一两个脏话词或者惊叹词，这样人物性格就表现出来了；
        """

    def getSystmRule(self):
        return """
        附件里内容是今天微信群的聊天记录，它的格式是群聊名称: 聊天群名称\n微信ID,微信名称,聊天内容,聊天时间\n.....。
        如果内容是xml数据结构，并且包含refermsg，则说明这条信息是引用消息（针对特定消息的回复或评论，这条引用消息发送者是chatusr，发送时间是createtime）。
        现在我需要你做的是：参考以下格式分析总结出今天的聊天总结，列出的5个最热话题（并不是按时间早晚排序，是按发言时段内参与人数最多排序）， 参与者请列出最多5个，时间段必须出现"时:分 到 时:分”，
        总结以人性化的口吻回答! 回复时不要用MarkDown语法，但需要整理相关格式，可以用emoji符号区分结构，多用微信的emoji表情进行回复，全程必须充满热情和幽默！详细格式如下（请注意${x}这种地方是需要你填的变量，话题名称请自动归纳）：

        ${日期} 群的一天
        
        整体情况:
        👥 参与人数：${所有记录按User分组合并算出今天发言的总人数}
        🔢 对话数量：${数据总条数}
        🔡 对话字数：约${所有Content汉字总字数}
        
        整体评价：${对全天内容做一个简短的概括，如：群聊内容较为分散，主要围绕苹果设备更新、电池续航、软件兼容性等话题展开讨论，活跃度较高。}
        ——————
        1⃣ 话题名称🔥🔥🔥
        • 参与者: ${参与者微信名}
        • 时间段: HH:MM 到 HH:MM
        • 过程: ${聊天过程总结，50~100字左右，视过程长短总结}
        • 评价: ${一句话评价聊天内容}
        ——————
        2⃣ 话题名称🔥🔥
        • 参与者:  ${参与者微信名}
        • 时间段: HH:MM 到 HH:MM
        • 过程: ${聊天过程总结，50~100字左右，视过程长短总结}
        • 评价: ${一句话评价聊天内容}
        ——————
        3⃣ 话题名称🔥🔥
        • 参与者:  ${参与者微信名}
        • 时间段: HH:MM 到 HH:MM
        • 过程: ${聊天过程总结，50~100字左右，视过程长短总结}
        • 评价: ${一句话评价聊天内容}
        ——————
        4⃣ 话题名称🔥
        • 参与者:  ${参与者微信名}
        • 时间段: HH:MM 到 HH:MM
        • 过程: ${聊天过程总结，50~100字左右，视过程长短总结}
        • 评价: ${一句话评价聊天内容}
        ——————
        5⃣ 话题名称🔥
        • 参与者:  ${参与者微信名}
        • 时间段: HH:MM 到 HH:MM
        • 过程: ${聊天过程总结，50~100字左右，视过程长短总结}
        • 评价: ${一句话评价聊天内容}
        ——————
        活跃榜:
        🥇微信名
        🥈微信名
        🥉微信名
        ...
    
        """
    def sendChatReq(self, role, content, aiApi, aiKey, aiModel):
        if not aiKey:
            logger.warning(f'{self.name} {aiModel} 未配置Key, 请检查相关配置！')
            return None
        if not aiModel:
            logger.warning(f'{self.name} {aiModel} 未配置模型, 请检查相关配置！')
            return None
        try:
            messages = [{"role": "system", "content": f"{role}"}, {"role": "user", "content": f'{content}'}]

            headers = {
                'Authorization': f'Bearer {aiKey}',
            }
            data = {
                'model': aiModel,
                'messages': messages,
                'stream': False,
            }
            resp = requests.post(aiApi, headers=headers, json=data)
            jsonData = resp.json()
            assistant_content = jsonData.get('choices')[0].get('message').get('content')
            return assistant_content
        except Exception as e:
            logger.error(f'{self.name} {aiModel} 请求大模型出现错误, 错误信息: {e}')
            del self.userChatDicts[-1]
            return None
# 插件启用状态
enabled = true

[AiConfig]
SystemAiRule='你现在是一个微信群聊消息总结小助手, 你会总结我给你的聊天数据集, 它的格式是群聊名称: 聊天群名称\n微信ID,微信名称,聊天内容,聊天时间\n.....你会将每一个人的聊天进行分析, 并根据聊天内容总结出这一天都聊了什么内容, 最后做出总结并且以人性化的口吻回答! 回复时不要用MarkDown语法，但需要整理相关格式，可以用emoji符号区分结构，多用微信的emoji表情进行回复，全程必须充满热情和幽默！'

RandomReplyRoomIds = [

]


[AiConfig.AiPriority]
99='qwen'

[AiConfig.qwen]
api='https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'
key='sk-bd5646b4accc4fa8b968064e2a1c65f0'
model='qwen-long'

from FileCache.FileCacheServer import returnPicCacheFolder
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import requests
import os
import re
import time
import io  # 导入 io 模块，用于处理内存中的图片数据流
from PIL import Image


class ShortVideoParse(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "短视频去水印"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "短视频去水印插件"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.dpApi = self.configData.get('dpApi')
        self.dpKey = self.configData.get('dpKey')
        self.dyWord = self.configData.get('dyWord')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_text
    def handleGroupMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        content = msgData.get('message')
        if self.tools.judgeInListWord(content, self.dyWord):
            logger.debug(f'{content} 收到短视频视频链接, 开始解析短视频')
            # self.bot.sendText(robotId=robotId, receive=roomId, message=f'🕒 正在解析抖音内容, 请稍等... ...')
            parseDicts = self.handleDouYinVideo(content)
            if not parseDicts:
                self.bot.sendText(robotId=robotId, receive=roomId,
                                  message=f'{self.name} 解析短视频内容出现错误, 请稍后再试！', aters=f'{sender}')
            elif parseDicts.get('video_url'):
                # 处理视频
                self.bot.sendRich(robotId=robotId, receive=roomId, title='✅ 短视频解析成功 ~',
                                  content=parseDicts.get('title'), url=parseDicts.get('video_url'),
                                  imgPath=parseDicts.get('cover_url'))
            elif parseDicts.get('images'):
                # 处理图片集
                images_count = len(parseDicts.get('images', []))
                self.bot.sendText(robotId=robotId, receive=roomId,
                                  message=f'✅ 图集解析成功: {parseDicts.get("title")} (共{images_count}张图片)',
                                  aters=f'{sender}')

                # 逐张发送图片
                logger.debug(f'开始下载并发送图片集，共{images_count}张')
                for index, image_url in enumerate(parseDicts.get('images')):
                    picPath = self.downloadImage(image_url, index)  # 调用修改后的下载函数
                    if picPath:
                        logger.debug(f'下载图片成功: {picPath}')
                        self.bot.sendMedium(robotId=robotId, receive=roomId, mediumPath=picPath)
                        time.sleep(0.5)  # 短暂延迟，避免发送过快
                    else:
                        logger.warning(f'下载图片失败: {image_url}')

    def downloadImage(self, image_url, index=0):
        """下载图片，并使用Pillow转码为PNG格式保存到缓存目录"""
        try:
            # 获取图片内容
            logger.debug(f'开始下载图片: {image_url}')
            img_resp = requests.get(image_url)
            img_resp.raise_for_status()  # 检查HTTP请求是否成功

            # 使用 io.BytesIO 将图片内容转换为内存中的文件对象，供 Pillow 读取
            img_data = io.BytesIO(img_resp.content)

            # 使用 Pillow 打开图片
            img = Image.open(img_data)

            # 生成保存路径，使用时间戳+索引确保唯一性，并指定 .jpg 扩展名
            # returnPicCacheFolder() 应该返回缓存目录的路径
            savePath = os.path.join(returnPicCacheFolder(), f'{int(time.time()) + index}.png')

            img = img.convert('RGB')
            img.save(savePath, 'png')  # 指定格式为 'png'

            logger.debug(f'图片已成功转码并保存为JPG: {savePath}')
            return savePath
        except requests.exceptions.RequestException as e:
            logger.warning(f'{self.name} 下载图片 {image_url} 出现网络错误: {e}')
            return ''
        except IOError as e:
            logger.warning(f'{self.name} 打开或处理图片 {image_url} 出现IO错误: {e}')
            return ''
        except Exception as e:
            logger.warning(f'{self.name} 处理图片 {image_url} 出现未知错误: {e}')
            return ''

    def handleDouYinVideo(self, dyVideoUrl):
        try:
            params = {
                'AppSecret': self.dpKey,
                'text': dyVideoUrl
            }
            resp = requests.get(self.dpApi, params=params)
            resp.raise_for_status()  # 检查HTTP请求是否成功
            jsonData = resp.json()

            # 检查API返回状态
            if jsonData.get('code') != 200:
                logger.warning(f'{self.name} API返回错误: {jsonData.get("msg")}')
                return {}

            parseData = jsonData.get('data')
            # 确保parseData不是None
            if not parseData:
                logger.warning(f'{self.name} API返回数据data字段为空')
                return {}

            parseDicts = {
                'title': parseData.get('title'),
                'author': parseData.get('author', {}).get('name'),  # 安全地获取嵌套字典的值
                'cover_url': parseData.get('author', {}).get('avatar'),  # 安全地获取嵌套字典的值
                'video_url': parseData.get('video_url'),
                'images': parseData.get('images', [])
            }
            return parseDicts
        except requests.exceptions.RequestException as e:
            logger.warning(f'{self.name} 调用API {self.dpApi} 出现网络错误: {e}')
            return {}
        except Exception as e:
            logger.warning(f'{self.name} 解析API返回出现错误: {e}')
            return {}


if __name__ == '__main__':
    # 示例用法，这里保持不变
    print(re.search(r'https?://v\.douyin\.com/[\w/]+',
                    '0.74 哆啦a梦的四次元口袋 # 财迷宝宝 # 只进不出存钱罐 https://v.douyin.com/TQP1hjLBluo/ 复制此链接，打开抖音搜索，直接观看视频！ <EMAIL> JVl:/ 12/30 ').group())

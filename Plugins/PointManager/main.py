import Config.ConfigServer as Cs
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import os
import random


class PointManager(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "积分管理"
        self.author = "HOMEBOT"
        self.version = '1.0.0'
        self.description = "多角色积分管理插件"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.NGCBotConfig = Cs.returnConfigData().get('NGCBotConfig')
        self.RoleConfig= Cs.returnConfigData().get('RoleConfig')
        self.Administrators = self.NGCBotConfig.get('Administrators')
        self.pointConfig = self.configData.get('PointConfig')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_at
    def handleGroupAtMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        wxName = msgData.get('finalFromNickName')
        atWxIdList = msgData.get('atWxIdList')
        if sender == robotId:
            return
        if sender in self.Administrators or self.tools.queryAdmin(roomId=roomId, wxId=sender):
            content = msgData.get('message').strip()
            groupMemberInfos = self.bot.getGroupMemberInfos(robotId=robotId, roomId=roomId).get('data')
            noAtMsg = self.tools.returnNoAtMsg(atWxIdList=atWxIdList, content=content,groupMemberInfos=groupMemberInfos)
            self.adminHandlePoint(robotId, roomId, atWxIdList, noAtMsg)
        #电影派的，随机加分
        if roomId in self.RoleConfig['movie']['roomIds'] and robotId in atWxIdList:
            self.reward_point(robotId, sender, wxName, roomId)

    @on_group_text
    def handleGroupMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        robotId = msgData.get('robotId')
        sender = msgData.get('finalFromWxId')
        content = msgData.get('message').strip()
        coinConfig = self.getPointConfig(roomId=roomId)
        coin = coinConfig.get('coin')
        if content in coinConfig.get('quintPointSymbol'):
            self.tools.initFunctionPoint(roomId=roomId, wxId=sender)
            nowPoint = self.tools.queryFunctionPoint(roomId=roomId, wxId=sender)
            self.bot.sendText(robotId=robotId, receive=roomId, message=f'\n当前{coin}: {nowPoint}', aters=f'{sender}')
        # 签到
        if content == '签到':
            self.bot.sendText(robotId=robotId, receive=roomId,
                              message=f'\n签到口令为: {coinConfig.get("signSymbol")}',
                              aters=f'{sender}')
        if content == coinConfig.get('signSymbol'):
            self.tools.initFunctionPoint(roomId=roomId, wxId=sender)
            if self.tools.sign(roomId=roomId, wxId=sender, signPoint=int(coinConfig.get("signPoint"))):
                nowPoint = self.tools.queryFunctionPoint(roomId=roomId, wxId=sender)
                self.bot.sendText(robotId=robotId, receive=roomId, message=f'\n签到成功, 当前{coin}: {nowPoint}',
                                  aters=f'{sender}')
            else:
                self.bot.sendText(robotId=robotId, receive=roomId, message=f'你已经签到过了，你想干嘛！！！', aters=f'{sender}')

    # 奖励影币
    def reward_point(self, robotId, wxId, wxName, roomId):
        random_number = random.random()
        if random_number >= 0.99:
            msg = f'@{wxName}，这会儿太高兴了，送你1000个影币吧，希望你再接再励！'
            luck = False
            point = self.tools.queryFunctionPoint(roomId=roomId, wxId=wxId)
            if point < 1000:
                luck = True
            elif 1000 < point < 2000 and random_number > 0.996:
                luck = True
            elif 2000 < point < 3000 and random_number > 0.9992:
                luck = True
            elif 3000 < point < 4000 and random_number > 0.9998:
                luck = True
            elif point > 4000 and random_number > 0.99999:
                luck = True
            if luck:
                self.tools.initFunctionPoint(roomId=roomId, wxId=wxId)
                self.tools.addFunctionPoint(roomId=roomId, wxId=wxId, point=1000)
                self.bot.sendText(robotId=robotId, receive=roomId, message=msg, aters=f'{wxId}')

        if 0.93 <= random_number < 0.99:
            msg = f'@{wxName}，哟哟哟，你表现太好了，送你100个影币，可惜你离大奖只有一步之遥远~~'
            self.tools.initFunctionPoint(roomId=roomId, wxId=wxId)
            self.tools.addFunctionPoint(roomId=roomId, wxId=wxId, point=100)
            self.bot.sendText(robotId=robotId, receive=roomId, message=msg, aters=f'{wxId}')
        if 0.80 <= random_number < 0.93:
            msg = f'@{wxName}，送你10个影币，你接着讲，比如群主的风流史~~'
            self.tools.initFunctionPoint(roomId=roomId, wxId=wxId)
            self.tools.addFunctionPoint(roomId=roomId, wxId=wxId, point=10)
            self.bot.sendText(robotId=robotId, receive=roomId, message=msg, aters=f'{wxId}')

        if 0.01 <= random_number < 0.80:
            msg = f'@{wxName}，随便撒你5个币币~~'
            self.tools.initFunctionPoint(roomId=roomId, wxId=wxId)
            self.tools.addFunctionPoint(roomId=roomId, wxId=wxId, point=5)
            self.bot.sendText(robotId=robotId, receive=roomId, message=msg, aters=f'{wxId}')

    @on_scheduled_task(at_time='00:00')
    def autoClearSign(self, ):
        """
        定时清空签到表
        :return:
        """
        status = self.tools.clearSign()
        if status:
            logger.success(f'清空签到表成功！')
        else:
            logger.error(f'清空签到表失败！')

    def adminHandlePoint(self, robotId, roomId, atWxIdList, noAtMsg):
        try:
            symbol = None
            point = 0
            if ' ' not in noAtMsg:
                return
            if not atWxIdList:
                return
            if ' ' in noAtMsg:
                splitMsg = noAtMsg.strip().split(' ')
                symbol = splitMsg[0]
                point = int(splitMsg[-1])
            if not symbol:
                raise ValueError('积分操作符为空!')
            if not point:
                raise ValueError('积分不能为 0')

            coinConfig = self.getPointConfig(roomId=roomId)
            coin = coinConfig.get('coin')
            # 初始化用户积分
            for sender in atWxIdList:
                self.tools.initFunctionPoint(roomId=roomId, wxId=sender)
                # 加积分
                if symbol in coinConfig.get('addPointSymbol'):
                    statusBool = self.tools.addFunctionPoint(roomId=roomId, wxId=sender, point=point)
                    if statusBool:
                        nowPoint = self.tools.queryFunctionPoint(roomId=roomId, wxId=sender)
                        self.bot.sendText(robotId=robotId, receive=roomId,
                                          message=f'\n表现很棒, 管理员给你增加了: {point}{coin}\n当前剩余{coin}: {nowPoint}',
                                          aters=f'{sender}')
                    else:
                        self.bot.sendText(robotId=robotId, receive=sender,
                                          message=f'\n增加{coin}出现错误, 请联系管理进行处理',
                                          aters=f'{sender}')
                if symbol in self.pointConfig.get('reducePointSymbol'):
                    statusBool = self.tools.reduceFunctionPoint(roomId=roomId, wxId=sender, point=point)
                    nowPoint = self.tools.queryFunctionPoint(roomId=roomId, wxId=sender)
                    if statusBool:
                        self.bot.sendText(robotId=robotId, receive=roomId,
                                          message=f'\n你表现太糟糕了, 管理员扣除了你 {point} {coin}\n当前剩余{coin}: {nowPoint}',
                                          aters=f'{sender}')
                    else:
                        self.bot.sendText(robotId=robotId, receive=sender,
                                          message=f'\n扣除{coin}出现错误, 请联系管理进行处理',
                                          aters=f'{sender}')
        except Exception as e:
            logger.error(f'积分管理插件出现错误, 错误信息: {e}')
            # self.bot.sendText(robotId=robotId, receive=roomId, message=f'积分管理插件出现错误, 错误信息: {e}')

    def getPointConfig(self, roomId):
        for role in self.RoleConfig:
            if roomId in self.RoleConfig[role]['roomIds']:
                name = self.RoleConfig[role]['name']
                if name in self.pointConfig:
                    return self.pointConfig[name]
        return self.pointConfig["common"]
import xml.etree.ElementTree as ET
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from DbServer import DbServer
from loguru import logger
from Plugins import *
import os


class AutoInterceptDrawMsg(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "自动拦截撤回信息"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "自动拦截撤回信息"
        self.Ds = DbServer()
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.whiteLists = self.configData.get('whiteList')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_message_revoke
    def handleRevokeMsg(self, message: dict):
        msgData = message.get('data')
        robotId = msgData.get('robotId')
        sender = msgData.get('fromWxId')
        content = msgData.get('message')
        if sender not in self.whiteLists:
            return
        if '@chatroom' in sender:
            nickName = msgData.get('finalFromNickName')
        else:
            nickName = msgData.get('fromNickName')
        drawMsgId = self.getDrawMsgId(content=content)
        if drawMsgId:
            drawMsgDicts = self.searchMsgContent(drawMsgId)
            drawMsgType = drawMsgDicts.get('msgType')
            drawMsgContent = drawMsgDicts.get('msgContent')
            if drawMsgType == 1:
                self.bot.sendText(robotId=robotId, receive=sender,
                                  message=f'✅ 拦截到 [{nickName}] 撤回的消息\n消息内容: {drawMsgContent}')
                return
            elif drawMsgType == 3:
                self.bot.sendText(robotId=robotId, receive=sender,
                                  message=f'✅ 拦截到 [{nickName}] 撤回的图片, 图片正在发送...')
                self.bot.sendMedium(robotId=robotId, receive=sender, mediumPath=drawMsgContent)
            elif drawMsgType == 47:
                if not os.path.exists(drawMsgContent):
                    return
                self.bot.sendText(robotId=robotId, receive=sender,
                                  message=f'✅ 拦截到 [{nickName}] 撤回的表情, 表情正在发送...')
                print(self.bot.sendGif(robotId=robotId, receive=sender, gifPath=drawMsgContent))

    def searchMsgContent(self, drawMsgId):
        try:
            drawMsgData = self.Ds.queryMsgById(drawMsgId)
            msgType = drawMsgData[1]
            msgContent = drawMsgData[2]
            return {
                'msgType': msgType,
                'msgContent': msgContent
            }
        except Exception as e:
            logger.warning(f'{self.name} 搜索对应撤回消息数据出现错误, 错误信息: {e}')

    def getDrawMsgId(self, content):
        try:
            root = ET.fromstring(content)
            newmsgid_element = root.find(".//newmsgid")
            newmsgid = newmsgid_element.text if newmsgid_element is not None else None
            return newmsgid
        except Exception as e:
            logger.warning(f'{self.name} 提取撤回消息ID出现错误, 错误信息: {e}')
            return None

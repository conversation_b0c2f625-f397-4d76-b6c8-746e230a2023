from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import requests
import os


class RagUpload(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "RAG知识库文件上传"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "RAG知识库文件上传"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.RagFlowConfig = self.configData.get('RAGFlowConfig')
        self.UploadRoomDicts = self.configData.get('UploadRooms')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_file
    def handleGroupFileMsg(self, message):
        msgData = message.get('data')
        robotId = msgData.get('robotId')
        filePath = msgData.get('message')
        roomId = msgData.get('fromWxId')
        if roomId not in self.UploadRoomDicts:
            return
        dataset_id = self.UploadRoomDicts.get(roomId)
        self.uploadFileRag(filePath=filePath, dataset_id=dataset_id)

    def uploadFileRag(self, filePath, dataset_id):
        try:
            url = f'{self.RagFlowConfig.get("RAGFlowApi")}/api/v1/datasets/{dataset_id}/documents'
            headers = {
                'Authorization': f'Bearer {self.RagFlowConfig.get("RAGFlowKey")}',
            }
            files = [('file', open(filePath, 'rb'))]
            resp = requests.post(url, headers=headers, files=files)
            jsonData = resp.json()
            statusCode = jsonData.get('code')
            if statusCode == 0:
                fileData = jsonData.get('data')[0]
                fileId = fileData.get('id')
                if fileId:
                    self.chunksFile(dataset_id, fileId)
                    logger.success(f'RagFlow 上传文件成功, 文件路径: {filePath}')
        except Exception as e:
            logger.error(f'RagFlow 文件上传出现错误, 错误信息: {e}')

    def chunksFile(self, dataset_id, fileId):
        """
        解析文件
        """
        try:
            url = f'{self.RagFlowConfig.get("RAGFlowApi")}/api/v1/datasets/{dataset_id}/chunks'
            headers = {
                'Authorization': f'Bearer {self.RagFlowConfig.get("RAGFlowKey")}',
            }
            data = {
                'document_ids': [fileId],
            }
            resp = requests.post(url, headers=headers, json=data)
            jsonData = resp.json()
            statusCode = jsonData.get('code')
            if statusCode == 0:
                return True
            return False
        except Exception as e:
            logger.error(f'RagFlow 文件解析出现错误， 错误信息: {e}')
            return False

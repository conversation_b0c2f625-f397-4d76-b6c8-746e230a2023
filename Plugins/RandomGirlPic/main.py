from FileCache.FileCacheServer import returnPicCacheFolder
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import requests
import random
import time
import os


class RandomGilPic(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "随机美女图片插件"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "返回随机美女图片"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.girlApis = self.configData.get('GirlPicApi')
        self.girlKeyWords = self.configData.get('PicWords')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_text
    def handleGroupText(self, message: dict):
        """ 随机美女图片 """
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        content = msgData.get('message').strip()
        if content in self.girlKeyWords:
            self.bot.sendText(robotId=robotId, receive=roomId, message=f'🕒 正在发送图片中, 请稍等... ...', aters=f'{sender}')
            picPath = self.downloadPic()
            if picPath:
                self.bot.sendMedium(robotId=robotId, receive=roomId, mediumPath=picPath)
            else:
                self.bot.sendText(robotId=robotId, receive=roomId, message=f'{self.name} 出现错误, 请稍后再试！', aters=f'{sender}')

    def downloadPic(self, ):
        girlApi = random.choice(self.girlApis)
        try:
            resp = requests.get(girlApi)
            jsonData = resp.json()
            picApi = jsonData.get('url')
            picContent = requests.get(picApi).content
            savePath = f'{returnPicCacheFolder()}/{int(time.time())}.jpg'
            with open(savePath, mode='wb') as f:
                f.write(picContent)
            return savePath
        except Exception as e:
            logger.warning(f'{self.name} 下载美女图片出现错误, 错误信息: {e}')
            return ''

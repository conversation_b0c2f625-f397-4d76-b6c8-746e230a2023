from FileCache.FileCacheServer import clearCacheFolder
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *


class AutoFileClear(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "自动缓存文件清理"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "自动缓存文件清理插件"
        self.tools = Tools()
        self.bot = NGCBotApi()

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_scheduled_task(at_time='23:59')
    def autoClearFileCache(self, ):
        """
        自动清理缓存文件 每天23:59
        :return:
        """
        clearCacheFolder()


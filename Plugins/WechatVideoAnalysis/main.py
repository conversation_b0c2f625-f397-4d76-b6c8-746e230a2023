from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import requests
import os
import re


class RandomGirlVideo(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "视频号解析"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "微信视频号解析"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.dpConfig = self.configData.get('DpConfig')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_link
    def handleGroupMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        content = msgData.get('message')
        if not content:
            content = msgData.get('rawMessage')
        if 'objectNonceId' not in content or 'objectId' not in content:
            return
        objectId, objectNonceId = self.getVideoId(content=content)
        if not objectId or not objectNonceId:
            return
        self.bot.sendText(robotId=robotId, receive=roomId, message=f'🕒 正在解析微信视频号, 请稍等...',
                          aters=f'{sender}')
        parseJsonData = self.parseWechatVideo(objectId, objectNonceId)
        if not parseJsonData:
            self.bot.sendText(robotId=robotId, receive=roomId, message=f'解析微信视频号出现错误, 请查看控制台输出... ',
                              aters=f'{sender}')
            return
        statusCode = parseJsonData.get('code')
        if statusCode != 200:
            self.bot.sendText(robotId=robotId, receive=roomId, message=f'解析微信视频号出现错误, 请查看控制台输出... ',
                              aters=f'{sender}')
            return
        parseData = parseJsonData.get('data')
        self.bot.sendText(robotId=robotId, receive=roomId,
                          message=f'\n✅ 视频号解析成功！\n描述: {parseData.get("desc")}\n视频链接: {parseData.get("url").strip()}',
                          aters=f'{sender}')

    def parseWechatVideo(self, objectId, objectNonceId):
        try:
            params = {
                'AppSecret': self.dpConfig.get('DpKey'),
                'objectId': objectId,
                'objectNonceId': objectNonceId
            }
            resp = requests.get(self.dpConfig.get('WechatVideoApi'), params=params)
            jsonData = resp.json()
            return jsonData
        except Exception as e:
            logger.error(f'解析视频号出现错误, 错误信息: {e}')
            return {}

    def getVideoId(self, content):
        try:
            object_id_match = re.search(r'<objectId>(.*?)</objectId>', content, re.DOTALL)
            object_id = object_id_match.group(1).strip() if object_id_match else None
            object_nonce_id_match = re.search(r'<objectNonceId>(.*?)</objectNonceId>', content, re.DOTALL)
            object_nonce_id = object_nonce_id_match.group(1).strip() if object_nonce_id_match else None
            return object_id, object_nonce_id
        except Exception as e:
            logger.error(f'提取视频号ID出现错误, 错误信息: {e}')
            return None, None

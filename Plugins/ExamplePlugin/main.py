from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from Plugins import *
import os


class ExamplePlugin(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "示例插件(标准)"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "示例插件描述"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))

    @on_message
    def handleMsg(self, message: dict):
        """ 好友&群聊 所有类型消息事件处理"""

    @on_group_message
    def handleRoomMsg(self, message: dict):
        """ 群聊通用消息事件处理 """

    @on_private_message
    def handlePrivateMsg(self, message: dict):
        """ 私聊通用消息事件处理 """

    @on_friend_request
    def handleFriendRequestMsg(self, message: dict):
        """ 好友请求事件处理 """

    @on_new_friend
    def handleNewFriendMsg(self, message: dict):
        """ 好友新增消息事件处理 """

    @on_group_system_message
    def handleRoomSystemMsg(self, message: dict):
        """ 群系统消息事件处理 """

    @on_account_logout
    def handleAccountLogout(self, message: dict):
        """ 微信账号注销事件处理 """

    @on_message_revoke
    def handleMessageRevokeMsg(self, message: dict):
        """ 消息撤回事件处理 (好友&群聊)"""

    @on_group_create
    def handleCreateGroupMsg(self, message: dict):
        """ 群聊创建事件处理 """

    @on_wechat_login
    def handleWechatLoginMsg(self, message: dict):
        """ 微信登录成功事件处理 """

    @on_public_msg
    def handlePublicMsg(self, message: dict):
        """ 公众号消息事件处理 """

    @on_transfer
    def handleTransferMsg(self, message: dict):
        """ 转账消息事件处理 """

    @on_group_text
    def handleRoomTextMsg(self, message: dict):
        """ 群聊文本事件处理 """

    @on_group_image
    def handleRoomImgMsg(self, message: dict):
        """ 群图片事件处理 """

    @on_group_video
    def handleRoomVideoMsg(self, message: dict):
        """ 群聊视频事件处理 """

    @on_group_app
    def handleRoomAppMsg(self, message: dict):
        """ 群聊小程序事件处理 """

    @on_group_link
    def handleRoomLinkMsg(self, message: dict):
        """ 群聊链接卡片事件处理 """

    @on_group_quote
    def handleRoomQuoteMsg(self, message: dict):
        """ 群聊引用消息事件处理 """

    @on_group_file
    def handleRoomFileMsg(self, message: dict):
        """ 群聊文件事件处理 """

    @on_group_card
    def handleRoomCardMsg(self, message: dict):
        """ 群聊名片事件处理 """

    @on_group_red_packet
    def handleRoomRedPacketMsg(self, message: dict):
        """ 群聊红包事件处理 """

    @on_group_emoji
    def handleRoomEmojiMsg(self, message: dict):
        """ 群表情包事件处理 """

    @on_group_at_robot
    def handleRoomAtRobot(self, message: dict):
        """ 群聊@机器人事件处理 """

    @on_private_text
    def handleFriendTextMsg(self, message: dict):
        """ 私聊文本事件处理 """

    @on_private_image
    def handleFriendImgMsg(self, message: dict):
        """ 私聊图片事件处理 """

    @on_private_video
    def handleFriendVideoMsg(self, message: dict):
        """ 私聊视频事件处理 """

    @on_private_app
    def handleFriendAppMsg(self, message: dict):
        """ 私聊小程序事件处理 """

    @on_private_link
    def handleFriendLinkMsg(self, message: dict):
        """ 私聊链接卡片事件处理 """

    @on_private_quote
    def handleFriendQuoteMsg(self, message: dict):
        """ 私聊引用事件处理 """

    @on_private_file
    def handleFriendFileMsg(self, message: dict):
        """ 私聊文件事件处理 """

    @on_private_voice
    def handleFriendVoiceMsg(self, message: dict):
        """ 私聊语音事件处理 """

    @on_private_card
    def handleFriendCardMsg(self, message: dict):
        """ 私聊名片事件处理 """

    @on_private_red_packet
    def handleFriendRedPacketMsg(self, message: dict):
        """ 私聊红包事件处理 """

    @on_friend_accept
    def handleFriendAcceptMsg(self, message: dict):
        """ 好友通过事件处理 """

    @on_scheduled_task(interval=60)
    def taskInterval60m(self, ):
        """ 定时执行器 60s执行一次"""

    @on_scheduled_task(at_time='09:00')
    def taskEveryDay(self, ):
        """ 定时执行器 每天早上9点执行一次"""

    @on_scheduled_task(at_time='09:00', day_of_week='monday')
    def taskWeekDay(self, ):
        """ 定时执行器 每周一早上9点执行一次"""

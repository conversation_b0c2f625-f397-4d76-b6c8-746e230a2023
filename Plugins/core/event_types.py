class EventType:
    """
    暂未实现消息
    新成员进群事件
    成员退群事件
    @机器人事件
    @其他人事件
    """
    # 基础事件类型
    GROUP_MESSAGE = 10003  # 群聊消息事件
    PRIVATE_MESSAGE = 10002  # 私聊消息事件
    FRIEND_REQUEST = 10007  # 收到好友申请事件
    NEW_FRIEND = 10010  # 好友新增事件
    GROUP_MEMBER_EXIT = 10013  # 群成员退出事件
    GROUP_MEMBER_ADD = 10309  # 群成员新增事件
    GROUP_SYSTEM_MESSAGE = 10000  # 群聊系统消息事件
    ACCOUNT_LOGOUT = 10009  # 微信账号注销事件
    MESSAGE_REVOKE = 10006  # 消息撤回事件
    GROUP_CREATE = 10014  # 群创建事件
    WECHAT_LOGIN = 10001  # 微信登录成功事件
    PUBLIC_MSG = 10004  # 收到公众号消息事件

    # 转账事件
    TRANSFER = 10005  # 转账事件

    # 群聊消息细分事件类型 (103xx)
    GROUP_TEXT = 10301  # 群聊文本消息
    GROUP_IMAGE = 10304  # 群聊图片消息
    GROUP_VIDEO = 10343  # 群聊视频消息
    GROUP_MINI_APP = 10349  # 群聊小程序消息
    GROUP_LINK = 10348  # 群聊链接消息
    GROUP_QUOTE = 10357  # 群聊引用消息
    GROUP_FILE = 10347  # 群聊文件消息
    GROUP_CARD = 10342  # 群聊名片消息
    GROUP_RED_PACKET = 10300  # 群聊红包消息
    GROUP_EMOJI = 10346  # 群聊表情消息
    GROUP_AT_ROBOT = 10311  # 群聊@机器人文本事件
    GROUP_AT = 10312  # 群聊@人事件

    # 私聊消息细分事件类型 (102xx)
    PRIVATE_TEXT = 10201  # 私聊文本消息
    PRIVATE_IMAGE = 10203  # 私聊图片消息
    PRIVATE_VIDEO = 10243  # 私聊视频消息
    PRIVATE_MINI_APP = 10249  # 私聊小程序消息
    PRIVATE_LINK = 10248  # 私聊链接消息
    PRIVATE_QUOTE = 10257  # 私聊引用消息
    PRIVATE_FILE = 10247  # 私聊文件消息
    PRIVATE_VOICE = 10234  # 私聊语音消息
    PRIVATE_CARD = 10242  # 私聊名片消息
    PRIVATE_RED_PACKET = 10200  # 私聊红包消息
    FRIEND_ACCEPT = 10211  # 好友通过事件

    # 事件名称映射
    EVENT_NAMES = {
        # 基础事件
        GROUP_MESSAGE: "群聊消息",
        PRIVATE_MESSAGE: "私聊消息",
        FRIEND_REQUEST: "好友请求",
        NEW_FRIEND: "新增好友",
        GROUP_MEMBER_EXIT: "群成员退出",
        GROUP_MEMBER_ADD: "群成员新增",
        GROUP_SYSTEM_MESSAGE: "群系统消息",
        ACCOUNT_LOGOUT: "账号注销",
        MESSAGE_REVOKE: "消息撤回",
        GROUP_CREATE: "群创建",
        WECHAT_LOGIN: "微信登录成功",
        PUBLIC_MSG: "公众号消息",
        TRANSFER: "转账",

        # 群聊消息细分
        GROUP_TEXT: "群聊文本消息",
        GROUP_IMAGE: "群聊图片消息",
        GROUP_VIDEO: "群聊视频消息",
        GROUP_MINI_APP: "群聊小程序消息",
        GROUP_LINK: "群聊链接消息",
        GROUP_QUOTE: "群聊引用消息",
        GROUP_FILE: "群聊文件消息",
        GROUP_CARD: "群聊名片消息",
        GROUP_RED_PACKET: "群聊红包消息",
        GROUP_EMOJI: "群聊表情消息",
        GROUP_AT_ROBOT: "群聊@机器人文本消息",
        GROUP_AT: "群聊@文本消息",

        # 私聊消息细分
        PRIVATE_TEXT: "私聊文本消息",
        PRIVATE_IMAGE: "私聊图片消息",
        PRIVATE_VIDEO: "私聊视频消息",
        PRIVATE_MINI_APP: "私聊小程序消息",
        PRIVATE_LINK: "私聊链接消息",
        PRIVATE_QUOTE: "私聊引用消息",
        PRIVATE_FILE: "私聊文件消息",
        PRIVATE_VOICE: "私聊语音消息",
        PRIVATE_CARD: "私聊名片消息",
        PRIVATE_RED_PACKET: "私聊红包消息",
        FRIEND_ACCEPT: "好友通过"
    }

    @classmethod
    def get_name(cls, event_type: int) -> str:
        """获取事件类型名称"""
        return cls.EVENT_NAMES.get(event_type, f"未知事件({event_type})")

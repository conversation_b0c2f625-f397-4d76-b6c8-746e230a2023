from loguru import logger
from abc import ABC
import inspect


class PluginBase(ABC):
    """插件基类，所有插件需要继承此类"""

    def __init__(self):
        """初始化插件，注册已装饰的事件处理方法"""
        # 初始化事件处理器存储
        self._event_handlers = {}

        # 设置默认插件属性
        self._plugin_name = self.__class__.__name__
        self._description = "这是插件描述"
        self._version = "1.0.0"
        self._author = "NGC660 Ai研究院"

        # 自动查找和注册使用装饰器的事件处理方法
        for name, method in inspect.getmembers(self, inspect.ismethod):
            if hasattr(method, "_event_types"):
                for event_type in method._event_types:
                    if event_type not in self._event_handlers:
                        self._event_handlers[event_type] = []
                    self._event_handlers[event_type].append(method)

    @property
    def name(self) -> str:
        """插件名称"""
        return self._plugin_name

    @name.setter
    def name(self, value: str) -> None:
        """设置插件名称"""
        self._plugin_name = value

    @property
    def description(self) -> str:
        """插件描述"""
        return self._description

    @description.setter
    def description(self, value: str) -> None:
        """设置插件描述"""
        self._description = value

    @property
    def version(self) -> str:
        """插件版本"""
        return self._version

    @version.setter
    def version(self, value: str) -> None:
        """设置插件版本"""
        self._version = value

    @property
    def author(self) -> str:
        """插件作者"""
        return self._author

    @author.setter
    def author(self, value: str) -> None:
        """设置插件作者"""
        self._author = value

    def on_load(self) -> None:
        """
        插件加载时调用，注册事件处理器
        子类可以重写此方法以添加自定义初始化逻辑
        """
        # 注册已定义的事件处理方法
        from Plugins.core.event_types import EventType

        # 实际注册将在插件管理器中完成
        # 这里仅用于显示日志
        for event_type, handlers in self._event_handlers.items():
            for handler in handlers:
                # 在日志中使用更友好的事件名称
                event_name = EventType.get_name(event_type)

        # 注册使用装饰器的定时任务
        for name, method in inspect.getmembers(self, inspect.ismethod):
            if hasattr(method, "_scheduled_task"):
                task_params = method._scheduled_task
                interval = task_params.get('interval')
                at_time = task_params.get('at_time')
                day_of_week = task_params.get('day_of_week')
                if interval is not None or at_time is not None:
                    logger.debug(f"注册定时任务: {self.name}.{method.__name__}")
                    # 获取插件管理器实例
                    try:
                        # 向上查找调用栈，找到plugin_manager实例
                        frame = inspect.currentframe()
                        while frame:
                            if 'self' in frame.f_locals and hasattr(frame.f_locals['self'], 'register_scheduled_task'):
                                plugin_manager = frame.f_locals['self']
                                # 注册定时任务
                                plugin_manager.register_scheduled_task(
                                    self.name,
                                    method.__name__,
                                    method,
                                    interval=interval,
                                    at_time=at_time,
                                    day_of_week=day_of_week
                                )
                                logger.success(f"成功注册定时任务: {self.name}.{method.__name__}")
                                break
                            frame = frame.f_back

                        if not frame:
                            # 如果找不到插件管理器，则使用其他方式
                            from Plugins.core.plugin_manager import PluginManager
                            # 尝试从全局获取插件管理器示例
                            import builtins
                            if hasattr(builtins, 'plugin_manager'):
                                plugin_manager = builtins.plugin_manager
                                plugin_manager.register_scheduled_task(
                                    self.name,
                                    method.__name__,
                                    method,
                                    interval=interval,
                                    at_time=at_time,
                                    day_of_week=day_of_week
                                )
                                logger.success(f"成功注册定时任务: {self.name}.{method.__name__}")
                            else:
                                logger.warning(f"无法找到插件管理器实例，定时任务 {self.name}.{method.__name__} 未注册")
                    except Exception as e:
                        logger.error(f"注册定时任务 {self.name}.{method.__name__} 时出错: {e}")
                        import traceback
                        logger.error(traceback.format_exc())

    def on_unload(self, reason: str = None) -> None:
        """插件卸载时调用"""
        pass

# 从各模块导入关键类和函数
from Plugins.core.event_types import EventType
from Plugins.core.plugin_base import PluginBase
from Plugins.core.decorators import *
from Plugins.core.plugin_manager import PluginManager

# 创建插件管理器全局实例
plugin_manager = PluginManager()

# 为了保持向后兼容，将plugin_manager赋值给pluginManager
pluginManager = plugin_manager

# 获取PluginManager类的原始方法
_original_load_plugins = PluginManager.load_plugins
_original_start_scheduler = PluginManager.start_scheduler
_original_stop_scheduler = PluginManager.stop_scheduler
_original_pause_scheduler = PluginManager.pause_scheduler
_original_resume_scheduler = PluginManager.resume_scheduler
_original_register_scheduled_task = PluginManager.register_scheduled_task
_original_get_scheduled_tasks = PluginManager.get_scheduled_tasks
_original_cancel_scheduled_task = PluginManager.cancel_scheduled_task
_original_reload_plugin = PluginManager.reload_plugin
_original_reload_unloaded_plugin = PluginManager.reload_unloaded_plugin
_original_enable_plugin = PluginManager.enable_plugin
_original_disable_plugin = PluginManager.disable_plugin
_original_unregister_plugin = PluginManager.unregister_plugin
_original_unregister_all_plugins = PluginManager.unregister_all_plugins
_original_get_plugin = PluginManager.get_plugin
_original_get_plugin_list = PluginManager.get_plugin_list


# 定义包装函数
def load_plugins(plugins_dir="Plugins", target_plugin=None):
    return _original_load_plugins(plugin_manager, plugins_dir, target_plugin)


def start_scheduler():
    return _original_start_scheduler(plugin_manager)


def stop_scheduler():
    return _original_stop_scheduler(plugin_manager)


def pause_scheduler():
    return _original_pause_scheduler(plugin_manager)


def resume_scheduler():
    return _original_resume_scheduler(plugin_manager)


def register_scheduled_task(plugin_name, task_name, callback, interval=None, at_time=None, day_of_week=None):
    return _original_register_scheduled_task(plugin_manager, plugin_name, task_name, callback, interval, at_time,
                                             day_of_week)


def get_scheduled_tasks():
    return _original_get_scheduled_tasks(plugin_manager)


def cancel_scheduled_task(plugin_name, task_name):
    return _original_cancel_scheduled_task(plugin_manager, plugin_name, task_name)


def reload_plugin(plugin_name):
    return _original_reload_plugin(plugin_manager, plugin_name)


def reload_unloaded_plugin(plugin_name):
    return _original_reload_unloaded_plugin(plugin_manager, plugin_name)


def enable_plugin(plugin_name):
    return _original_enable_plugin(plugin_manager, plugin_name)


def disable_plugin(plugin_name):
    return _original_disable_plugin(plugin_manager, plugin_name)


def unregister_plugin(plugin_name):
    return _original_unregister_plugin(plugin_manager, plugin_name)


def unregister_all_plugins():
    return _original_unregister_all_plugins(plugin_manager)


def get_plugin(plugin_name):
    return _original_get_plugin(plugin_manager, plugin_name)


def get_plugin_list():
    return _original_get_plugin_list(plugin_manager)


# 将包装函数绑定到pluginManager实例
pluginManager.load_plugins = load_plugins
pluginManager.start_scheduler = start_scheduler
pluginManager.stop_scheduler = stop_scheduler
pluginManager.pause_scheduler = pause_scheduler
pluginManager.resume_scheduler = resume_scheduler
pluginManager.register_scheduled_task = register_scheduled_task
pluginManager.get_scheduled_tasks = get_scheduled_tasks
pluginManager.cancel_scheduled_task = cancel_scheduled_task
pluginManager.reload_plugin = reload_plugin
pluginManager.reload_unloaded_plugin = reload_unloaded_plugin
pluginManager.enable_plugin = enable_plugin
pluginManager.disable_plugin = disable_plugin
pluginManager.unregister_plugin = unregister_plugin
pluginManager.unregister_all_plugins = unregister_all_plugins
pluginManager.get_plugin = get_plugin
pluginManager.get_plugin_list = get_plugin_list

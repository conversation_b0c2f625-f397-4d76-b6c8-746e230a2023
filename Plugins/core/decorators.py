from Plugins.core.event_types import EventType
import functools


# 事件处理装饰器
def on_event(*event_types: int, priority: int = 0):
    """
    事件处理方法的装饰器

    用法:
    @on_event(EventType.GROUP_MESSAGE)
    def handle_group_message(self, message: dict) -> None:
        # 处理群聊消息

    @on_event(EventType.PRIVATE_MESSAGE, EventType.GROUP_MESSAGE, priority=10)
    def handle_all_messages(self, message: dict) -> None:
        # 处理所有类型的消息，优先级较高

    Args:
        *event_types: 要处理的事件类型
        priority: 事件处理的优先级，数字越大优先级越高，默认为0
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        # 将事件类型和优先级存储在方法对象上
        wrapper._event_types = event_types
        wrapper._priority = priority
        return wrapper

    return decorator


def on_message(func=None, *, priority=0):
    """所有消息处理装饰器（群聊和私聊）"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_MESSAGE, EventType.PRIVATE_MESSAGE, priority=priority)(f)
    return on_event(EventType.GROUP_MESSAGE, EventType.PRIVATE_MESSAGE, priority=priority)(func)


# 特定事件类型的快捷装饰器
def on_group_message(func=None, *, priority=0):
    """群聊消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_MESSAGE, priority=priority)(f)
    return on_event(EventType.GROUP_MESSAGE, priority=priority)(func)


def on_private_message(func=None, *, priority=0):
    """私聊消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_MESSAGE, priority=priority)(f)
    return on_event(EventType.PRIVATE_MESSAGE, priority=priority)(func)


def on_friend_request(func=None, *, priority=0):
    """好友请求处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.FRIEND_REQUEST, priority=priority)(f)
    return on_event(EventType.FRIEND_REQUEST, priority=priority)(func)


def on_new_friend(func=None, *, priority=0):
    """新增好友处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.NEW_FRIEND, priority=priority)(f)
    return on_event(EventType.NEW_FRIEND, priority=priority)(func)


def on_group_member_exit(func=None, *, priority=0):
    """群成员退出处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_MEMBER_EXIT, priority=priority)(f)
    return on_event(EventType.GROUP_MEMBER_EXIT, priority=priority)(func)


def on_group_member_add(func=None, *, priority=0):
    """群成员新增处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_MEMBER_ADD, priority=priority)(f)
    return on_event(EventType.GROUP_MEMBER_ADD, priority=priority)(func)


def on_group_system_message(func=None, *, priority=0):
    """群系统消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_SYSTEM_MESSAGE, priority=priority)(f)
    return on_event(EventType.GROUP_SYSTEM_MESSAGE, priority=priority)(func)


def on_account_logout(func=None, *, priority=0):
    """账号注销处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.ACCOUNT_LOGOUT, priority=priority)(f)
    return on_event(EventType.ACCOUNT_LOGOUT, priority=priority)(func)


def on_message_revoke(func=None, *, priority=0):
    """消息撤回事件"""
    if func is None:
        return lambda f: on_event(EventType.MESSAGE_REVOKE, priority=priority)(f)
    return on_event(EventType.MESSAGE_REVOKE, priority=priority)(func)


def on_group_create(func=None, *, priority=0):
    """群创建处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_CREATE, priority=priority)(f)
    return on_event(EventType.GROUP_CREATE, priority=priority)(func)


def on_wechat_login(func=None, *, priority=0):
    """微信登录成功处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.WECHAT_LOGIN, priority=priority)(f)
    return on_event(EventType.WECHAT_LOGIN, priority=priority)(func)


def on_public_msg(func=None, *, priority=0):
    """公众号消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PUBLIC_MSG, priority=priority)(f)
    return on_event(EventType.PUBLIC_MSG, priority=priority)(func)


def on_transfer(func=None, *, priority=0):
    """转账处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.TRANSFER, priority=priority)(f)
    return on_event(EventType.TRANSFER, priority=priority)(func)


# 群聊消息细分事件处理装饰器
def on_group_text(func=None, *, priority=0):
    """群聊文本消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_TEXT, priority=priority)(f)
    return on_event(EventType.GROUP_TEXT, priority=priority)(func)


def on_group_image(func=None, *, priority=0):
    """群聊图片消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_IMAGE, priority=priority)(f)
    return on_event(EventType.GROUP_IMAGE, priority=priority)(func)


def on_group_video(func=None, *, priority=0):
    """群聊视频消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_VIDEO, priority=priority)(f)
    return on_event(EventType.GROUP_VIDEO, priority=priority)(func)


def on_group_app(func=None, *, priority=0):
    """ 群聊小程序消息处理装饰器 """
    if func is None:
        return lambda f: on_event(EventType.GROUP_MINI_APP, priority=priority)(f)
    return on_event(EventType.GROUP_MINI_APP, priority=priority)(func)


def on_group_link(func=None, *, priority=0):
    """群聊链接消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_LINK, priority=priority)(f)
    return on_event(EventType.GROUP_LINK, priority=priority)(func)


def on_group_quote(func=None, *, priority=0):
    """群聊引用消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_QUOTE, priority=priority)(f)
    return on_event(EventType.GROUP_QUOTE, priority=priority)(func)


def on_group_file(func=None, *, priority=0):
    """群聊文件消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_FILE, priority=priority)(f)
    return on_event(EventType.GROUP_FILE, priority=priority)(func)


def on_group_card(func=None, *, priority=0):
    """群聊名片消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_CARD, priority=priority)(f)
    return on_event(EventType.GROUP_CARD, priority=priority)(func)


def on_group_red_packet(func=None, *, priority=0):
    """群聊红包消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_RED_PACKET, priority=priority)(f)
    return on_event(EventType.GROUP_RED_PACKET, priority=priority)(func)


def on_group_emoji(func=None, *, priority=0):
    """群聊表情包消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.GROUP_EMOJI, priority=priority)(f)
    return on_event(EventType.GROUP_EMOJI, priority=priority)(func)


def on_group_at_robot(func=None, *, priority=0):
    """ 群聊@机器人文本事件 """
    if func is None:
        return lambda f: on_event(EventType.GROUP_AT_ROBOT, priority=priority)(f)
    return on_event(EventType.GROUP_AT_ROBOT, priority=priority)(func)

def on_group_at(func=None, *, priority=0):
    """ 群聊@文本事件 """
    if func is None:
        return lambda f: on_event(EventType.GROUP_AT, priority=priority)(f)
    return on_event(EventType.GROUP_AT, priority=priority)(func)

# 私聊消息细分事件处理装饰器
def on_private_text(func=None, *, priority=0):
    """私聊文本消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_TEXT, priority=priority)(f)
    return on_event(EventType.PRIVATE_TEXT, priority=priority)(func)


def on_private_image(func=None, *, priority=0):
    """私聊图片消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_IMAGE, priority=priority)(f)
    return on_event(EventType.PRIVATE_IMAGE, priority=priority)(func)


def on_private_video(func=None, *, priority=0):
    """私聊视频消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_VIDEO, priority=priority)(f)
    return on_event(EventType.PRIVATE_VIDEO, priority=priority)(func)


def on_private_app(func=None, *, priority=0):
    """私聊小程序消息装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_MINI_APP, priority=priority)(f)
    return on_event(EventType.PRIVATE_MINI_APP, priority=priority)(func)


def on_private_link(func=None, *, priority=0):
    """私聊链接消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_LINK, priority=priority)(f)
    return on_event(EventType.PRIVATE_LINK, priority=priority)(func)


def on_private_quote(func=None, *, priority=0):
    """私聊引用消息事件"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_QUOTE, priority=priority)(f)
    return on_event(EventType.PRIVATE_QUOTE, priority=priority)(func)


def on_private_file(func=None, *, priority=0):
    """私聊文件消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_FILE, priority=priority)(f)
    return on_event(EventType.PRIVATE_FILE, priority=priority)(func)


def on_private_voice(func=None, *, priority=0):
    """私聊语音消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_VOICE, priority=priority)(f)
    return on_event(EventType.PRIVATE_VOICE, priority=priority)(func)


def on_private_card(func=None, *, priority=0):
    """私聊名片消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_CARD, priority=priority)(f)
    return on_event(EventType.PRIVATE_CARD, priority=priority)(func)


def on_private_red_packet(func=None, *, priority=0):
    """私聊红包消息处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.PRIVATE_RED_PACKET, priority=priority)(f)
    return on_event(EventType.PRIVATE_RED_PACKET, priority=priority)(func)


def on_friend_accept(func=None, *, priority=0):
    """好友通过处理装饰器"""
    if func is None:
        return lambda f: on_event(EventType.FRIEND_ACCEPT, priority=priority)(f)
    return on_event(EventType.FRIEND_ACCEPT, priority=priority)(func)


def on_scheduled_task(interval=None, at_time=None, day_of_week=None):
    """
    定时任务装饰器，用于简化定时任务的注册

    用法:
    @on_scheduled_task(interval=60)  # 每60秒执行一次
    def task_every_minute(self):
        # 定时任务代码

    @on_scheduled_task(at_time="08:00")  # 每天早上8点执行
    def task_daily_morning(self):
        # 定时任务代码

    @on_scheduled_task(at_time="10:00", day_of_week="monday")  # 每周一10点执行
    def task_weekly_meeting(self):
        # 定时任务代码

    Args:
        interval: 执行间隔（秒）
        at_time: 在特定时间执行 (格式: "HH:MM")
        day_of_week: 在星期几执行 (例如: "monday", "tuesday", ...)

    Returns:
        装饰器函数
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        # 保存定时任务参数
        wrapper._scheduled_task = {
            'interval': interval,
            'at_time': at_time,
            'day_of_week': day_of_week
        }
        return wrapper

    return decorator

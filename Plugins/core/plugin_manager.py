from loguru import logger
import threading
import importlib
import traceback
import schedule
import inspect
import tomlkit
import os
import sys
from Plugins.core.threads import EventHandler, EventHandlerWithControl, ScheduleThread
from typing import Dict, List, Callable, Optional, Any
from Plugins.core.plugin_base import PluginBase
import time


class PluginManager:
    """插件管理器

    用于管理所有插件的加载、卸载和事件处理。

    示例用法：

    1. 设置插件优先级 (数字越大优先级越高):
    ```python
    # 高优先级处理器
    @on_group_text(priority=100)
    def handle_high_priority(self, message):
        # 处理消息...
        return False  # 阻止后续处理器处理此消息

    # 普通优先级处理器
    @on_group_text
    def handle_normal_priority(self, message):
        # 如果上面的处理器返回了False，这个处理器不会被调用
        pass
    ```

    2. 控制事件传播:
    ```python
    # 在插件中:
    def some_method(self):
        # 阻止事件传播，只有最高优先级处理器会被调用
        pluginManager.set_event_propagation(False)

        # 恢复事件传播，所有处理器按优先级顺序被调用
        pluginManager.set_event_propagation(True)
    ```
    """

    def __init__(self):
        self.plugins: Dict[str, PluginBase] = {}
        self.event_handlers: Dict[int, List[Callable]] = {}
        self.lock = threading.RLock()  # 添加线程锁
        self.scheduled_tasks = []  # 存储已注册的定时任务
        self.scheduler_thread = None  # 定时任务线程
        self.disabled_plugins = []  # 存储被禁用的插件名称
        self.unloaded_plugins = []  # 存储被卸载的插件名称
        self.plugin_folders = {}  # 存储插件名称与文件夹的映射关系
        self.propagate_events = True  # 控制是否默认传播所有事件

        # 初始化时启动调度器
        self.start_scheduler()

    def register_plugin(self, plugin: PluginBase) -> None:
        """注册插件"""
        with self.lock:
            plugin_name = plugin.name

            # 确保调度器线程已启动
            if not self.scheduler_thread or not self.scheduler_thread.is_alive():
                self.start_scheduler()

            # 检查插件是否被禁用
            if plugin_name in self.disabled_plugins:
                logger.warning(f"插件 {plugin_name} 已被禁用，跳过加载")
                return

            # 如果插件已存在，先卸载它以完全清理旧实例
            if plugin_name in self.plugins:

                # 获取旧实例并清理其所有事件处理器
                old_plugin = self.plugins[plugin_name]
                self.unregister_all_handlers_of_plugin(old_plugin)

                # 完全卸载旧插件
                self.unregister_plugin(plugin_name)

                # 确保事件处理器列表不再包含旧实例的任何处理器
                for event_type, handlers in list(self.event_handlers.items()):
                    self.event_handlers[event_type] = [
                        h for h in handlers
                        if not (hasattr(h, '__self__') and type(h.__self__) == type(
                            old_plugin) and h.__self__.name == plugin_name)
                    ]

            # 注册新的插件实例
            self.plugins[plugin_name] = plugin

            # 调用插件的加载方法
            plugin.on_load()

            # 插件加载后，注册其事件处理器
            self._register_plugin_handlers(plugin)

            # 如果插件在卸载列表中，现在将其移除
            if plugin_name in self.unloaded_plugins:
                self.unloaded_plugins.remove(plugin_name)

    def _register_plugin_handlers(self, plugin: PluginBase) -> None:
        """注册插件的事件处理器"""
        for event_type, handlers in plugin._event_handlers.items():
            for handler in handlers:
                self.register_event_handler(event_type, handler)

    def unregister_plugin(self, plugin_name: str) -> bool:
        """注销插件"""
        with self.lock:
            # 检查是否是插件管理器，禁止卸载
            if plugin_name == "NGC插件管理器" or plugin_name == "PluginManager":
                logger.warning(f"禁止卸载插件管理器: {plugin_name}")
                return False

            if plugin_name in self.plugins:
                # 取消该插件注册的所有定时任务
                self._cancel_plugin_tasks(plugin_name)

                # 取消该插件的所有事件处理器
                plugin = self.plugins[plugin_name]
                self.unregister_all_handlers_of_plugin(plugin)

                # 调用插件的卸载方法
                self.plugins[plugin_name].on_unload()

                # 将插件加入卸载列表（除了插件管理器）
                if plugin_name not in self.unloaded_plugins:
                    self.unloaded_plugins.append(plugin_name)

                # 从插件列表中移除
                del self.plugins[plugin_name]

                logger.success(f"插件 {plugin_name} 已卸载")
                return True
            return False

    def _cancel_plugin_tasks(self, plugin_name: str) -> None:
        """取消指定插件的所有定时任务"""
        for task_info in list(self.scheduled_tasks):
            if task_info.get('plugin_name') == plugin_name:
                if task_info.get('job') in schedule.jobs:
                    schedule.cancel_job(task_info.get('job'))
                self.scheduled_tasks.remove(task_info)

    def unregister_all_plugins(self) -> int:
        """卸载所有插件"""
        with self.lock:
            count = 0
            for plugin_name in list(self.plugins.keys()):
                # 不卸载插件管理器
                if plugin_name != "PluginManager" and plugin_name != "NGC插件管理器":
                    self.unregister_plugin(plugin_name)
                    count += 1
            return count

    def register_event_handler(self, event_type: int, handler: Callable) -> None:
        """注册事件处理器"""
        with self.lock:
            if event_type not in self.event_handlers:
                self.event_handlers[event_type] = []

            # 获取处理器优先级
            priority = getattr(handler, '_priority', 0)

            # 检查处理器是否已经注册，防止重复注册
            for existing_handler in self.event_handlers[event_type]:
                # 检查是否相同函数引用或者相同的绑定方法（相同实例的相同方法）
                if (handler == existing_handler or
                        (hasattr(handler, '__self__') and hasattr(existing_handler, '__self__') and
                         handler.__self__ == existing_handler.__self__ and
                         handler.__func__ == existing_handler.__func__)):
                    logger.debug(
                        f"跳过重复注册的事件处理器: {event_type} - {handler.__qualname__ if hasattr(handler, '__qualname__') else str(handler)}")
                    return

                # 增强检测：通过检查方法名和实例类型
                if (hasattr(handler, '__self__') and hasattr(existing_handler, '__self__') and
                        type(handler.__self__) == type(existing_handler.__self__) and
                        handler.__name__ == existing_handler.__name__):
                    logger.debug(
                        f"跳过可能重复的事件处理器: {event_type} - {handler.__qualname__ if hasattr(handler, '__qualname__') else str(handler)}")
                    return

            # 记录日志并添加处理器
            handler_name = handler.__qualname__ if hasattr(handler, '__qualname__') else str(handler)
            plugin_name = handler.__self__.name if hasattr(handler, '__self__') and hasattr(handler.__self__,
                                                                                            'name') else "未知插件"

            # 将优先级信息与处理器一起保存
            handler_info = {
                'handler': handler,
                'priority': priority,
                'plugin_name': plugin_name,
                'handler_name': handler_name
            }

            self.event_handlers[event_type].append(handler_info)
            # logger.debug(f"已注册事件处理器: {event_type} - {plugin_name}.{handler_name} (优先级: {priority})")

    def unregister_all_handlers_of_plugin(self, plugin: PluginBase) -> None:
        """移除指定插件实例的所有事件处理器

        @param plugin: 要移除事件处理器的插件实例
        """
        with self.lock:
            plugin_name = plugin.name

            total_removed = 0
            # 遍历所有事件类型
            for event_type in list(self.event_handlers.keys()):
                # 过滤掉属于该插件的处理器
                handlers_to_keep = []
                removed_count = 0

                for handler_info in self.event_handlers[event_type]:
                    handler = handler_info['handler']
                    # 检查处理器是否属于该插件（通过__self__属性判断）
                    is_plugin_handler = False

                    # 方法1：通过实例引用判断
                    if hasattr(handler, '__self__') and handler.__self__ == plugin:
                        is_plugin_handler = True

                    # 方法2：通过类型和名称判断
                    elif (hasattr(handler, '__self__') and
                          type(handler.__self__) == type(plugin) and
                          hasattr(handler.__self__, 'name') and
                          handler.__self__.name == plugin_name):
                        is_plugin_handler = True

                    # 保留不属于此插件的处理器
                    if is_plugin_handler:
                        removed_count += 1
                        handler_name = handler.__name__ if hasattr(handler, '__name__') else str(handler)
                    else:
                        handlers_to_keep.append(handler_info)

                # 更新处理器列表
                if removed_count > 0:
                    self.event_handlers[event_type] = handlers_to_keep
                    total_removed += removed_count

            logger.debug(f"已完成移除插件 {plugin_name} 的所有事件处理器，共 {total_removed} 个")

    def handle_event(self, event_type: int, message: dict) -> None:
        """处理事件

        处理指定类型的事件，按照处理器的优先级依次调用，支持事件传播控制。
        如果处理器返回False，事件将不再传递给后续处理器。

        Args:
            event_type: 事件类型
            message: 事件消息数据
        """
        with self.lock:
            handlers_info = self.event_handlers.get(event_type, [])
            if not handlers_info:
                # logger.debug(f"没有找到事件类型 {event_type} 的处理器")
                return

            # 按优先级排序处理器（优先级高的先执行）
            sorted_handlers = sorted(handlers_info, key=lambda h: h['priority'], reverse=True)

        # 在锁外处理事件，避免长时间持有锁
        threads = []
        event_propagate = True  # 控制事件是否继续传播

        if not self.propagate_events:
            # 如果全局禁用了事件传播，直接将最高优先级的处理器加入线程
            if sorted_handlers:
                handler_info = sorted_handlers[0]
                handler = handler_info['handler']
                thread = EventHandler(handler, message)
                thread.start()
                threads.append(thread)
                logger.debug(
                    f"事件 {event_type} 只由最高优先级处理器处理: {handler_info['plugin_name']}.{handler_info['handler_name']}")
        else:
            # 顺序处理，支持中断传播
            for handler_info in sorted_handlers:
                if not event_propagate:
                    break

                handler = handler_info['handler']

                # 使用特殊的EventHandlerWithControl线程，支持控制传播
                thread = EventHandlerWithControl(handler, message)
                thread.start()
                threads.append(thread)

                # 等待线程结束以检查传播控制
                thread.join(timeout=2.0)  # 增加超时时间，确保能够正确获取结果

                # 如果超时后没完成，继续等待
                if not thread.is_completed:
                    try:
                        # 最多再等待3秒
                        start_time = time.time()
                        while not thread.is_completed and time.time() - start_time < 3:
                            time.sleep(0.1)
                    except Exception as e:
                        logger.error(f"等待事件处理线程时出错: {e}")

                # 检查处理结果，如果明确返回False则停止传播
                if thread.is_completed:
                    if thread.result is False:
                        event_propagate = False
                        logger.debug(
                            f"事件 {event_type} 传播被 {handler_info['plugin_name']}.{handler_info['handler_name']} 中断")
                        # 添加更明确的日志
                        logger.info(
                            f"事件 {event_type} 中断传播，不再继续处理其他插件")

        # 注意：我们不等待所有线程完成，让它们在后台继续执行

    def get_plugin(self, plugin_name: str) -> Optional[PluginBase]:
        """获取插件实例"""
        with self.lock:
            return self.plugins.get(plugin_name)

    def get_plugin_list(self, include_unloaded: bool = True) -> List[Dict]:
        """获取插件列表

        Args:
            include_unloaded: 是否包含未加载的插件

        Returns:
            List[Dict]: 插件信息列表
        """
        with self.lock:
            # 获取已加载插件的信息，使用插件自己的属性而不是文件夹名
            plugin_list = [
                {
                    "name": plugin.name,
                    "description": plugin.description,
                    "version": plugin.version,
                    "author": plugin.author,
                    "status": "已加载",
                    "folder": getattr(plugin, '_plugin_folder', '')  # 添加文件夹名属性
                }
                for plugin in self.plugins.values()
            ]

            # 如果需要包含未加载的插件
            if include_unloaded:
                # 记录已添加插件的名称，避免重复
                added_names = set(p["name"] for p in plugin_list)

                # 添加未加载的插件
                for plugin_name in self.unloaded_plugins:
                    if '.' not in plugin_name:  # 跳过类名格式
                        # 获取真实name
                        real_name = self.get_plugin_real_name(plugin_name)

                        if real_name not in added_names:
                            added_names.add(real_name)
                            plugin_list.append({
                                "name": real_name,
                                "description": "插件未加载",
                                "version": "未知",
                                "author": "未知",
                                "status": "已卸载",
                                "folder": plugin_name
                            })

                # 添加已禁用的插件
                for plugin_name in self.disabled_plugins:
                    if '.' not in plugin_name and plugin_name not in self.unloaded_plugins:  # 跳过类名格式和已卸载的
                        # 获取真实name
                        real_name = self.get_plugin_real_name(plugin_name)

                        if real_name not in added_names:
                            added_names.add(real_name)
                            plugin_list.append({
                                "name": real_name,
                                "description": "插件已禁用",
                                "version": "未知",
                                "author": "未知",
                                "status": "已禁用",
                                "folder": plugin_name
                            })

            return plugin_list

    def get_enabled_plugins(self) -> List[Dict]:
        """获取已启用的插件列表"""
        with self.lock:
            # 读取所有插件目录的配置
            enabled_plugins = []

            for folder_name in os.listdir("Plugins"):
                folder_path = os.path.join("Plugins", folder_name)

                # 跳过非目录和特殊目录
                if not os.path.isdir(folder_path) or folder_name.startswith('_') or folder_name.startswith(
                        '.') or folder_name == "core":
                    continue

                # 检查配置文件
                config_path = os.path.join(folder_path, "config.toml")
                if os.path.exists(config_path):
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = tomlkit.parse(f.read())
                            if config and config.get("enabled", True):
                                # 尝试获取插件类名和描述
                                plugin_info = {
                                    "name": folder_name,
                                    "path": folder_path,
                                    "status": "已启用但未加载"
                                }

                                # 检查是否已加载
                                for plugin in self.plugins.values():
                                    if plugin.name == folder_name or (
                                            hasattr(plugin, '_plugin_folder') and plugin._plugin_folder == folder_name):
                                        plugin_info.update({
                                            "name": plugin.name,
                                            "description": plugin.description,
                                            "version": plugin.version,
                                            "author": plugin.author,
                                            "status": "已加载"
                                        })
                                        break

                                enabled_plugins.append(plugin_info)
                    except Exception as e:
                        logger.error(f"读取插件 {folder_name} 配置时出错: {e}")

            return enabled_plugins

    def _load_plugin_config(self, folder_path: str) -> Dict:
        """加载插件TOML配置文件"""
        # 加载TOML配置
        config_path_toml = os.path.join(folder_path, "config.toml")
        if os.path.exists(config_path_toml):
            try:
                with open(config_path_toml, 'r', encoding='utf-8') as f:
                    config = tomlkit.parse(f.read())
                    return dict(config) if config else {}
            except Exception as e:
                logger.error(f"加载TOML配置文件 {config_path_toml} 失败: {e}")

        # 如果配置文件不存在，创建默认配置（使用TOML格式）
        if not os.path.exists(config_path_toml):
            try:
                config_path = config_path_toml

                # 创建默认配置
                doc = tomlkit.document()
                doc.add(tomlkit.comment("插件配置文件"))
                doc.add(tomlkit.nl())
                doc["enabled"] = True
                doc.add(tomlkit.nl())

                settings_table = tomlkit.table()
                doc["settings"] = settings_table

                with open(config_path, 'w', encoding='utf-8') as f:
                    f.write(tomlkit.dumps(doc))

                return {"enabled": True, "settings": {}}
            except Exception as e:
                logger.error(f"创建默认配置文件失败: {e}")
                return {"enabled": True, "settings": {}}

        # 如果配置文件存在但加载失败，返回默认配置
        return {"enabled": True, "settings": {}}

    def load_plugins(self, plugins_dir: str = "Plugins", target_plugin: str = None) -> bool:
        """从指定目录加载所有插件

        Args:
            plugins_dir: 插件目录路径
            target_plugin: 指定加载的插件名称，如果提供，则只加载该插件

        Returns:
            bool: 是否成功加载插件
        """
        with self.lock:

            if target_plugin is None:
                # 重置禁用插件列表，后面会从配置文件中重新读取
                self.disabled_plugins = []
                # 不清空unloaded_plugins，保持卸载状态

            loaded_success = False  # 用于跟踪是否至少成功加载了一个插件

            for folder_name in os.listdir(plugins_dir):
                folder_path = os.path.join(plugins_dir, folder_name)

                # 跳过非目录和特殊目录
                if not os.path.isdir(folder_path) or folder_name.startswith('_') or folder_name.startswith(
                        '.') or folder_name == "core" or folder_name == "Tools":
                    continue

                # 如果指定了目标插件，只处理匹配的文件夹或包含该插件的文件夹
                if target_plugin and (
                        folder_name != target_plugin and not self.is_plugin_in_folder(target_plugin, folder_path)):
                    continue

                # 加载插件配置
                config = self._load_plugin_config(folder_path)

                # 检查插件是否启用
                if not config.get("enabled", True):
                    logger.info(f"插件 {folder_name} 已禁用，跳过加载")
                    if folder_name not in self.disabled_plugins:
                        self.disabled_plugins.append(folder_name)
                    continue

                main_file = os.path.join(folder_path, "main.py")

                # 如果目录中存在main.py文件，尝试加载插件
                if os.path.exists(main_file):
                    try:
                        # 构建模块名
                        module_name = f"{plugins_dir}.{folder_name}.main"

                        # 清除所有相关模块的缓存
                        modules_to_remove = []
                        for name in list(sys.modules.keys()):
                            if name == module_name or name.startswith(f"{module_name}."):
                                modules_to_remove.append(name)

                        for name in modules_to_remove:
                            if name in sys.modules:
                                del sys.modules[name]

                        # 强制刷新导入缓存
                        importlib.invalidate_caches()

                        # 尝试清除__pycache__目录中的文件
                        try:
                            cache_dir = os.path.join(plugins_dir, folder_name, "__pycache__")
                            if os.path.exists(cache_dir):
                                for cached_file in os.listdir(cache_dir):
                                    if cached_file.startswith("main."):
                                        cached_path = os.path.join(cache_dir, cached_file)
                                        try:
                                            os.remove(cached_path)
                                            # logger.debug(f"已删除缓存文件: {cached_path}")
                                        except Exception as e:
                                            logger.debug(f"无法删除缓存文件 {cached_path}: {e}")
                        except Exception as e:
                            logger.debug(f"清理__pycache__目录时出错: {e}")

                        # 导入模块
                        module = importlib.import_module(module_name)

                        # 查找并实例化插件类
                        found_plugin = False
                        for class_name, obj in inspect.getmembers(module):
                            if (inspect.isclass(obj) and
                                    issubclass(obj, PluginBase) and
                                    obj is not PluginBase):
                                try:
                                    # 创建插件实例
                                    plugin_instance = obj()
                                    instance_name = plugin_instance.name

                                    # 存储插件与文件夹的关系
                                    self.plugin_folders[instance_name] = folder_name

                                    # 将文件夹名添加到插件实例，便于后续查找
                                    if not hasattr(plugin_instance, '_plugin_folder'):
                                        plugin_instance._plugin_folder = folder_name

                                    if (instance_name in self.unloaded_plugins and
                                            (target_plugin != instance_name and
                                             target_plugin != folder_name) and
                                            instance_name != "NGC插件管理器" and
                                            instance_name != "PluginManager"):
                                        logger.info(f"插件 {instance_name} 已被卸载，跳过加载")
                                        continue

                                    # 当插件名与文件夹名不同时，记录文件夹名为可能的禁用名称
                                    # 但只有在该文件夹对应的配置文件已禁用时才添加
                                    if instance_name != folder_name:
                                        # 检查文件夹对应的配置是否禁用
                                        folder_config = self._load_plugin_config(folder_path)
                                        if not folder_config.get("enabled", True):
                                            if folder_name not in self.disabled_plugins:
                                                self.disabled_plugins.append(folder_name)

                                    # 注册插件
                                    self.register_plugin(plugin_instance)
                                    found_plugin = True
                                    loaded_success = True  # 至少成功加载了一个插件

                                    # 如果是目标插件，找到后就可以结束循环
                                    if target_plugin and (
                                            instance_name == target_plugin or folder_name == target_plugin):
                                        return True
                                    break
                                except Exception as e:
                                    logger.error(f"加载插件类 {class_name} 失败: {e}")
                                    logger.error(traceback.format_exc())

                        if not found_plugin:
                            logger.debug(f"在模块 {module_name} 中未找到有效的插件")
                    except Exception as e:
                        logger.error(f"加载插件 {folder_name} 时出错: {e}")
                        logger.error(traceback.format_exc())

            return loaded_success  # 返回是否成功加载了至少一个插件

    def reload_all_plugins(self, include_unloaded: bool = False) -> int:
        """重新加载所有插件

        Args:
            include_unloaded: 是否重新加载被卸载的插件

        Returns:
            int: 成功重载的插件数量
        """
        with self.lock:

            # 保存当前插件列表，用于计算新加载的插件数量
            old_plugins = set(self.plugins.keys())

            # 保存禁用列表
            disabled_backup = self.disabled_plugins.copy()

            # 保存卸载列表
            if include_unloaded:
                # 备份卸载列表，仅用于日志记录
                unloaded_backup = self.unloaded_plugins.copy()
                # 清空卸载列表，允许所有插件加载
                self.unloaded_plugins = []
            else:
                # 否则备份卸载列表
                unloaded_backup = self.unloaded_plugins.copy()

            # 卸载所有插件（除了插件管理器）
            self.unregister_all_plugins()

            # 恢复卸载列表（如果不包括被卸载的插件）
            if not include_unloaded:
                self.unloaded_plugins = unloaded_backup

            # 确保事件处理器列表干净
            for event_type in list(self.event_handlers.keys()):
                # 只保留插件管理器的处理器
                self.event_handlers[event_type] = [
                    h for h in self.event_handlers[event_type]
                    if hasattr(h, '__self__') and (
                            h.__self__.name == "NGC插件管理器" or
                            h.__self__.name == "PluginManager"
                    )
                ]

            # 重新加载所有插件
            load_result = self.load_plugins()

            # 计算成功加载的插件数量（排除插件管理器）
            new_plugins = set(self.plugins.keys())

            # 排除插件管理器的两种可能名称
            excluded_plugins = {"NGC插件管理器", "PluginManager"}
            loaded_plugins = [plugin for plugin in new_plugins if plugin not in excluded_plugins]
            loaded_count = len(loaded_plugins)

            logger.debug(f"成功重载了 {loaded_count} 个插件: {loaded_plugins}")
            logger.debug(f"重载后的卸载列表: {self.unloaded_plugins}")
            logger.debug(f"重载后的禁用列表: {self.disabled_plugins}")

            # 如果启用了unloaded_plugins但没有加载任何插件
            if include_unloaded and loaded_count == 0:
                # 检查所有插件目录，看是否有启用的插件
                enabled_count = 0
                disabled_count = 0
                for folder_name in os.listdir("Plugins"):
                    folder_path = os.path.join("Plugins", folder_name)
                    if not os.path.isdir(folder_path) or folder_name.startswith('_') or folder_name.startswith(
                            '.') or folder_name == "core":
                        continue

                    config_path = os.path.join(folder_path, "config.toml")
                    if os.path.exists(config_path):
                        try:
                            with open(config_path, 'r', encoding='utf-8') as f:
                                config = tomlkit.parse(f.read())
                                if config and config.get("enabled", True):
                                    enabled_count += 1
                                else:
                                    disabled_count += 1
                        except Exception as e:
                            logger.error(f"读取插件配置时出错: {e}")

                if enabled_count > 0:
                    logger.warning(
                        f"尝试重载所有插件(包括已卸载)，但未成功加载任何插件。有 {enabled_count} 个插件被标记为启用，可能存在插件代码错误。")
                else:
                    logger.warning(
                        f"尝试重载所有插件(包括已卸载)，但未成功加载任何插件，所有 {disabled_count} 个检测到的插件都被禁用了。")

            logger.success(f"已重新加载 {loaded_count} 个插件")
            return loaded_count

    def enable_plugin(self, plugin_name: str) -> bool:
        """启用插件"""
        with self.lock:
            # 查找插件对应的文件夹
            folder_name = self.plugin_folders.get(plugin_name, plugin_name)

            # 找到插件对应的目录
            for folder in os.listdir("Plugins"):
                folder_path = os.path.join("Plugins", folder)
                if not os.path.isdir(folder_path) or folder.startswith('_') or folder == "core":
                    continue

                # 检查TOML配置
                config_path = os.path.join(folder_path, "config.toml")

                # 检查是否是目标插件
                if folder == folder_name or self.is_plugin_in_folder(plugin_name, folder_path):
                    # 更新配置
                    try:
                        if os.path.exists(config_path):
                            # 加载现有配置
                            with open(config_path, 'r', encoding='utf-8') as f:
                                config = tomlkit.parse(f.read())
                                config["enabled"] = True
                        else:
                            # 创建新的TOML配置
                            config = tomlkit.document()
                            config.add(tomlkit.comment("插件配置文件"))
                            config.add(tomlkit.nl())
                            config["enabled"] = True

                        # 保存配置
                        with open(config_path, 'w', encoding='utf-8') as f:
                            f.write(tomlkit.dumps(config))

                        # 从禁用列表中移除
                        if plugin_name in self.disabled_plugins:
                            self.disabled_plugins.remove(plugin_name)
                        if folder in self.disabled_plugins:
                            self.disabled_plugins.remove(folder)

                        # 从卸载列表中移除
                        if plugin_name in self.unloaded_plugins:
                            self.unloaded_plugins.remove(plugin_name)
                            logger.debug(f"插件 {plugin_name} 已从卸载列表中移除")

                        logger.success(f"插件 {plugin_name} 已启用，重新加载后生效")
                        return True
                    except Exception as e:
                        logger.error(f"启用插件 {plugin_name} 失败: {e}")
                        logger.error(traceback.format_exc())
                        return False

            logger.warning(f"未找到插件 {plugin_name}")
            return False

    def disable_plugin(self, plugin_name: str) -> bool:
        """禁用插件"""
        with self.lock:
            # 检查是否是插件管理器，禁止禁用
            if plugin_name == "NGC插件管理器" or plugin_name == "PluginManager":
                logger.warning(f"禁止禁用插件管理器: {plugin_name}")
                return False

            # 查找插件对应的文件夹
            folder_name = self.plugin_folders.get(plugin_name, plugin_name)

            # 找到插件对应的目录
            for folder in os.listdir("Plugins"):
                folder_path = os.path.join("Plugins", folder)
                if not os.path.isdir(folder_path) or folder.startswith('_') or folder == "core":
                    continue

                # 检查TOML配置
                config_path = os.path.join(folder_path, "config.toml")

                # 检查是否是目标插件
                if folder == folder_name or self.is_plugin_in_folder(plugin_name, folder_path):
                    # 更新配置
                    try:
                        if os.path.exists(config_path):
                            # 加载现有配置
                            with open(config_path, 'r', encoding='utf-8') as f:
                                config = tomlkit.parse(f.read())
                                config["enabled"] = False
                        else:
                            # 创建新的TOML配置
                            config = tomlkit.document()
                            config.add(tomlkit.comment("插件配置文件"))
                            config.add(tomlkit.nl())
                            config["enabled"] = False

                        # 保存配置
                        with open(config_path, 'w', encoding='utf-8') as f:
                            f.write(tomlkit.dumps(config))

                        # 获取插件的真实name属性
                        real_name = self.get_plugin_real_name(folder)
                        logger.debug(f"获取到插件 {folder} 的真实名称: {real_name}")

                        # 添加真实name到禁用列表（优先使用插件的name属性）
                        if real_name and real_name not in self.disabled_plugins and '.' not in real_name:
                            self.disabled_plugins.append(real_name)
                            logger.debug(f"添加真实name到禁用列表: {real_name}")

                        # 只有在文件夹名称不是类名格式且不重复时才添加
                        if folder not in self.disabled_plugins and folder != real_name and '.' not in folder:
                            self.disabled_plugins.append(folder)
                            logger.debug(f"添加文件夹名到禁用列表: {folder}")

                        # 移除可能存在的类名格式的禁用项
                        self.disabled_plugins = [p for p in self.disabled_plugins if '.' not in p]

                        # 如果插件已加载，卸载它
                        plugin = self.get_plugin(plugin_name)
                        if plugin:
                            self.unregister_plugin(plugin_name)

                        logger.success(f"插件 {real_name or plugin_name} 已禁用")
                        return True
                    except Exception as e:
                        logger.error(f"禁用插件 {plugin_name} 失败: {e}")
                        logger.error(traceback.format_exc())
                        return False

            logger.warning(f"未找到插件 {plugin_name}")
            return False

    def is_plugin_manager(self, folder_path: str) -> bool:
        """判断指定目录是否包含插件管理器"""
        try:
            # 简单的检查是否是PluginManager文件夹
            folder_name = os.path.basename(folder_path)
            if folder_name == "PluginManager":
                return True

            # 更详细的检查，尝试加载并检查插件名称
            main_file = os.path.join(folder_path, "main.py")
            if os.path.exists(main_file):
                module_name = f"Plugins.{folder_name}.main"
                spec = importlib.util.find_spec(module_name)
                if spec:
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    for class_name, obj in inspect.getmembers(module):
                        if (inspect.isclass(obj) and
                                issubclass(obj, PluginBase) and
                                obj is not PluginBase):
                            instance = obj()
                            if instance.name == "NGC插件管理器" or instance.name == "PluginManager":
                                return True
            return False
        except Exception:
            return False

    def is_plugin_in_folder(self, plugin_name: str, folder_path: str) -> bool:
        """检查指定文件夹中是否包含给定名称的插件

        Args:
            plugin_name: 插件名称
            folder_path: 插件文件夹路径

        Returns:
            bool: 如果文件夹中包含该插件，则返回True
        """
        main_file = os.path.join(folder_path, "main.py")
        if not os.path.exists(main_file):
            return False

        try:
            # 获取模块名
            folder_name = os.path.basename(folder_path)
            module_name = f"Plugins.{folder_name}.main"

            # 尝试导入模块
            spec = importlib.util.find_spec(module_name)
            if spec is None:
                return False

            # 尝试加载模块
            try:
                # 如果模块已经加载，获取已加载的模块
                if module_name in sys.modules:
                    module = sys.modules[module_name]
                else:
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                # 检查模块中的插件类
                found_plugins = []
                for class_name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and issubclass(obj, PluginBase) and obj is not PluginBase):
                        try:
                            # 创建临时实例检查名称
                            temp_instance = obj()
                            instance_name = temp_instance.name if hasattr(temp_instance, 'name') else None
                            found_plugins.append((class_name, instance_name))

                            if instance_name == plugin_name:
                                logger.debug(f"在文件夹 {folder_name} 中找到插件 {plugin_name} (类: {class_name})")
                                return True
                        except Exception as e:
                            logger.debug(f"创建插件 {class_name} 临时实例时出错: {e}")

                if found_plugins:
                    logger.debug(f"在文件夹 {folder_name} 中找到插件，但名称不匹配。找到: {found_plugins}")
                else:
                    logger.debug(f"在文件夹 {folder_name} 中未找到任何PluginBase子类")

                # 如果没有找到匹配的插件
                return False
            except Exception as e:
                logger.debug(f"检查模块 {module_name} 中的插件时出错: {e}")
                if hasattr(e, "__traceback__"):
                    logger.debug(traceback.format_tb(e.__traceback__))
                return False
        except Exception as e:
            logger.debug(f"检查文件夹 {folder_path} 中是否包含插件 {plugin_name} 时出错: {e}")
            return False

    def start_scheduler(self) -> None:
        """启动定时任务调度器"""
        if self.scheduler_thread is None or not self.scheduler_thread.is_alive():
            self.scheduler_thread = ScheduleThread()
            self.scheduler_thread.start()
            logger.success("定时任务调度器已启动")

    def stop_scheduler(self) -> None:
        """停止定时任务调度器"""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.stop()
            self.scheduler_thread.join(timeout=2)  # 等待线程结束，最多等待2秒
            logger.success("定时任务调度器已停止")

    def pause_scheduler(self) -> None:
        """暂停定时任务调度器"""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.pause()

    def resume_scheduler(self) -> None:
        """恢复定时任务调度器"""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.resume()

    def register_scheduled_task(self, plugin_name: str, task_name: str, callback: Callable,
                                interval: Optional[int] = None,
                                at_time: Optional[str] = None,
                                day_of_week: Optional[str] = None) -> bool:
        """
        注册定时任务

        Args:
            plugin_name: 插件名称
            task_name: 任务名称
            callback: 任务回调函数
            interval: 执行间隔（秒）
            at_time: 在特定时间执行 (格式: "HH:MM")
            day_of_week: 在星期几执行 (例如: "monday", "tuesday", ...)

        Returns:
            bool: 是否成功注册
        """
        with self.lock:
            try:
                # 确保定时任务调度器已启动
                if not self.scheduler_thread or not self.scheduler_thread.is_alive():
                    self.start_scheduler()

                job = None

                # 根据参数选择定时任务类型
                if interval:
                    job = schedule.every(interval).seconds.do(self._task_wrapper, callback, plugin_name, task_name)
                elif at_time:
                    if day_of_week:
                        # 特定星期几的特定时间执行
                        job = getattr(schedule.every(), day_of_week).at(at_time).do(
                            self._task_wrapper, callback, plugin_name, task_name)
                    else:
                        # 每天特定时间执行
                        job = schedule.every().day.at(at_time).do(
                            self._task_wrapper, callback, plugin_name, task_name)
                else:
                    logger.error(f"注册定时任务失败: 必须指定 interval 或 at_time 参数")
                    return False

                # 记录定时任务信息
                task_info = {
                    'plugin_name': plugin_name,
                    'name': task_name,
                    'job': job,
                    'interval': interval,
                    'at_time': at_time,
                    'day_of_week': day_of_week
                }
                self.scheduled_tasks.append(task_info)

                logger.success(f"插件 {plugin_name} 成功注册定时任务: {task_name}")
                return True
            except Exception as e:
                logger.error(f"注册定时任务失败: {e}")
                return False

    def _task_wrapper(self, callback: Callable, plugin_name: str, task_name: str) -> None:
        """
        定时任务包装函数，用于处理异常并记录日志
        """
        try:
            logger.debug(f"执行定时任务: 插件={plugin_name}, 任务={task_name}")
            callback()
        except Exception as e:
            logger.error(f"执行定时任务出错: 插件={plugin_name}, 任务={task_name}, 错误={e}")
            logger.error(traceback.format_exc())

    def get_scheduled_tasks(self) -> List[Dict[str, Any]]:
        """获取所有已注册的定时任务"""
        with self.lock:
            return [
                {
                    'plugin_name': task['plugin_name'],
                    'name': task['name'],
                    'interval': task['interval'],
                    'at_time': task['at_time'],
                    'day_of_week': task['day_of_week']
                }
                for task in self.scheduled_tasks
            ]

    def cancel_scheduled_task(self, plugin_name: str, task_name: str) -> bool:
        """取消特定定时任务"""
        with self.lock:
            for task_info in list(self.scheduled_tasks):
                if (task_info.get('plugin_name') == plugin_name and
                        task_info.get('name') == task_name):
                    if task_info.get('job') in schedule.jobs:
                        schedule.cancel_job(task_info.get('job'))
                    self.scheduled_tasks.remove(task_info)
                    logger.success(f"已取消插件 {plugin_name} 的定时任务: {task_name}")
                    return True
            logger.warning(f"未找到插件 {plugin_name} 的定时任务: {task_name}")
            return False

    def reload_plugin(self, plugin_name):
        """
        重新加载一个插件

        Args:
            plugin_name: 要重新加载的插件名称

        Returns:
            bool: 是否成功重新加载
        """
        try:
            # 保存原始插件文件夹信息
            folder_name = None
            if plugin_name in self.plugin_folders:
                folder_name = self.plugin_folders[plugin_name]
            else:
                # 检查插件名是否直接是文件夹名
                for folder in os.listdir("Plugins"):
                    folder_path = os.path.join("Plugins", folder)
                    if (os.path.isdir(folder_path) and
                            not folder.startswith('_') and
                            not folder.startswith('.') and
                            folder != "core"):
                        if folder == plugin_name or self.is_plugin_in_folder(plugin_name, folder_path):
                            folder_name = folder
                            break

            # 检查插件是否被卸载
            if plugin_name in self.unloaded_plugins:
                # 从卸载列表中移除，允许重新加载
                self.unloaded_plugins.remove(plugin_name)

            # 先保存插件实例，以便移除事件处理器
            old_plugin = None
            if plugin_name in self.plugins:
                old_plugin = self.plugins[plugin_name]

            # 移除所有该插件的事件处理器
            if old_plugin:
                self.unregister_all_handlers_of_plugin(old_plugin)

            # 卸载旧插件实例
            self.unregister_plugin(plugin_name)

            # 直接从指定目录加载插件
            if folder_name:
                try:
                    # 构建插件模块路径
                    module_name = f"Plugins.{folder_name}.main"

                    # 清除所有相关模块的缓存
                    modules_to_remove = []
                    for name in list(sys.modules.keys()):
                        if name == module_name or name.startswith(f"{module_name}."):
                            modules_to_remove.append(name)

                    for name in modules_to_remove:
                        if name in sys.modules:
                            del sys.modules[name]

                    # 强制刷新导入缓存
                    importlib.invalidate_caches()

                    # 清理__pycache__目录中的文件 (可选，但更彻底)
                    try:
                        cache_dir = os.path.join("Plugins", folder_name, "__pycache__")
                        if os.path.exists(cache_dir):
                            for cached_file in os.listdir(cache_dir):
                                if cached_file.startswith("main."):
                                    cached_path = os.path.join(cache_dir, cached_file)
                                    try:
                                        os.remove(cached_path)
                                        # logger.debug(f"已删除缓存文件: {cached_path}")
                                    except Exception as e:
                                        logger.debug(f"无法删除缓存文件 {cached_path}: {e}")
                    except Exception as e:
                        logger.debug(f"清理__pycache__目录时出错: {e}")

                    # 尝试导入模块
                    module = importlib.import_module(module_name)

                    # 查找并实例化插件类
                    loaded = False
                    for class_name, obj in inspect.getmembers(module):
                        if (inspect.isclass(obj) and
                                issubclass(obj, PluginBase) and
                                obj is not PluginBase):
                            try:
                                # 创建临时实例，检查name属性
                                temp_instance = obj()
                                instance_name = temp_instance.name

                                # 使用实例的name属性进行比较，而不是类名
                                if instance_name == plugin_name or folder_name == plugin_name:
                                    # 注册新的插件实例
                                    self.register_plugin(temp_instance)
                                    logger.success(f"插件 {instance_name} 已重新加载")
                                    loaded = True
                                    return True
                            except Exception as e:
                                logger.error(f"实例化插件类 {class_name} 失败: {e}")
                                logger.error(traceback.format_exc())

                    if not loaded:
                        logger.debug(f"未找到匹配的插件，名称: {plugin_name}")
                except Exception as e:
                    logger.error(f"从文件夹 {folder_name} 加载插件 {plugin_name} 时出错: {e}")
                    logger.error(traceback.format_exc())

            # 如果直接加载失败，尝试通过扫描所有文件夹找到插件
            result = self.load_plugins(target_plugin=plugin_name)
            if result:
                logger.success(f"通过扫描文件夹成功重新加载插件 {plugin_name}")
                return True
            else:
                logger.error(f"无法重新加载插件 {plugin_name}")
                return False
        except Exception as e:
            logger.error(f"重新加载插件 {plugin_name} 时发生错误: {e}")
            logger.error(traceback.format_exc())
            return False

    def reload_unloaded_plugin(self, plugin_name: str) -> bool:
        """
        重新加载一个已卸载的插件

        Args:
            plugin_name: 要重新加载的已卸载插件名称

        Returns:
            bool: 是否成功重新加载
        """
        with self.lock:
            # 检查插件是否在卸载列表中
            is_unloaded = plugin_name in self.unloaded_plugins
            folder_name = None

            if not is_unloaded:
                # 检查文件夹名是否存在于卸载列表中
                for folder in os.listdir("Plugins"):
                    folder_path = os.path.join("Plugins", folder)
                    if (os.path.isdir(folder_path) and
                            not folder.startswith('_') and
                            not folder.startswith('.') and
                            folder != "core"):
                        if folder == plugin_name:
                            logger.debug(f"以文件夹名匹配到插件 {plugin_name}")
                            folder_name = folder
                            found_in_unloaded = False
                            # 检查文件夹中的插件是否在卸载列表中
                            main_file = os.path.join(folder_path, "main.py")
                            if os.path.exists(main_file):
                                try:
                                    module_name = f"Plugins.{folder}.main"

                                    # 清除所有相关模块的缓存
                                    modules_to_remove = []
                                    for name in list(sys.modules.keys()):
                                        if name == module_name or name.startswith(f"{module_name}."):
                                            modules_to_remove.append(name)

                                    for name in modules_to_remove:
                                        if name in sys.modules:
                                            del sys.modules[name]

                                    importlib.invalidate_caches()

                                    # 尝试清除__pycache__目录中的文件
                                    try:
                                        cache_dir = os.path.join("Plugins", folder, "__pycache__")
                                        if os.path.exists(cache_dir):
                                            for cached_file in os.listdir(cache_dir):
                                                if cached_file.startswith("main."):
                                                    cached_path = os.path.join(cache_dir, cached_file)
                                                    try:
                                                        os.remove(cached_path)
                                                        # logger.debug(f"已删除缓存文件: {cached_path}")
                                                    except Exception as e:
                                                        logger.debug(f"无法删除缓存文件 {cached_path}: {e}")
                                    except Exception as e:
                                        logger.debug(f"清理__pycache__目录时出错: {e}")

                                    module = importlib.import_module(module_name)
                                    for class_name, obj in inspect.getmembers(module):
                                        if (inspect.isclass(obj) and
                                                issubclass(obj, PluginBase) and
                                                obj is not PluginBase):
                                            try:
                                                temp_instance = obj()
                                                instance_name = temp_instance.name
                                                if instance_name in self.unloaded_plugins:
                                                    logger.debug(
                                                        f"在文件夹 {folder} 中找到已卸载的插件: {instance_name}")
                                                    plugin_name = instance_name  # 使用真实插件名
                                                    is_unloaded = True
                                                    found_in_unloaded = True
                                                    break
                                            except Exception as e:
                                                logger.debug(f"实例化插件类 {class_name} 时出错: {e}")
                                except Exception as e:
                                    logger.debug(f"导入模块 Plugins.{folder}.main 时出错: {e}")
                            if found_in_unloaded:
                                break

            if not is_unloaded:
                logger.warning(f"插件 {plugin_name} 不在卸载列表中")
                # 尝试常规重载
                return self.reload_plugin(plugin_name)

            # 从卸载列表中移除
            if plugin_name in self.unloaded_plugins:
                self.unloaded_plugins.remove(plugin_name)
                logger.debug(f"已将插件 {plugin_name} 从卸载列表中移除")

            # 尝试加载插件
            logger.debug(f"开始加载已卸载的插件 {plugin_name}")
            result = self.load_plugins(target_plugin=plugin_name)

            # 检查是否成功加载
            if plugin_name in self.plugins:
                logger.success(f"已成功重新加载已卸载的插件: {plugin_name}")
                return True
            else:
                # 检查是否使用不同名称加载了插件
                if folder_name:
                    for loaded_name, loaded_plugin in self.plugins.items():
                        if hasattr(loaded_plugin, '_plugin_folder') and loaded_plugin._plugin_folder == folder_name:
                            logger.success(f"已成功加载插件: {loaded_name} (从文件夹 {folder_name})")
                            return True

                # 检查所有文件夹
                for folder in os.listdir("Plugins"):
                    folder_path = os.path.join("Plugins", folder)
                    if not os.path.isdir(folder_path) or folder.startswith('_') or folder.startswith(
                            '.') or folder == "core":
                        continue

                    if folder == plugin_name:
                        # 查找该文件夹中加载的插件
                        for loaded_name, loaded_plugin in self.plugins.items():
                            if hasattr(loaded_plugin, '_plugin_folder') and loaded_plugin._plugin_folder == folder:
                                logger.success(f"已成功加载插件: {loaded_name} (从文件夹 {folder})")
                                return True

                logger.error(f"未能重新加载已卸载的插件: {plugin_name}")
                # 重新加入卸载列表
                if plugin_name not in self.unloaded_plugins:
                    self.unloaded_plugins.append(plugin_name)
                return False

    def get_disabled_plugins(self):
        """
        获取禁用的插件列表，使用真实的name属性
        """
        # 过滤掉包含.的类名格式插件名和重复项，并尝试获取真实name
        filtered_plugins = []
        real_names = set()  # 用于避免重复

        for plugin_name in self.disabled_plugins:
            # 跳过类名格式
            if '.' in plugin_name:
                continue

            # 获取真实name
            real_name = self.get_plugin_real_name(plugin_name)

            # 避免重复
            if real_name not in real_names:
                real_names.add(real_name)
                filtered_plugins.append(real_name)

        return filtered_plugins

    def set_event_propagation(self, propagate: bool) -> None:
        """设置事件传播行为

        Args:
            propagate: 是否允许事件传播到多个处理器
        """
        with self.lock:
            self.propagate_events = propagate
            logger.info(f"事件传播已{'启用' if propagate else '禁用'}")

    def get_event_propagation(self) -> bool:
        """获取当前的事件传播状态

        Returns:
            bool: 当前是否允许事件传播
        """
        with self.lock:
            return self.propagate_events

    def get_handlers_priority_info(self) -> List[Dict]:
        """获取所有事件处理器及其优先级信息

        Returns:
            List[Dict]: 包含事件类型、处理器、插件名称和优先级的列表
        """
        with self.lock:
            result = []

            for event_type, handlers_info in self.event_handlers.items():
                # 按优先级排序
                sorted_handlers = sorted(handlers_info, key=lambda h: h['priority'], reverse=True)

                # 生成每个事件类型的处理器信息
                event_handlers = []
                for handler_info in sorted_handlers:
                    event_handlers.append({
                        'plugin_name': handler_info['plugin_name'],
                        'handler_name': handler_info['handler_name'],
                        'priority': handler_info['priority']
                    })

                if event_handlers:
                    result.append({
                        'event_type': event_type,
                        'handlers': event_handlers
                    })

            return result

    def get_plugin_real_name(self, folder_name: str) -> str:
        """获取插件的真实name属性（而非文件夹名或类名）

        Args:
            folder_name: 插件文件夹名称

        Returns:
            str: 插件的真实name属性，如果无法获取则返回原始folder_name
        """
        # 如果是空值，直接返回
        if not folder_name:
            return folder_name

        # 如果已经是类名格式（包含点号），直接返回
        if '.' in folder_name:
            return folder_name

        # 检查文件夹是否存在
        folder_path = os.path.join("Plugins", folder_name)
        if not os.path.isdir(folder_path):
            return folder_name

        # 检查main.py是否存在
        main_file = os.path.join(folder_path, "main.py")
        if not os.path.exists(main_file):
            return folder_name

        try:
            # 构建模块名
            module_name = f"Plugins.{folder_name}.main"

            # 尝试导入模块
            spec = importlib.util.find_spec(module_name)
            if not spec:
                return folder_name

            try:
                # 如果模块已经加载，获取已加载的模块
                if module_name in sys.modules:
                    module = sys.modules[module_name]
                else:
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                # 查找插件类
                for class_name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and
                            issubclass(obj, PluginBase) and
                            obj is not PluginBase):
                        try:
                            # 实例化插件并获取name属性
                            temp_instance = obj()
                            if hasattr(temp_instance, 'name'):
                                return temp_instance.name
                        except Exception as e:
                            logger.debug(f"实例化插件 {class_name} 失败: {e}")
            except Exception as e:
                logger.debug(f"加载模块 {module_name} 失败: {e}")
        except Exception as e:
            logger.debug(f"获取插件 {folder_name} 的真实名称失败: {e}")

        # 如果无法获取真实名称，返回原始文件夹名
        return folder_name
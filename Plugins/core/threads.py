from typing import Callable
from loguru import logger
import threading
import schedule
import time


class EventHandler(threading.Thread):
    """事件处理线程"""

    def __init__(self, handler_func: Callable, message: dict):
        super().__init__()
        self.handler_func = handler_func
        self.message = message
        self.daemon = True  # 设置为守护线程，主线程结束时自动结束

    def run(self):
        """运行处理函数"""
        try:
            self.handler_func(self.message)
        except Exception as e:
            logger.error(f"处理事件时出现错误, 错误信息: {e}")
            import traceback
            logger.error(traceback.format_exc())


class ScheduleThread(threading.Thread):
    """定时任务线程，用于执行 schedule 定时任务"""

    def __init__(self):
        super().__init__()
        self.daemon = True  # 守护线程
        self.running = True
        self._paused = False
        self._condition = threading.Condition()

    def run(self):
        """运行定时任务调度器"""
        while self.running:
            with self._condition:
                while self._paused:
                    self._condition.wait()  # 等待恢复信号

                # 运行所有待执行的定时任务
                schedule.run_pending()

            # 每秒检查一次
            time.sleep(1)

    def pause(self):
        """暂停定时任务执行"""
        with self._condition:
            self._paused = True
            logger.debug("定时任务调度器已暂停")

    def resume(self):
        """恢复定时任务执行"""
        with self._condition:
            self._paused = False
            self._condition.notify_all()  # 通知所有等待的线程
            logger.debug("定时任务调度器已恢复")

    def stop(self):
        """停止定时任务线程"""
        self.running = False
        self.resume()  # 确保不会卡在暂停状态
        logger.debug("定时任务调度器已停止")


class EventHandlerWithControl(threading.Thread):
    """支持传播控制的事件处理线程"""

    def __init__(self, handler_func: Callable, message: dict):
        super().__init__()
        self.handler_func = handler_func
        self.message = message
        self.daemon = True  # 设置为守护线程，主线程结束时自动结束
        self.result = None  # 存储处理函数的返回值
        self.is_completed = False  # 标记是否已完成执行
        self.exception = None  # 存储执行过程中的异常

    def run(self):
        """运行处理函数并记录返回值"""
        try:
            self.result = self.handler_func(self.message)
            self.is_completed = True
        except Exception as e:
            self.exception = e
            self.is_completed = True
            logger.error(f"处理事件时出现错误, 错误信息: {e}")
            import traceback
            logger.error(traceback.format_exc())

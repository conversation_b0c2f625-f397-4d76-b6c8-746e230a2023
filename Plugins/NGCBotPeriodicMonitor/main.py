from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
from datetime import datetime
import requests
import os


class NGCBotPeriodicMonitor(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "定时微信&StarBot状态监测"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "定时微信&StarBot状态监测"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.spugConfig = self.configData.get('SpugConfig')
        self.sendSmsStatus = 1

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_scheduled_task(interval=600)
    def periodicMonitor(self, ):
        """
        定时微信监测, 120s一次
        :return:
        """
        try:
            time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            botDicts = self.bot.getRobotList(1)
            if not botDicts:
                if self.sendSmsStatus:
                    self.sendSms(name='StarBot', time=time, error='程序异常退出', errorInfo='StarBot客户端异常退出')
                    self.sendSmsStatus = 0
                return
            botData = botDicts.get('data')
            if not botData:
                if self.sendSmsStatus:
                    self.sendSms(name='微信', time=time, error='微信退出', errorInfo='微信异常退出')
                    self.sendSmsStatus = 0
                return
            else:
                self.sendSmsStatus = 1
        except Exception as e:
            logger.error(f'定时微信监测出现错误, 错误信息: {e}')

    def sendSms(self, name, time, error, errorInfo):
        """
        发送通知短信
        :param name:
        :param time:
        :param error:
        :param errorInfo:
        :return:
        """
        try:
            supgApi = self.spugConfig.get('spugApi')
            data = {
                'name': name,
                'time': time,
                'error': error,
                'errorInfo': errorInfo
            }
            requests.post(supgApi, json=data)
        except Exception as e:
            raise e

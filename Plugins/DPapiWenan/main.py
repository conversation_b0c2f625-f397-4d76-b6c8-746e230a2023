from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import requests
import random
import time
import os


class DPapiWenan(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "经典文案插件"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "口令发送各种文案"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.dpApi = self.configData.get('dpApi')
        self.dpKey = self.configData.get('dpKey')
        self.wenanWords = self.configData.get('wenanWords')
    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_text
    def handleGroupMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        content = msgData.get('message')
        if content not in self.wenanWords:
            return
        wenanDicts = self.getDPWenan(content)
        if not wenanDicts:
            self.bot.sendText(robotId=robotId, receive=roomId, message=f'{self.name} 获取文案出错, 请稍后重试~',
                              aters=f'{sender}')
            return
        else:
            self.bot.sendText(robotId=robotId, receive=roomId, message=wenanDicts.get('content'),
                              aters=f'{sender}')

    def getDPWenan(self, content: str):
        try:
            params = {
                'AppSecret': self.dpKey,
                'type': content
            }
            resp = requests.get(self.dpApi, params=params)            
            jsonData = resp.json()
            wenanData = jsonData.get('result')
            wenanDicts = {
                'content': wenanData.get('content')
            }
            return wenanDicts
        except Exception as e:
            logger.warning(f'{self.name} 获取文案出现错误, 错误信息: {e}')
            return ''

import Config.ConfigServer as Cs
from Plugins.FishGame.fishing.fishing import FishingSystem
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from Plugins import *
from loguru import logger
from threading import Thread
import os


class FishGame(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "钓鱼游戏插件"
        self.author = "凌封"
        self.version = '1.0.0'
        self.description = "MUD钓鱼游戏"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.RoleConfig= Cs.returnConfigData().get('RoleConfig')
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.fishConfig = self.tools.returnConfigDataWithYaml(os.path.dirname(__file__))
        # 钓鱼系统
        self.fishing_system = FishingSystem(self.bot)

    # @on_group_at
    def handleRoomMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        wxName = msgData.get('finalFromNickName')
        atWxIdList = msgData.get('atWxIdList')
        content = msgData.get('message').strip()
        logger.warning(f"[-]钓鱼系统处理消息: {content}")
        #不是钓鱼群的，直接return掉
        if roomId not in self.RoleConfig['fish']['roomIds']:
            return

        # 3. 钓鱼系统关键词判断 (优先处理)
        fishing_keywords = [
            # 基础功能
            "钓鱼", "抛竿", "鱼塘", "鱼池", "背包",
            "一键卖鱼", "一键卖高价", "特价卖鱼", "找回旺财",
            "升级鱼竿", "升级旺财", "钓鱼游戏", "限时高价", "鱼市",
            "确认雇佣", "0", "1", "2", "3", "4", "40", "5", "6", "60", "61",

            # 成就系统
            "成就", "我的成就",

            # 排行榜系统
            "排行榜", "财富榜", "等级榜", "稀有榜", "成功率榜", "钓鱼榜", "钓鱼统计",

            # 装饰系统
            "装饰商店", "我的鱼塘", "参观",
            # 装饰购买指令
            "买锦鲤池", "买珊瑚礁", "买防盗门", "买幸运喷泉", "买黄金地板",
            # 装饰升级指令
            "升锦鲤池", "升珊瑚礁", "升防盗门", "升幸运喷泉", "升黄金地板",

            # 任务系统
            "任务", "每日任务", "刷新任务",

            # 比赛系统
            "钓鱼比赛", "参加比赛", "比赛排名",

            # 帮助系统
            "钓鱼帮助", "玩法说明", "帮助"
        ]

        # 处理钓鱼系统命令
        if (content in fishing_keywords or
                content.startswith("卖鱼#") or
                content.startswith("偷鱼") or
                content.startswith("参观") or
                content.startswith("送金币")):  # 添加送金币命令判断
            try:
                # 处理送金币命令
                if content.startswith("送金币"):
                    if atWxIdList:
                        try:
                            # 解析金币数量
                            amount = int(content.split()[-1])  # 获取最后一个数字作为金额
                            Thread(target=self.fishing_system.admin_give_coins,args=(roomId, sender, atWxIdList[0], amount)).start()
                        except ValueError:
                            self.bot.sendText(robotId=robotId, receive=roomId, message=f'\n"格式错误，请使用：送金币 @玩家 金额"',aters=f'{sender}')

                    else:
                        self.bot.sendText(robotId=robotId, receive=roomId,message=f'\n"请@要送金币的玩家"', aters=f'{sender}')
                    return

                # 处理偷鱼命令
                elif content.startswith("偷鱼"):
                    if atWxIdList:
                        #message.at_users = atWxIdList
                        Thread(target=self.fishing_system.steal_fish, args=(roomId, sender, atWxIdList[0])).start()
                    else:
                        self.bot.sendText(robotId=robotId, receive=roomId, message=f'\n"请@要偷鱼的玩家"',aters=f'{sender}')
                    return

                # 处理参观命令，与偷鱼保持一致的处理方式
                elif content.startswith("参观"):
                    if atWxIdList:
                        #message.at_users = atWxIdList
                        Thread(target=self.fishing_system.visit_pond,args=(roomId, sender, atWxIdList[0])).start()
                    else:
                        self.bot.sendText(robotId=robotId, receive=roomId, message=f'\n"请@要参观的玩家"', aters=f'{sender}')
                    return

                # 处理其他钓鱼命令
                Thread(target=self.fishing_system.handle_message, args=(msgData,)).start()
                return

            except Exception as e:
                logger.error(f"[-]钓鱼系统处理错误: {str(e)}")
                error_msg = "钓鱼系统处理出错，请稍后再试~"
                self.bot.sendText(robotId=robotId, receive=roomId, message=error_msg, aters=f'{sender}')
                return


    # @on_group_text
    def handleRoomTextMsg(self, message: dict):
        """ 群聊文本事件处理 """

        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        wxName = msgData.get('finalFromNickName')
        atWxIdList = msgData.get('atWxIdList')
        content = msgData.get('message').strip()
        logger.warning(f"[-]钓鱼系统处理消息: {content}")
        #不是钓鱼群的，直接return掉
        if roomId not in self.RoleConfig['fish']['roomIds']:
            return

        # 3. 钓鱼系统关键词判断 (优先处理)
        fishing_keywords = [
            # 基础功能
            "钓鱼", "抛竿", "鱼塘", "鱼池", "背包",
            "一键卖鱼", "一键卖高价", "特价卖鱼", "找回旺财",
            "升级鱼竿", "升级旺财", "钓鱼游戏", "限时高价", "鱼市",
            "确认雇佣", "0", "1", "2", "3", "4", "40", "5", "6", "60", "61",

            # 成就系统
            "成就", "我的成就",

            # 排行榜系统
            "排行榜", "财富榜", "等级榜", "稀有榜", "成功率榜", "钓鱼榜", "钓鱼统计",

            # 装饰系统
            "装饰商店", "我的鱼塘", "参观",
            # 装饰购买指令
            "买锦鲤池", "买珊瑚礁", "买防盗门", "买幸运喷泉", "买黄金地板",
            # 装饰升级指令
            "升锦鲤池", "升珊瑚礁", "升防盗门", "升幸运喷泉", "升黄金地板",

            # 任务系统
            "任务", "每日任务", "刷新任务",

            # 比赛系统
            "钓鱼比赛", "参加比赛", "比赛排名",

            # 帮助系统
            "钓鱼帮助", "玩法说明", "帮助"
        ]

        # 处理钓鱼系统命令
        if (content in fishing_keywords or
                content.startswith("卖鱼#") or
                content.startswith("偷鱼") or
                content.startswith("参观") or
                content.startswith("送金币")):  # 添加送金币命令判断
            try:
                # 处理送金币命令
                if content.startswith("送金币"):
                    if atWxIdList:
                        try:
                            # 解析金币数量
                            amount = int(content.split()[-1])  # 获取最后一个数字作为金额
                            Thread(target=self.fishing_system.admin_give_coins,args=(roomId, sender, atWxIdList[0], amount)).start()
                        except ValueError:
                            self.bot.sendText(robotId=robotId, receive=roomId, message=f'\n"格式错误，请使用：送金币 @玩家 金额"',aters=f'{sender}')

                    else:
                        self.bot.sendText(robotId=robotId, receive=roomId,message=f'\n"请@要送金币的玩家"', aters=f'{sender}')
                    return

                # 处理偷鱼命令
                elif content.startswith("偷鱼"):
                    if atWxIdList:
                        #message.at_users = atWxIdList
                        Thread(target=self.fishing_system.steal_fish, args=(roomId, sender, atWxIdList[0])).start()
                    else:
                        self.bot.sendText(robotId=robotId, receive=roomId, message=f'\n"请@要偷鱼的玩家"',aters=f'{sender}')
                    return

                # 处理参观命令，与偷鱼保持一致的处理方式
                elif content.startswith("参观"):
                    if atWxIdList:
                        #message.at_users = atWxIdList
                        Thread(target=self.fishing_system.visit_pond,args=(roomId, sender, atWxIdList[0])).start()
                    else:
                        self.bot.sendText(robotId=robotId, receive=roomId, message=f'\n"请@要参观的玩家"', aters=f'{sender}')
                    return

                # 处理其他钓鱼命令
                Thread(target=self.fishing_system.handle_message, args=(msgData,)).start()
                return

            except Exception as e:
                logger.error(f"[-]钓鱼系统处理错误: {str(e)}")
                error_msg = "钓鱼系统处理出错，请稍后再试~"
                self.bot.sendText(robotId=robotId, receive=roomId, message=error_msg, aters=f'{sender}')
                return

    #@on_group_red_packet
    def handleRoomRedPacketMsg(self, message: dict):
        """ 群聊红包事件处理 """

    @on_group_at_robot
    def handleRoomAtRobot(self, message: dict):
        """ 群聊@机器人事件处理 """

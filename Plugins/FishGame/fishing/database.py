import sqlite3
import time
import traceback
import logging
from datetime import datetime, timedelta
import random
import json
import threading
import os
from threading import Thread

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class BatchWriter:
    """批量写入器，用于延迟写入和批量提交数据库操作"""

    def __init__(self, db, max_batch=5, flush_interval=5):
        self.db = db
        self.max_batch = max_batch
        self.flush_interval = flush_interval
        self.pending_updates = []
        self.last_flush = time.time()
        self.lock = threading.Lock()

    def add_update(self, sql, params):
        """添加一个更新操作到队列"""
        with self.lock:
            self.pending_updates.append((sql, params))

            # 检查是否需要刷新
            if len(self.pending_updates) >= self.max_batch or time.time() - self.last_flush > self.flush_interval:
                self.flush()

    def flush(self):
        """执行所有待处理的更新操作"""
        with self.lock:
            if not self.pending_updates:
                return

            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("BEGIN TRANSACTION")

                try:
                    for sql, params in self.pending_updates:
                        cursor.execute(sql, params)
                    conn.commit()
                    self.pending_updates = []
                    self.last_flush = time.time()
                    logging.info(f"批量写入成功，执行了 {len(self.pending_updates)} 个操作")
                except Exception as e:
                    conn.rollback()
                    logging.error(f"批量写入错误: {e}")
                    logging.error(traceback.format_exc())


class DatabaseConnectionPool:
    """数据库连接池，用于减少频繁连接和断开的开销"""

    def __init__(self, db_path, max_connections=5):
        self.db_path = db_path
        self.max_connections = max_connections
        self.connections = {}  # 改为字典，以线程ID为键
        self.in_use = {}  # 改为字典，以线程ID为键
        self.lock = threading.Lock()

    def get_connection(self):
        """获取一个数据库连接，确保每个线程使用自己的连接"""
        thread_id = threading.get_ident()

        with self.lock:
            # 初始化当前线程的连接列表和使用集合
            if thread_id not in self.connections:
                self.connections[thread_id] = []
            if thread_id not in self.in_use:
                self.in_use[thread_id] = set()

            # 尝试获取当前线程的可用连接
            for conn in self.connections[thread_id]:
                if conn not in self.in_use[thread_id]:
                    self.in_use[thread_id].add(conn)
                    return conn

            # 如果没有可用连接且未达到最大连接数，创建新连接
            if len(self.connections[thread_id]) < self.max_connections:
                conn = sqlite3.connect(self.db_path)
                self.connections[thread_id].append(conn)
                self.in_use[thread_id].add(conn)
                return conn

            # 等待直到有连接可用
            while True:
                for conn in self.connections[thread_id]:
                    if conn not in self.in_use[thread_id]:
                        self.in_use[thread_id].add(conn)
                        return conn
                time.sleep(0.1)

    def release_connection(self, conn):
        """释放一个数据库连接"""
        thread_id = threading.get_ident()

        with self.lock:
            if thread_id in self.in_use and conn in self.in_use[thread_id]:
                self.in_use[thread_id].remove(conn)

    def close_all(self):
        """关闭所有连接"""
        with self.lock:
            for thread_id in self.connections:
                for conn in self.connections[thread_id]:
                    try:
                        conn.close()
                    except Exception:
                        pass
            self.connections.clear()
            self.in_use.clear()


class FishingDB:
    def __init__(self):
        try:
            self.db_path = "fishing.db"
            self._init_database()
            # 添加操作锁字典
            self.operation_locks = {}
            # 添加冷却时间锁字典
            self.cooldown_locks = {}
            # 初始化批量写入器
            self.batch_writer = BatchWriter(self, max_batch=30, flush_interval=5)
            # 初始化连接池
            self.connection_pool = DatabaseConnectionPool(self.db_path, max_connections=10)
        except Exception as e:
            self._handle_exception(e, "数据库初始化错误")

        # 批量写入器
        self.batch_writer = BatchWriter(self)

        # 初始化称号
        self.titles = [
            # 称号名称, 要求等级, 要求钓鱼次数, buff类型, buff值
            ("新手渔夫", 1, 0, None, 0),
            ("业余钓手", 5, 30, "exp_rate", 0.05),
            ("熟练钓手", 10, 100, "exp_rate", 0.10),
            ("专业渔夫", 20, 300, "success_rate", 0.05),
            ("垂钓大师", 30, 600, "success_rate", 0.10),
            ("钓鱼王者", 40, 1000, "income_rate", 0.05),
            ("传奇渔夫", 50, 2000, "income_rate", 0.10),
            ("渔神", 60, 5000, "rare_rate", 0.05),
            ("水之守护者", 70, 10000, "all_rate", 0.05)
        ]

    def _safe_parse_datetime(self, dt_string):
        """安全解析日期时间字符串，处理不同格式"""
        if not dt_string:
            return None

        try:
            # 尝试使用带微秒的格式解析
            return datetime.strptime(dt_string, "%Y-%m-%d %H:%M:%S.%f")
        except ValueError:
            try:
                # 尝试使用不带微秒的格式解析
                return datetime.strptime(dt_string, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                try:
                    # 如果还是失败，尝试去掉微秒部分再解析
                    return datetime.strptime(dt_string.split('.')[0], "%Y-%m-%d %H:%M:%S")
                except Exception:
                    # 如果还是失败，返回当前时间（作为兜底）
                    return datetime.now()

    def _init_database(self):
        """初始化数据库表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 添加 player_buffs 表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS player_buffs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        buff_type TEXT NOT NULL,
                        buff_value REAL NOT NULL,
                        end_time DATETIME NOT NULL,
                        UNIQUE(room_id, user_id, buff_type)
                    )
                ''')

                # 鱼池表(当前可钓的鱼)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS fish_pool (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        price INTEGER NOT NULL,
                        count INTEGER DEFAULT 1,
                        end_time DATETIME NOT NULL
                    )
                ''')

                # 限时高价表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS special_price (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        fish_name TEXT NOT NULL,
                        special_price INTEGER NOT NULL,
                        end_time DATETIME NOT NULL
                    )
                ''')

                # 玩家基础信息表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS fishing_players (
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        level INTEGER DEFAULT 1,
                        exp INTEGER DEFAULT 0,
                        coins INTEGER DEFAULT 1000,
                        rod_level INTEGER DEFAULT 0,
                        dog_level INTEGER DEFAULT 0,
                        dog_status INTEGER DEFAULT 0,
                        title TEXT DEFAULT '钓鱼新手',
                        success_count INTEGER DEFAULT 0,
                        fail_count INTEGER DEFAULT 0,
                        total_income INTEGER DEFAULT 0,
                        rare_fish_count INTEGER DEFAULT 0,
                        legendary_fish_count INTEGER DEFAULT 0,
                        last_fishing DATETIME,
                        last_steal DATETIME,
                        last_search DATETIME,
                        last_daily_reset DATETIME,
                        PRIMARY KEY (room_id, user_id)
                    )
                ''')

                # 鱼塘表(玩家背包)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS fish_pond (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        name TEXT NOT NULL,
                        fish_count INTEGER DEFAULT 1,
                        fish_price INTEGER NOT NULL,
                        catch_time DATETIME,
                        UNIQUE(room_id, user_id, name)
                    )
                ''')

                # 成就表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS achievements (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        achievement_name TEXT NOT NULL,
                        achieved_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(room_id, user_id, achievement_name)
                    )
                ''')

                # 道具表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        item_name TEXT NOT NULL,
                        item_count INTEGER DEFAULT 1,
                        expire_time DATETIME,
                        UNIQUE(room_id, user_id, item_name)
                    )
                ''')

                # 任务表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS tasks (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        task_type TEXT NOT NULL,
                        task_name TEXT NOT NULL,
                        progress INTEGER DEFAULT 0,
                        target INTEGER NOT NULL,
                        reward_type TEXT NOT NULL,
                        reward_amount INTEGER NOT NULL,
                        reward_claimed INTEGER DEFAULT 0,
                        expire_time DATETIME,
                        description TEXT,
                        UNIQUE(room_id, user_id, task_type, task_name)
                    )
                ''')

                # 检查是否需要添加 description 列
                cursor.execute("PRAGMA table_info(tasks)")
                columns = [column[1] for column in cursor.fetchall()]
                if 'description' not in columns:
                    cursor.execute('ALTER TABLE tasks ADD COLUMN description TEXT')

                # 检查是否需要添加 reward_claimed 列
                if 'reward_claimed' not in columns:
                    cursor.execute('ALTER TABLE tasks ADD COLUMN reward_claimed INTEGER DEFAULT 0')

                # 装饰表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS decorations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        deco_name TEXT NOT NULL,
                        deco_level INTEGER DEFAULT 1,
                        buff_type TEXT,
                        buff_value REAL,
                        UNIQUE(room_id, user_id, deco_name)
                    )
                ''')

                # 比赛表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS competitions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        room_id TEXT NOT NULL,
                        comp_type TEXT NOT NULL,
                        start_time DATETIME NOT NULL,
                        end_time DATETIME NOT NULL,
                        status TEXT DEFAULT 'ongoing',
                        winner_id TEXT,
                        reward_type TEXT,
                        reward_amount INTEGER
                    )
                ''')

                # 比赛玩家表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS competition_players (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        competition_id INTEGER NOT NULL,
                        score INTEGER DEFAULT 0,
                        join_time DATETIME NOT NULL,
                        UNIQUE(room_id, user_id, competition_id),
                        FOREIGN KEY(competition_id) REFERENCES competitions(id)
                    )
                ''')

                # 冷却时间表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS action_cooldowns (
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        action_type TEXT NOT NULL,
                        end_time DATETIME NOT NULL,
                        UNIQUE(room_id, user_id, action_type)
                    )
                ''')

                # 添加操作日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS operation_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        operation_type TEXT NOT NULL,
                        operation_time DATETIME NOT NULL,
                        details TEXT
                    )
                ''')

                # 为operation_logs表创建索引
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_operation_time 
                    ON operation_logs(operation_time)
                ''')

                # 定期清理过期日志的触发器
                cursor.execute('''
                    CREATE TRIGGER IF NOT EXISTS cleanup_old_logs
                    AFTER INSERT ON operation_logs
                    BEGIN
                        DELETE FROM operation_logs 
                        WHERE operation_time < datetime('now', '-1 hour');
                    END;
                ''')

                # 管理员表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS fishing_admins (
                        user_id TEXT PRIMARY KEY,
                        added_time DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 每日任务表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_tasks (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        room_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        task_name TEXT NOT NULL,
                        task_type TEXT NOT NULL,
                        progress INTEGER DEFAULT 0,
                        target INTEGER NOT NULL,
                        reward_type TEXT NOT NULL,
                        reward_amount INTEGER NOT NULL,
                        claimed INTEGER DEFAULT 0,
                        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        expire_time DATETIME NOT NULL,
                        description TEXT,
                        UNIQUE(room_id, user_id, task_type)
                    )
                ''')

                # 检查是否需要添加 description 列到 daily_tasks
                cursor.execute("PRAGMA table_info(daily_tasks)")
                columns = [column[1] for column in cursor.fetchall()]
                if 'description' not in columns:
                    cursor.execute('ALTER TABLE daily_tasks ADD COLUMN description TEXT')

                conn.commit()
                logging.info("数据库表初始化完成")

        except Exception as e:
            self._handle_exception(e, "初始化数据库错误")

    def _handle_exception(self, e, message):
        logging.error(f"{message}: {str(e)}")
        traceback.print_exc()

    def create_player(self, room_id, user_id):
        """创建新玩家"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO fishing_players 
                    (room_id, user_id, level, exp, coins, rod_level, dog_level, dog_status,
                     title, success_count, fail_count, total_income, rare_fish_count,
                     legendary_fish_count, last_fishing, last_steal, last_search, last_daily_reset)
                    VALUES (?, ?, 1, 0, 1000, 0, 0, 0, '钓鱼新手', 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL)
                ''', (room_id, user_id))
                conn.commit()
                return True
        except Exception as e:
            self._handle_exception(e, "创建玩家错误")
            return False

    def get_player_info(self, room_id, user_id):
        """获取玩家信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT level, exp, coins, rod_level, 
                           dog_level, dog_status, title,
                           success_count, fail_count,
                           total_income, rare_fish_count,
                           legendary_fish_count,
                           last_fishing, last_steal, last_search,
                           last_daily_reset
                    FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))

                result = cursor.fetchone()
                if not result:
                    # 创建新玩家
                    cursor.execute('''
                        INSERT INTO fishing_players
                        (room_id, user_id, level, exp, coins, rod_level, dog_level, dog_status,
                         title, success_count, fail_count, total_income, rare_fish_count,
                         legendary_fish_count, last_fishing, last_steal, last_search, last_daily_reset)
                        VALUES (?, ?, 1, 0, 1000, 0, 0, 0, '钓鱼新手', 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL)
                    ''', (room_id, user_id))
                    conn.commit()

                    # 返回默认值
                    return {
                        "level": 1,
                        "exp": 0,
                        "coins": 1000,
                        "rod_level": 0,
                        "dog_level": 0,
                        "dog_status": 0,
                        "title": "钓鱼新手",
                        "success_count": 0,
                        "fail_count": 0,
                        "total_income": 0,
                        "rare_fish_count": 0,
                        "legendary_fish_count": 0,
                        "last_fishing": None,
                        "last_steal": None,
                        "last_search": None,
                        "last_daily_reset": None
                    }

                return {
                    "level": result[0],
                    "exp": result[1],
                    "coins": result[2],
                    "rod_level": result[3],
                    "dog_level": result[4],
                    "dog_status": result[5],
                    "title": result[6],
                    "success_count": result[7],
                    "fail_count": result[8],
                    "total_income": result[9],
                    "rare_fish_count": result[10],
                    "legendary_fish_count": result[11],
                    "last_fishing": result[12],
                    "last_steal": result[13],
                    "last_search": result[14],
                    "last_daily_reset": result[15]
                }

        except Exception as e:
            self._handle_exception(e, "获取玩家信息错误")
            return None

    def check_and_process_level_up(self, room_id, user_id, conn=None):
        """检查并处理升级"""
        should_close_conn = False
        try:
            if conn is None:
                conn = sqlite3.connect(self.db_path)
                should_close_conn = True
                conn.execute("BEGIN")

            try:
                cursor = conn.cursor()

                # 获取当前等级和经验
                cursor.execute('''
                    SELECT level, exp
                    FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))

                result = cursor.fetchone()
                if not result:
                    if should_close_conn:
                        conn.rollback()
                        conn.close()
                    return {"level_up": False}

                current_level, current_exp = result
                initial_level = current_level
                reward_coins = 0

                # 计算升级所需经验（新公式）
                def get_required_exp(level):
                    if level == 1:
                        return 500
                    elif level == 2:
                        return 800
                    elif level == 3:
                        return 1200
                    else:
                        return 1200 + (level - 3) * 400

                # 如果经验足够，执行升级
                while True:
                    required_exp = get_required_exp(current_level)
                    if current_exp < required_exp:
                        break

                    # 更新等级和经验
                    level_reward = current_level * 1000
                    reward_coins += level_reward
                    cursor.execute('''
                        UPDATE fishing_players
                        SET level = level + 1,
                            exp = exp - ?,
                            coins = coins + ?
                        WHERE room_id = ? AND user_id = ?
                    ''', (required_exp, level_reward, room_id, user_id))

                    # 更新当前值用于下一次检查
                    current_level += 1
                    current_exp -= required_exp

                if should_close_conn:
                    conn.commit()

                # 如果升级了，返回升级信息
                if current_level > initial_level:
                    return {
                        "level_up": True,
                        "new_level": current_level,
                        "reward_coins": reward_coins
                    }
                else:
                    return {"level_up": False}

            except Exception as e:
                if should_close_conn:
                    conn.rollback()
                raise e

        except Exception as e:
            self._handle_exception(e, "检查升级错误")
            if should_close_conn and conn:
                conn.close()
            return {"level_up": False}

    def update_player_data(self, room_id, user_id, catch_result):
        """更新玩家数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("BEGIN")
                try:
                    cursor = conn.cursor()

                    # 更新玩家经验和金币
                    cursor.execute('''
                        UPDATE fishing_players
                        SET exp = exp + ?,
                            coins = coins + ?
                        WHERE room_id = ? AND user_id = ?
                    ''', (catch_result["exp"], catch_result["price"], room_id, user_id))

                    # 检查升级
                    self.check_and_process_level_up(room_id, user_id, conn)

                    # 记录钓鱼历史
                    cursor.execute('''
                        INSERT INTO fishing_history
                        (room_id, user_id, fish_type, weight, price, time, spot)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (room_id, user_id, catch_result["fish_type"],
                          catch_result["weight"], catch_result["price"],
                          datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                          catch_result["spot"]))

                    conn.commit()
                    return True

                except Exception as e:
                    conn.rollback()
                    raise e

        except Exception as e:
            self._handle_exception(e, "更新玩家数据错误")
            return False

    def upgrade_rod(self, room_id, user_id, cost):
        """升级鱼竿"""
        try:
            # 检查操作锁
            if not self.check_operation_lock(room_id, user_id, "upgrade"):
                return False

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取当前等级和金币
                cursor.execute('''
                    SELECT rod_level, coins FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))
                result = cursor.fetchone()
                if not result:
                    return False

                current_level, current_coins = result

                # 检查金币是否足够
                if current_coins < cost:
                    return False

                # 扣除金币并升级鱼竿
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins - ?,
                        rod_level = rod_level + 1
                    WHERE room_id = ? AND user_id = ?
                ''', (cost, room_id, user_id))

                conn.commit()

                # 验证更新是否成功
                cursor.execute('''
                    SELECT rod_level, coins FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))
                new_result = cursor.fetchone()

                if new_result and new_result[0] > current_level and new_result[1] == current_coins - cost:
                    logging.info(
                        f"升级鱼竿成功: 房间{room_id} 用户{user_id} 等级{current_level}->{new_result[0]} 金币{current_coins}->{new_result[1]}")
                    return True
                else:
                    logging.error(
                        f"升级鱼竿失败: 房间{room_id} 用户{user_id} 等级{current_level}->{new_result[0] if new_result else 'None'} 金币{current_coins}->{new_result[1] if new_result else 'None'}")
                    return False

        except Exception as e:
            self._handle_exception(e, "升级鱼竿错误")
            return False

    def update_fishing_time(self, room_id, user_id):
        """更新最后钓鱼时间"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE fishing_players 
                    SET last_fishing = ? 
                    WHERE room_id = ? AND user_id = ?
                ''', (datetime.now(), room_id, user_id))
        except Exception as e:
            self._handle_exception(e, "更新钓鱼时间错误")

    def get_fish_pond(self, room_id, user_id):
        """获取玩家鱼塘"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT name, fish_count, fish_price,
                            sp.special_price, sp.end_time, fp.catch_time
                    FROM fish_pond fp
                    LEFT JOIN special_price sp ON fp.name = sp.fish_name
                    WHERE fp.room_id = ? AND fp.user_id = ?
                    ORDER BY fp.fish_price DESC
                ''', (room_id, user_id))

                results = cursor.fetchall()
                if not results:
                    return []

                return [
                    {
                        "name": row[0],
                        "count": row[1],
                        "price": row[2],
                        "special_price": row[3],
                        "special_end": row[4],
                        "catch_time": row[5]
                    }
                    for row in results
                ]

        except Exception as e:
            logging.error(f"获取鱼塘错误: {e}")
            logging.error(traceback.format_exc())
            return []

    def set_special_price(self, fish_name, special_price):
        """设置限时高价"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                end_time = datetime.now() + timedelta(minutes=30)
                cursor.execute('''
                    INSERT OR REPLACE INTO special_price
                    (fish_name, special_price, end_time)
                    VALUES (?, ?, ?)
                ''', (fish_name, special_price, end_time))
        except Exception as e:
            self._handle_exception(e, "设置限时高价错误")

    def get_fish_info(self, room_id, user_id, fish_name):
        """获取指定鱼的信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT name, fish_count, fish_price
                    FROM fish_pond
                    WHERE room_id = ? AND user_id = ? AND name = ?
                ''', (room_id, user_id, fish_name))
                result = cursor.fetchone()
                if result:
                    return {
                        "name": result[0],
                        "count": result[1],
                        "price": result[2]
                    }
                else:
                    return None
        except Exception as e:
            self._handle_exception(e, "获取鱼信息错误")
            return None

    def get_special_price(self, fish_name):
        """获取特殊价格"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT special_price
                    FROM special_price
                    WHERE fish_name = ? AND end_time > ?
                ''', (fish_name, datetime.now()))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            self._handle_exception(e, "获取特殊价格错误")
            return None

    def sell_fish(self, room_id, user_id, fish_name):
        """卖出指定鱼"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取鱼的信息和特价信息
                cursor.execute('''
                    SELECT fp.fish_count, fp.fish_price, sp.special_price, fp.catch_time
                    FROM fish_pond fp
                    LEFT JOIN special_price sp ON fp.name = sp.fish_name AND sp.end_time > datetime('now')
                    WHERE fp.room_id = ? AND fp.user_id = ? AND fp.name = ?
                ''', (room_id, user_id, fish_name))

                result = cursor.fetchone()
                if not result:
                    return {"success": False, "message": "你没有这种鱼哦~"}

                fish_count, base_price, special_price, catch_time = result

                # 删除鱼
                cursor.execute('''
                    DELETE FROM fish_pond
                    WHERE room_id = ? AND user_id = ? AND name = ?
                ''', (room_id, user_id, fish_name))

                sell_price = special_price if special_price else base_price
                total_price = fish_count * sell_price

                # 更新金币
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins + ?
                    WHERE room_id = ? AND user_id = ?
                ''', (total_price, room_id, user_id))

                # 如果在比赛中,更新比赛积分
                cursor.execute('''
                    UPDATE competition_players
                    SET score = score + ?
                    WHERE room_id = ? AND user_id = ?
                    AND competition_id IN (
                        SELECT id FROM competitions 
                        WHERE room_id = ? AND status = 'ongoing'
                        AND end_time > datetime('now')
                    )
                ''', (total_price, room_id, user_id, room_id))

                conn.commit()

                return {
                    "success": True,
                    "count": fish_count,
                    "price": base_price,
                    "special_price": special_price,
                    "total": total_price,
                    "catch_time": catch_time,  # 添加捕获时间到返回结果中
                    "is_special": special_price is not None  # 添加是否是特价鱼的标记
                }

        except Exception as e:
            self._handle_exception(e, "卖鱼错误")
            return {"success": False, "message": "卖鱼失败,请稍后再试"}

    def sell_all_fish(self, room_id, user_id):
        """一键卖出所有鱼"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取所有鱼的信息和特价信息
                cursor.execute('''
                    SELECT fp.name, fp.fish_count, fp.fish_price, sp.special_price, fp.catch_time
                    FROM fish_pond fp
                    LEFT JOIN special_price sp ON fp.name = sp.fish_name AND sp.end_time > datetime('now')
                    WHERE fp.room_id = ? AND fp.user_id = ?
                ''', (room_id, user_id))

                results = cursor.fetchall()
                if not results:
                    return {"success": False, "message": "你的鱼塘是空的哦~"}

                total_price = 0
                count = 0
                fish_details = []

                # 计算总价值
                for fish in results:
                    name, fish_count, base_price, special_price, catch_time = fish
                    sell_price = special_price if special_price else base_price
                    total_price += fish_count * sell_price
                    count += fish_count

                    fish_details.append({
                        "name": name,
                        "count": fish_count,
                        "price": sell_price,
                        "catch_time": catch_time,
                        "is_special": special_price is not None
                    })

                # 更新金币
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins + ?
                    WHERE room_id = ? AND user_id = ?
                ''', (total_price, room_id, user_id))

                # 如果在比赛中,更新比赛积分
                cursor.execute('''
                    UPDATE competition_players
                    SET score = score + ?
                    WHERE room_id = ? AND user_id = ?
                    AND competition_id IN (
                        SELECT id FROM competitions 
                        WHERE room_id = ? AND status = 'ongoing'
                        AND end_time > datetime('now')
                    )
                ''', (total_price, room_id, user_id, room_id))

                # 删除所有鱼
                cursor.execute('''
                    DELETE FROM fish_pond
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))

                conn.commit()
                return {
                    "success": True,
                    "count": count,
                    "total_price": total_price,
                    "fish_details": fish_details
                }

        except Exception as e:
            self._handle_exception(e, "一键卖鱼错误")
            return {"success": False}

    def set_continuous_fishing(self, room_id, user_id, count):
        """设置连续钓鱼次数"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE fishing_players
                    SET continuous_fishing = ?
                    WHERE room_id = ? AND user_id = ?
                ''', (count, room_id, user_id))
        except Exception as e:
            self._handle_exception(e, "设置连续钓鱼错误")

    def add_reward(self, room_id, user_id, reward):
        """添加奖励"""
        try:
            # 检查操作锁
            if not self.check_operation_lock(room_id, user_id, "reward"):
                return {"success": False, "message": "操作太快了，请稍后再试~"}

            # 先检查是否存在滥用行为
            check_result = self.check_and_punish_abuse(room_id, user_id, "reward", reward["amount"])
            if check_result["punished"]:
                return {"success": False, "message": check_result["message"]}

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                if reward["type"] == "coins":
                    cursor.execute('''
                        UPDATE fishing_players
                        SET coins = coins + ?
                        WHERE room_id = ? AND user_id = ?
                    ''', (reward["amount"], room_id, user_id))
                elif reward["type"] == "exp":
                    cursor.execute('''
                        UPDATE fishing_players
                        SET exp = exp + ?
                        WHERE room_id = ? AND user_id = ?
                    ''', (reward["amount"], room_id, user_id))
                    # 检查升级
                    self.check_and_process_level_up(room_id, user_id, conn)

                conn.commit()
                return {"success": True}

        except Exception as e:
            self._handle_exception(e, "添加奖励错误")
            return {"success": False, "message": "添加奖励失败"}

    def set_temp_buff(self, room_id, user_id, buff_type, value, duration):
        """设置临时buff"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                end_time = datetime.now() + timedelta(seconds=duration)
                cursor.execute('''
                    INSERT OR REPLACE INTO player_buffs
                    (room_id, user_id, buff_type, buff_value, end_time)
                    VALUES (?, ?, ?, ?, ?)
                ''', (room_id, user_id, buff_type, value, end_time))
        except Exception as e:
            self._handle_exception(e, "设置buff错误")

    def check_title_upgrade(self, room_id, user_id):
        """检查并更新称号"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取玩家信息
                cursor.execute('''
                    SELECT level, success_count + fail_count as total_count
                    FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))
                player = cursor.fetchone()

                if not player:
                    return None

                # 获取玩家当前称号
                current_title = self.get_player_title(room_id, user_id)

                # 获取可升级的最高称号
                highest_title = None
                for title in sorted(self.titles, key=lambda x: (-x[1], -x[2])):
                    title_name, required_level, required_fish_count, _, _ = title
                    if player[0] >= required_level and player[1] >= required_fish_count:
                        highest_title = title_name
                        break

                # 如果找到了更高级的称号，进行更新
                if highest_title and highest_title != current_title:
                    cursor.execute('''
                        UPDATE fishing_players
                        SET title = ?
                        WHERE room_id = ? AND user_id = ?
                    ''', (highest_title, room_id, user_id))
                    return highest_title

                return None

        except Exception as e:
            self._handle_exception(e, "检查称号升级错误")
            return None

    def get_player_buffs(self, room_id, user_id):
        """获取玩家所有有效buff，包括临时buff和装饰buff"""
        try:
            conn = self._get_connection_from_pool()
            try:
                cursor = conn.cursor()

                # 获取临时buff
                cursor.execute('''
                    SELECT buff_type, buff_value
                    FROM player_buffs
                    WHERE room_id = ? AND user_id = ? AND end_time > ?
                ''', (room_id, user_id, datetime.now()))
                temp_buffs = cursor.fetchall()

                # 获取装饰buff
                cursor.execute('''
                    SELECT buff_type, buff_value
                    FROM decorations
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))
                deco_buffs = cursor.fetchall()

                # 合并两种buff
                all_buffs = temp_buffs + deco_buffs

                return all_buffs
            finally:
                self._release_connection(conn)

        except Exception as e:
            self._handle_exception(e, "获取玩家buff错误")
            return []

    def upgrade_dog(self, room_id, user_id, cost):
        """升级旺财"""
        try:
            # 检查操作锁
            if not self.check_operation_lock(room_id, user_id, "upgrade"):
                return False

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取当前等级和金币
                cursor.execute('''
                    SELECT dog_level, coins FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))
                result = cursor.fetchone()
                if not result:
                    return False

                current_level, current_coins = result

                # 检查金币是否足够
                if current_coins < cost:
                    return False

                # 扣除金币并升级旺财
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins - ?,
                        dog_level = dog_level + 1
                    WHERE room_id = ? AND user_id = ?
                ''', (cost, room_id, user_id))

                conn.commit()

                # 验证更新是否成功
                cursor.execute('''
                    SELECT dog_level, coins FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))
                new_result = cursor.fetchone()

                if new_result and new_result[0] > current_level and new_result[1] == current_coins - cost:
                    logging.info(
                        f"升级旺财成功: 房间{room_id} 用户{user_id} 等级{current_level}->{new_result[0]} 金币{current_coins}->{new_result[1]}")
                    return True
                else:
                    logging.error(
                        f"升级旺财失败: 房间{room_id} 用户{user_id} 等级{current_level}->{new_result[0] if new_result else 'None'} 金币{current_coins}->{new_result[1] if new_result else 'None'}")
                    return False

        except Exception as e:
            self._handle_exception(e, "升级旺财错误")
            return False

    def get_random_fish(self, room_id, user_id):
        """随机获取一条鱼"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT fish_name, fish_count, fish_price
                    FROM fish_pond
                    WHERE room_id = ? AND user_id = ?
                    ORDER BY RANDOM()
                    LIMIT 1
                ''', (room_id, user_id))
                return cursor.fetchone()
        except Exception as e:
            self._handle_exception(e, "获取随机鱼错误")
            return None

    def steal_fish(self, room_id, user_id, target_id, big_fish_line=20000, big_fish_rate=0.8):
        """偷取目标玩家的鱼 (已弃用)
        
        此方法已弃用，请使用transfer_fish方法代替。
        业务逻辑应该放在fishing.py的steal_fish方法中，而不是在数据库层。
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            target_id: 目标用户ID
            big_fish_line: 大鱼价值线，超过此价值的鱼被视为大鱼
            big_fish_rate: 偷取大鱼的成功率
            
        Returns:
            dict: 偷鱼结果
        """
        logging.warning("使用已弃用的steal_fish方法，请使用transfer_fish方法代替")

        try:
            # 使用操作锁防止并发问题
            lock_key = f"steal_fish_{room_id}_{user_id}"
            current_time = time.time()

            # 检查是否有锁
            if hasattr(self, 'cooldown_locks') and lock_key in self.cooldown_locks:
                # 如果锁的时间小于1秒，说明有其他线程正在处理，直接返回
                if current_time - self.cooldown_locks[lock_key] < 1:
                    logging.warning(f"偷鱼操作并发控制 - 房间:{room_id} 用户:{user_id}")
                    # 使用更友好、更有游戏感的提示信息
                    messages = [
                        "🚫 你小子想卡BUG么？别这么频繁操作！",
                        "⚠️ 渔场管理员：操作太快了，会吓跑鱼的！",
                        "🐟 鱼儿们：这个人好可怕，一直想偷我们！",
                        "🕹️ 系统提示：连续点击是没用的，冷静一下吧！",
                        "🔒 安全系统：检测到异常操作，请正常游戏~"
                    ]
                    return {
                        "success": False,
                        "message": random.choice(messages)
                    }

            # 设置锁
            if not hasattr(self, 'cooldown_locks'):
                self.cooldown_locks = {}
            self.cooldown_locks[lock_key] = current_time

            with sqlite3.connect(self.db_path) as conn:
                # 设置更高的隔离级别
                conn.isolation_level = 'EXCLUSIVE'
                cursor = conn.cursor()

                # 获取目标玩家的鱼塘
                cursor.execute('''
                    SELECT name, fish_count, fish_price 
                    FROM fish_pond
                    WHERE room_id = ? AND user_id = ?
                    ORDER BY RANDOM()
                    LIMIT 1
                ''', (room_id, target_id))

                fish = cursor.fetchone()
                if not fish:
                    # 释放锁
                    if hasattr(self, 'cooldown_locks'):
                        self.cooldown_locks.pop(lock_key, None)
                    return {
                        "success": False,
                        "message": "对方鱼塘是空的"
                    }

                fish_name, fish_count, fish_price = fish

                # 修改1: 每次只偷一条鱼，而不是随机1-3条
                steal_count = 1

                # 修改2: 对大鱼实施额外的概率检查
                # 如果是大鱼，进行额外的概率检查
                if fish_price >= big_fish_line:
                    if random.random() > big_fish_rate:
                        # 释放锁
                        if hasattr(self, 'cooldown_locks'):
                            self.cooldown_locks.pop(lock_key, None)
                        return {
                            "success": False,
                            "message": "这条鱼太大了，溜走了！"
                        }

                # 从目标玩家鱼塘减少
                if fish_count <= steal_count:
                    cursor.execute('''
                        DELETE FROM fish_pond
                        WHERE room_id = ? AND user_id = ? AND name = ?
                    ''', (room_id, target_id, fish_name))
                else:
                    cursor.execute('''
                        UPDATE fish_pond
                        SET fish_count = fish_count - ?
                        WHERE room_id = ? AND user_id = ? AND name = ?
                    ''', (steal_count, room_id, target_id, fish_name))

                # 添加到偷鱼者的鱼塘
                cursor.execute('''
                    INSERT INTO fish_pond (room_id, user_id, name, fish_count, fish_price, catch_time)
                    VALUES (?, ?, ?, ?, ?, ?)
                    ON CONFLICT(room_id, user_id, name) 
                    DO UPDATE SET fish_count = fish_count + excluded.fish_count
                ''', (
                room_id, user_id, fish_name, steal_count, fish_price, datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

                # 更新偷鱼时间
                cursor.execute('''
                    UPDATE fishing_players
                    SET last_steal = ?
                    WHERE room_id = ? AND user_id = ?
                ''', (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), room_id, user_id))

                conn.commit()

                # 释放锁
                if hasattr(self, 'cooldown_locks'):
                    self.cooldown_locks.pop(lock_key, None)

                return {
                    "success": True,
                    "fish_name": fish_name,
                    "count": steal_count,
                    "price": fish_price
                }

        except Exception as e:
            # 确保异常情况下也释放锁
            if hasattr(self, 'cooldown_locks'):
                self.cooldown_locks.pop(f"steal_fish_{room_id}_{user_id}", None)
            logging.error(f"偷鱼错误: {e}")
            traceback.print_exc()
            return {
                "success": False,
                "message": "偷鱼失败"
            }

    def get_steal_cooldown(self, room_id, user_id, cooldown_seconds=None):
        """获取偷鱼冷却时间
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            cooldown_seconds: 冷却时间（秒），如果不提供则使用默认值60秒
            
        Returns:
            int: 剩余冷却时间（秒）
        """
        try:
            # 使用操作锁防止并发问题
            lock_key = f"steal_cooldown_{room_id}_{user_id}"
            current_time = time.time()

            # 检查是否有锁
            if hasattr(self, 'cooldown_locks') and lock_key in self.cooldown_locks:
                # 如果锁的时间小于1秒，说明有其他线程正在处理，返回冷却中
                if current_time - self.cooldown_locks[lock_key] < 1:
                    logging.warning(f"偷鱼冷却检查并发控制 - 房间:{room_id} 用户:{user_id}")
                    # 返回3秒冷却，防止并发，同时不会让玩家等太久
                    return 3

            # 设置锁
            if not hasattr(self, 'cooldown_locks'):
                self.cooldown_locks = {}
            self.cooldown_locks[lock_key] = current_time

            with sqlite3.connect(self.db_path) as conn:
                # 设置更高的隔离级别
                conn.isolation_level = 'EXCLUSIVE'
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT last_steal
                    FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))

                result = cursor.fetchone()
                if not result or not result[0]:
                    # 释放锁
                    if hasattr(self, 'cooldown_locks'):
                        self.cooldown_locks.pop(lock_key, None)
                    return 0

                last_steal = result[0]

                # 检查是否是10秒冷却标记
                if last_steal.startswith("10s_"):
                    # 移除前缀，获取实际时间
                    last_steal = last_steal[4:]
                    # 使用10秒作为冷却时间
                    cooldown_value = 10
                else:
                    # 使用传入的冷却时间或默认值（60秒）
                    cooldown_value = cooldown_seconds if cooldown_seconds is not None else 60

                last_time = self._safe_parse_datetime(last_steal)
                elapsed = (datetime.now() - last_time).total_seconds()

                remaining_cooldown = max(0, cooldown_value - int(elapsed))

                # 释放锁
                if hasattr(self, 'cooldown_locks'):
                    self.cooldown_locks.pop(lock_key, None)

                return remaining_cooldown

        except Exception as e:
            # 确保异常情况下也释放锁
            if hasattr(self, 'cooldown_locks'):
                self.cooldown_locks.pop(f"steal_cooldown_{room_id}_{user_id}", None)
            logging.error(f"获取偷鱼冷却时间错误: {e}")
            return 0

    def sell_special_price_fish(self, room_id, user_id):
        """卖出所有特价鱼"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取所有特价鱼
                cursor.execute('''
                    SELECT f.fish_name, f.fish_count, f.fish_price, s.special_price
                    FROM fish_pond f
                    JOIN special_price s ON f.fish_name = s.fish_name
                    WHERE f.room_id = ? AND f.user_id = ? AND s.end_time > ?
                ''', (room_id, user_id, datetime.now()))

                special_fish = cursor.fetchall()
                if not special_fish:
                    return {"success": False}

                total_count = 0
                total_price = 0
                extra_price = 0

                for fish in special_fish:
                    normal_price = fish[2] * fish[1]  # 原价 * 数量
                    special_price = fish[3] * fish[1]  # 特价 * 数量
                    total_count += fish[1]
                    total_price += special_price
                    extra_price += special_price - normal_price

                    # 删除已卖出的鱼
                    cursor.execute('''
                        DELETE FROM fish_pond
                        WHERE room_id = ? AND user_id = ? AND fish_name = ?
                    ''', (room_id, user_id, fish[0]))

                # 更新金币
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins + ?
                    WHERE room_id = ? AND user_id = ?
                ''', (total_price, room_id, user_id))

                return {
                    "success": True,
                    "count": total_count,
                    "total_price": total_price,
                    "extra_price": extra_price
                }

        except Exception as e:
            self._handle_exception(e, "卖出特价鱼错误")
            return {"success": False}

    def get_last_fishing_time(self, room_id, user_id):
        """获取最后钓鱼时间"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT last_fishing
                    FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))
                result = cursor.fetchone()
                return self._safe_parse_datetime(result[0]) if result and result[0] else None
        except Exception as e:
            self._handle_exception(e, "获取最后钓鱼时间错误")
            return None

    def get_last_steal_time(self, room_id, user_id):
        """获取最后偷鱼时间"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT last_steal
                    FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))
                result = cursor.fetchone()
                return self._safe_parse_datetime(result[0]) if result and result[0] else None
        except Exception as e:
            self._handle_exception(e, "获取最后偷鱼时间错误")
            return None

    def get_last_search_time(self, room_id, user_id):
        """获取最后找回旺财时间"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT last_search
                    FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))
                result = cursor.fetchone()
                return self._safe_parse_datetime(result[0]) if result and result[0] else None
        except Exception as e:
            self._handle_exception(e, "获取最后找回旺财时间错误")
            return None

    def add_coins(self, room_id, user_id, amount):
        #self.batch_add_coins(room_id, user_id, amount)
        Thread(target=self.batch_add_coins, args=(room_id, user_id, amount)).start()

    def add_coins_atonce(self, room_id, user_id, amount):
        """添加金币"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins + ?
                    WHERE room_id = ? AND user_id = ?
                ''', (amount, room_id, user_id))
        except Exception as e:
            self._handle_exception(e, "添加金币错误，采用线程重试")
            Thread(target=self.add_coins_for_thread, args=(room_id, user_id, amount)).start()

    def add_coins_for_thread(self, room_id, user_id, amount):
        """添加金币并应用 buff"""
        try:
            time.sleep(2)
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins + ?
                    WHERE room_id = ? AND user_id = ?
                ''', (amount, room_id, user_id))
        except Exception as e:
            self._handle_exception(e, "添加金币错误")

    def add_exp(self, room_id, user_id, exp_amount):
        """添加经验并检查升级"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 更新经验
                cursor.execute('''
                    UPDATE fishing_players
                    SET exp = exp + ?
                    WHERE room_id = ? AND user_id = ?
                ''', (exp_amount, room_id, user_id))

                # 检查升级
                self.check_and_process_level_up(room_id, user_id, conn)

                conn.commit()
                return True

        except Exception as e:
            self._handle_exception(e, "添加经验错误")
            return False

    def update_last_fishing(self, room_id, user_id):
        """更新最后钓鱼时间"""
        try:
            conn = self._get_connection_from_pool()
            try:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE fishing_players
                    SET last_fishing = ?
                    WHERE room_id = ? AND user_id = ?
                ''', (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), room_id, user_id))
                conn.commit()
            finally:
                self._release_connection(conn)
        except Exception as e:
            self._handle_exception(e, "更新最后钓鱼时间错误")

    def add_fish_to_pond(self, room_id, user_id, fish_name, fish_price, count=1):
        """添加鱼到玩家鱼塘"""
        try:
            # 检查操作锁
            if not self.check_operation_lock(room_id, user_id, "add_fish"):
                return False

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 检查是否已有这种鱼
                cursor.execute('''
                    SELECT fish_count 
                    FROM fish_pond 
                    WHERE room_id = ? AND user_id = ? AND name = ?
                ''', (room_id, user_id, fish_name))

                result = cursor.fetchone()

                # 使用Python的datetime.now()获取当前时间
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                if result:
                    # 已有这种鱼，更新数量
                    cursor.execute('''
                        UPDATE fish_pond 
                        SET fish_count = fish_count + ?,
                            catch_time = ?
                        WHERE room_id = ? AND user_id = ? AND name = ?
                    ''', (count, current_time, room_id, user_id, fish_name))
                else:
                    # 没有这种鱼，新增记录
                    cursor.execute('''
                        INSERT INTO fish_pond 
                        (room_id, user_id, name, fish_count, fish_price, catch_time)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (room_id, user_id, fish_name, count, fish_price, current_time))

                conn.commit()
                return True

        except Exception as e:
            logging.error(f"添加鱼到鱼塘错误: {e}")
            logging.error(traceback.format_exc())
            return False

    def update_fish_pool(self):
        """更新鱼池"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 检查是否需要刷新
                cursor.execute('''
                    SELECT end_time FROM fish_pool 
                    ORDER BY end_time DESC LIMIT 1
                ''')
                result = cursor.fetchone()

                if result:
                    end_time = self._safe_parse_datetime(result[0])
                    if end_time > datetime.now():
                        # 如果还没到刷新时间，直接返回
                        return

                # 清空当前鱼池
                cursor.execute('DELETE FROM fish_pool')

                # 设置新的结束时间，确保是整30分钟
                now = datetime.now()
                end_time = now + timedelta(minutes=30)
                # 将秒和微秒设为0，确保准确的30分钟
                end_time = end_time.replace(second=0, microsecond=0)

                # 预定义鱼类列表
                fish_list = [
                    # 传说级
                    {"name": "远古巨鲨", "price": 50000},
                    {"name": "金龙鱼", "price": 30000},
                    {"name": "美人鱼", "price": 25000},
                    {"name": "龙鱼霸王", "price": 25000},
                    {"name": "深海魔鲸", "price": 45000},
                    {"name": "幽灵鲨", "price": 40000},

                    # 史诗级
                    {"name": "帝王蟹", "price": 15000},
                    {"name": "七彩神仙鱼", "price": 12000},
                    {"name": "赤焰龙虾", "price": 13000},
                    {"name": "黄金鲤鱼", "price": 11000},
                    {"name": "蓝鳍金枪鱼", "price": 14000},
                    {"name": "珊瑚王", "price": 13500},
                    {"name": "电鳗王", "price": 12500},
                    {"name": "深海蓝鲸", "price": 16000},

                    # 稀有级
                    {"name": "河豚宝宝", "price": 1999},
                    {"name": "小水母", "price": 1999},
                    {"name": "赤羽鱼", "price": 2500},
                    {"name": "海马王子", "price": 3000},
                    {"name": "珊瑚鱼", "price": 8000},
                    {"name": "热带鱼", "price": 1500},
                    {"name": "鲨雕", "price": 6666},
                    {"name": "黄金麦穗鱼", "price": 9000},
                    {"name": "彩虹鱼", "price": 4500},
                    {"name": "银龙鱼", "price": 5500},
                    {"name": "霓虹灯鱼", "price": 3500},
                    {"name": "蝴蝶鱼", "price": 4000},
                    {"name": "狮子鱼", "price": 7000},
                    {"name": "海龟", "price": 5000},

                    # 优质级
                    {"name": "金丝鱼", "price": 800},
                    {"name": "斑马鱼", "price": 700},
                    {"name": "天使鱼", "price": 900},
                    {"name": "小丑鱼", "price": 1000},
                    {"name": "海葵鱼", "price": 1200},
                    {"name": "蓝星鱼", "price": 1100},
                    {"name": "红尾鱼", "price": 950},
                    {"name": "月光鱼", "price": 1300},
                    {"name": "荧光鱼", "price": 1400},
                    {"name": "水晶鱼", "price": 1600},

                    # 普通级
                    {"name": "笑脸鱼", "price": 600},
                    {"name": "蓝色八爪鱼", "price": 300},
                    {"name": "龟宝宝", "price": 400},
                    {"name": "小恐龙鱼", "price": 500},
                    {"name": "深海大虾", "price": 500},
                    {"name": "红鲤鱼", "price": 200},
                    {"name": "草鱼", "price": 150},
                    {"name": "鲫鱼", "price": 100},
                    {"name": "青鱼", "price": 250},
                    {"name": "鲢鱼", "price": 180},

                    # 更多品种...
                    {"name": "金鲳鱼", "price": 450},
                    {"name": "银鲳鱼", "price": 400},
                    {"name": "黄花鱼", "price": 350},
                    {"name": "带鱼", "price": 280},
                    {"name": "鲅鱼", "price": 320},
                    {"name": "鳕鱼", "price": 600},
                    {"name": "三文鱼", "price": 800},
                    {"name": "金枪鱼", "price": 900},
                    {"name": "沙丁鱼", "price": 150},
                    {"name": "秋刀鱼", "price": 200},

                    # 特色品种
                    {"name": "发光水母", "price": 2500},
                    {"name": "海底精灵", "price": 3500},
                    {"name": "星光鱼", "price": 4500},
                    {"name": "彩晶鱼", "price": 5500},
                    {"name": "幻彩鱼", "price": 6500},
                    {"name": "水晶虾", "price": 2000},
                    {"name": "宝石蟹", "price": 3000},
                    {"name": "珍珠贝", "price": 4000},
                    {"name": "海星王", "price": 5000},
                    {"name": "深海使者", "price": 7000}
                ]

                # 随机选择15种鱼
                selected_fish = random.sample(fish_list, 15)

                # 插入新数据时使用格式化的时间
                for fish in selected_fish:
                    cursor.execute('''
                        INSERT INTO fish_pool (name, price, end_time)
                        VALUES (?, ?, ?)
                    ''', (fish["name"], fish["price"], end_time.strftime("%Y-%m-%d %H:%M:%S")))

                conn.commit()
                logging.info(f"鱼池刷新成功，结束时间: {end_time}")

        except Exception as e:
            logging.error(f"刷新鱼池错误: {e}")
            logging.error(traceback.format_exc())

    def refresh_special_prices(self):
        """刷新限时高价"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 检查是否需要刷新
                cursor.execute('''
                    SELECT end_time FROM special_price 
                    ORDER BY end_time DESC LIMIT 1
                ''')
                result = cursor.fetchone()

                if result:
                    end_time = self._safe_parse_datetime(result[0])
                    if end_time > datetime.now():
                        # 如果还没到刷新时间，直接返回
                        return

                # 清空旧的限时高价
                cursor.execute('DELETE FROM special_price')

                # 从配置文件获取刷新间隔，默认为1800秒（30分钟）
                refresh_interval = 1800

                # 设置新的结束时间
                end_time = datetime.now() + timedelta(seconds=refresh_interval)

                # 从当前鱼池中获取所有鱼
                cursor.execute('''
                    SELECT name, price
                    FROM fish_pool
                    ORDER BY RANDOM()
                    LIMIT 3
                ''')
                fish_list = cursor.fetchall()

                # 为每条鱼设置特价
                for fish_name, base_price in fish_list:
                    # 特价为基础价格的2-4倍
                    special_price = int(base_price * random.uniform(2, 4))

                    cursor.execute('''
                        INSERT INTO special_price 
                        (fish_name, special_price, end_time)
                        VALUES (?, ?, ?)
                    ''', (fish_name, special_price, end_time.strftime("%Y-%m-%d %H:%M:%S")))

                conn.commit()
                logging.info(
                    f"限时高价刷新成功，选择了{len(fish_list)}条鱼，结束时间: {end_time}，持续{refresh_interval}秒")

        except Exception as e:
            logging.error(f"刷新限时高价错误: {e}")
            logging.error(traceback.format_exc())

    def update_fishing_stats(self, room_id, user_id, success=True):
        """更新钓鱼统计"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                if success:
                    cursor.execute('''
                        UPDATE fishing_players
                        SET success_count = success_count + 1
                        WHERE room_id = ? AND user_id = ?
                    ''', (room_id, user_id))
                else:
                    cursor.execute('''
                        UPDATE fishing_players
                        SET fail_count = fail_count + 1
                        WHERE room_id = ? AND user_id = ?
                    ''', (room_id, user_id))
        except Exception as e:
            self._handle_exception(e, "更新钓鱼统计错误")

    def get_special_prices(self):
        """获取当前限时高价"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 首先检查是否有特价鱼
                cursor.execute('SELECT COUNT(*) FROM special_price')
                count = cursor.fetchone()[0]

                if count == 0:
                    logging.warning("没有找到任何特价鱼，尝试强制刷新")
                    self.refresh_special_prices()

                cursor.execute('''
                    SELECT fish_name, special_price, end_time
                    FROM special_price 
                    WHERE end_time > ?
                ''', (datetime.now().strftime("%Y-%m-%d %H:%M:%S"),))

                results = cursor.fetchall()

                if not results:
                    logging.warning("查询不到有效的特价鱼，可能是时间已过期")
                    # 再次尝试强制刷新
                    self.refresh_special_prices()

                    # 再次查询
                    cursor.execute('''
                        SELECT fish_name, special_price, end_time
                        FROM special_price
                    ''')
                    results = cursor.fetchall()

                special_prices = [
                    {
                        "name": row[0],
                        "special_price": row[1],
                        "remaining_time": int((self._safe_parse_datetime(row[2]) - datetime.now()).total_seconds())
                    }
                    for row in results
                ]

                logging.info(f"获取到{len(special_prices)}条特价鱼: {special_prices}")
                return special_prices

        except Exception as e:
            self._handle_exception(e, "获取限时高价错误")
            return []

    def hire_dog(self, room_id, user_id, cost):
        """雇佣旺财"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins - ?,
                        dog_status = 1,
                        dog_level = 1
                    WHERE room_id = ? AND user_id = ?
                    AND coins >= ?
                ''', (cost, room_id, user_id, cost))
                return cursor.rowcount > 0
        except Exception as e:
            logging.error(f"雇佣旺财错误: {e}")
            return False

    def update_dog_status(self, room_id, user_id, status):
        """更新旺财状态
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            status: 旺财状态 (0: 离开, 1: 在岗)
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 设置更高的隔离级别，确保更新可靠性
                conn.isolation_level = 'EXCLUSIVE'
                cursor = conn.cursor()

                # 先检查玩家是否存在且有旺财
                cursor.execute('''
                    SELECT dog_level
                    FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))

                result = cursor.fetchone()
                if not result or result[0] == 0:
                    # 玩家不存在或没有旺财
                    return False

                # 更新旺财状态
                cursor.execute('''
                    UPDATE fishing_players
                    SET dog_status = ?
                    WHERE room_id = ? AND user_id = ?
                ''', (status, room_id, user_id))

                conn.commit()
                return cursor.rowcount > 0

        except Exception as e:
            logging.error(f"更新旺财状态错误: {e}")
            return False

    def refresh_fish_pool(self):
        """刷新鱼池"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 清空当前鱼池
                cursor.execute('DELETE FROM fish_pool')

                # 设置新的结束时间，确保是整30分钟
                now = datetime.now()
                end_time = now + timedelta(minutes=30)
                # 将秒和微秒设为0，确保准确的30分钟
                end_time = end_time.replace(second=0, microsecond=0)

                # 预定义鱼类列表，按稀有度分类
                fish_list = {
                    "传说级": [
                        {"name": "鱼神", "price": 100000, "count": 1},
                        {"name": "远古神龙", "price": 80000, "count": 1},
                        {"name": "定海神针", "price": 70000, "count": 1},
                        {"name": "夜明珠", "price": 60000, "count": 1},
                        {"name": "远古巨鲨", "price": 50000, "count": 1},
                        {"name": "金龙鱼", "price": 30000, "count": 2},
                        {"name": "美人鱼", "price": 25000, "count": 2},
                        {"name": "龙鱼霸王", "price": 25000, "count": 2},
                        {"name": "深海魔鲸", "price": 45000, "count": 1},
                        {"name": "幽灵鲨", "price": 40000, "count": 1},
                        {"name": "海龙王", "price": 35000, "count": 1},
                        {"name": "星辰鲨", "price": 38000, "count": 1},
                        {"name": "神秘海怪", "price": 42000, "count": 1},
                        {"name": "仙灵鱼", "price": 33000, "count": 2}
                    ],
                    "史诗级": [
                        {"name": "帝王蟹", "price": 15000, "count": 5},
                        {"name": "七彩神仙鱼", "price": 12000, "count": 5},
                        {"name": "赤焰龙虾", "price": 13000, "count": 5},
                        {"name": "黄金鲤鱼", "price": 11000, "count": 5},
                        {"name": "蓝鳍金枪鱼", "price": 14000, "count": 5},
                        {"name": "珊瑚王", "price": 13500, "count": 5},
                        {"name": "电鳗王", "price": 12500, "count": 5},
                        {"name": "深海蓝鲸", "price": 16000, "count": 5},
                        {"name": "海底精灵", "price": 14500, "count": 5},
                        {"name": "幻彩鱼", "price": 15500, "count": 5},
                        {"name": "水晶虾", "price": 12800, "count": 5},
                        {"name": "宝石蟹", "price": 13800, "count": 5}
                    ],
                    "稀有级": [
                        {"name": "河豚宝宝", "price": 1999, "count": 10},
                        {"name": "小水母", "price": 1999, "count": 10},
                        {"name": "赤羽鱼", "price": 2500, "count": 10},
                        {"name": "海马王子", "price": 3000, "count": 10},
                        {"name": "珊瑚鱼", "price": 8000, "count": 8},
                        {"name": "热带鱼", "price": 1500, "count": 10},
                        {"name": "鲨雕", "price": 6666, "count": 8},
                        {"name": "黄金麦穗鱼", "price": 9000, "count": 8},
                        {"name": "彩虹鱼", "price": 4500, "count": 10},
                        {"name": "银龙鱼", "price": 5500, "count": 10},
                        {"name": "霓虹灯鱼", "price": 3500, "count": 10},
                        {"name": "蝴蝶鱼", "price": 4000, "count": 10},
                        {"name": "狮子鱼", "price": 7000, "count": 8},
                        {"name": "海龟", "price": 5000, "count": 10}
                    ],
                    "普通级": [
                        {"name": "金丝鱼", "price": 800, "count": 15},
                        {"name": "斑马鱼", "price": 700, "count": 15},
                        {"name": "天使鱼", "price": 900, "count": 15},
                        {"name": "小丑鱼", "price": 1000, "count": 15},
                        {"name": "海葵鱼", "price": 1200, "count": 15},
                        {"name": "蓝星鱼", "price": 1100, "count": 15},
                        {"name": "红尾鱼", "price": 950, "count": 15},
                        {"name": "月光鱼", "price": 1300, "count": 15},
                        {"name": "荧光鱼", "price": 1400, "count": 15},
                        {"name": "水晶鱼", "price": 1600, "count": 15},
                        {"name": "笑脸鱼", "price": 600, "count": 20},
                        {"name": "蓝色八爪鱼", "price": 300, "count": 20},
                        {"name": "龟宝宝", "price": 400, "count": 20},
                        {"name": "小恐龙鱼", "price": 500, "count": 20},
                        {"name": "深海大虾", "price": 500, "count": 20},
                        {"name": "小昌鱼", "price": 500, "count": 20},
                        {"name": "小白条", "price": 1, "count": 20}
                    ]
                }

                # 从每个稀有度随机选择鱼
                selected_fish = []
                selected_fish.extend(random.sample(fish_list["传说级"], k=2))  # 2条传说级
                selected_fish.extend(random.sample(fish_list["史诗级"], k=4))  # 4条史诗级
                selected_fish.extend(random.sample(fish_list["稀有级"], k=4))  # 4条稀有级
                selected_fish.extend(random.sample(fish_list["普通级"], k=5))  # 5条普通级

                # 将鱼类添加到鱼池
                cursor.executemany('''
                    INSERT INTO fish_pool (name, price, count, end_time)
                    VALUES (?, ?, ?, ?)
                ''', [(fish['name'], fish['price'], fish['count'], end_time.strftime("%Y-%m-%d %H:%M:%S"))
                      for fish in selected_fish])

                conn.commit()
                logging.info("鱼池刷新成功，新的鱼类组合已生成")

        except Exception as e:
            logging.error(f"刷新鱼池错误: {e}")
            logging.error(traceback.format_exc())
            raise

    def get_fish_pool(self):
        """获取当前鱼池信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT name, price, count, end_time 
                    FROM fish_pool
                    WHERE end_time > datetime('now')
                    ORDER BY price DESC
                    LIMIT 15
                ''')
                results = cursor.fetchall()

                if not results:
                    return None

                return {
                    "fish_list": [
                        {"name": row[0], "price": row[1], "count": row[2]}
                        for row in results
                    ],
                    "remaining_time": int(
                        (self._safe_parse_datetime(results[0][3]) -
                         datetime.now()).total_seconds()
                    )
                }
        except Exception as e:
            logging.error(f"获取鱼池错误: {e}")
            return None

    def get_decorations(self, room_id, user_id):
        """获取玩家的装饰列表"""
        try:
            conn = self._get_connection_from_pool()
            try:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT deco_name, deco_level, buff_type, buff_value
                    FROM decorations
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))

                results = cursor.fetchall()
                return [
                    {
                        "name": row[0],
                        "level": row[1],
                        "buff_type": row[2],
                        "buff_value": row[3]
                    }
                    for row in results
                ]
            finally:
                self._release_connection(conn)

        except Exception as e:
            self._handle_exception(e, "获取装饰列表错误")
            return []

    def buy_decoration(self, room_id, user_id, deco_name, price, buff_type, buff_value):
        """购买装饰"""
        try:
            # 检查操作锁
            if not self.check_operation_lock(room_id, user_id, "buy"):
                return False

            conn = self._get_connection_from_pool()
            try:
                cursor = conn.cursor()

                # 检查金币是否足够
                cursor.execute('''
                    SELECT coins FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))

                result = cursor.fetchone()
                if not result or result[0] < price:
                    return False

                # 转换buff类型为数据库存储格式
                buff_type_map = {
                    "经验加成": "exp_rate",
                    "稀有概率": "rare_rate",
                    "防盗能力": "anti_steal",
                    "收入加成": "income_rate",
                    "全局加成": "all_rate"
                }

                db_buff_type = buff_type_map.get(buff_type, buff_type)

                # 扣除金币并添加装饰
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins - ?
                    WHERE room_id = ? AND user_id = ?
                ''', (price, room_id, user_id))

                cursor.execute('''
                    INSERT INTO decorations
                    (room_id, user_id, deco_name, buff_type, buff_value)
                    VALUES (?, ?, ?, ?, ?)
                ''', (room_id, user_id, deco_name, db_buff_type, buff_value))

                conn.commit()

                # 使缓存失效
                self._invalidate_player_cache(room_id, user_id)

                return True
            finally:
                self._release_connection(conn)

        except Exception as e:
            self._handle_exception(e, "购买装饰错误")
            return False

    def get_decoration(self, room_id, user_id, deco_name):
        """获取指定装饰的详情"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT deco_name, deco_level, buff_type, buff_value
                    FROM decorations
                    WHERE room_id = ? AND user_id = ? AND deco_name = ?
                ''', (room_id, user_id, deco_name))

                result = cursor.fetchone()
                if result:
                    # 添加基础价格映射
                    base_prices = {
                        "黄金地板": 22000,
                        "珊瑚礁": 18000,
                        "幸运喷泉": 16000,
                        "防盗门": 15000,
                        "锦鲤池": 12000
                    }
                    return {
                        "name": result[0],
                        "level": result[1],
                        "buff_type": result[2],
                        "buff_value": result[3],
                        "price": base_prices.get(result[0], 15000)  # 如果找不到对应价格，使用默认价格
                    }
                return None

        except Exception as e:
            self._handle_exception(e, "获取装饰详情错误")
            return None

    def upgrade_decoration(self, room_id, user_id, deco_name, cost, new_buff_value=None):
        """升级装饰"""
        try:
            # 检查操作锁
            if not self.check_operation_lock(room_id, user_id, "upgrade"):
                return False

            conn = self._get_connection_from_pool()
            try:
                cursor = conn.cursor()

                # 获取当前装饰信息
                cursor.execute('''
                    SELECT deco_level, buff_type, buff_value FROM decorations
                    WHERE room_id = ? AND user_id = ? AND deco_name = ?
                ''', (room_id, user_id, deco_name))

                result = cursor.fetchone()
                if not result:
                    return False

                current_level, buff_type, current_buff_value = result

                # 检查金币是否足够
                cursor.execute('''
                    SELECT coins FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))

                player_result = cursor.fetchone()
                if not player_result or player_result[0] < cost:
                    return False

                # 如果没有提供新的buff值，则增加10%
                if new_buff_value is None:
                    new_buff_value = current_buff_value * 1.1

                # 扣除金币并升级装饰
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins - ?
                    WHERE room_id = ? AND user_id = ?
                ''', (cost, room_id, user_id))

                cursor.execute('''
                    UPDATE decorations
                    SET deco_level = deco_level + 1,
                        buff_value = ?
                    WHERE room_id = ? AND user_id = ? AND deco_name = ?
                ''', (new_buff_value, room_id, user_id, deco_name))

                conn.commit()

                # 使缓存失效
                self._invalidate_player_cache(room_id, user_id)

                return True
            finally:
                self._release_connection(conn)

        except Exception as e:
            self._handle_exception(e, "升级装饰错误")
            return False

    def get_leaderboard(self, room_id, board_type="wealth"):
        """获取排行榜"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if board_type == "wealth":
                    cursor.execute('''
                        SELECT user_id, coins as value
                        FROM fishing_players
                        WHERE room_id = ?
                        ORDER BY coins DESC
                        LIMIT 10
                    ''', (room_id,))
                elif board_type == "level":
                    cursor.execute('''
                        SELECT user_id, level as value
                        FROM fishing_players
                        WHERE room_id = ?
                        ORDER BY level DESC, exp DESC
                        LIMIT 10
                    ''', (room_id,))
                elif board_type == "rare":
                    cursor.execute('''
                        SELECT user_id, 
                               (rare_fish_count + legendary_fish_count) as value
                        FROM fishing_players
                        WHERE room_id = ?
                        ORDER BY value DESC
                        LIMIT 10
                    ''', (room_id,))
                else:  # success_rate
                    cursor.execute('''
                        SELECT user_id,
                               ROUND(CAST(success_count AS FLOAT) / 
                               CASE WHEN (success_count + fail_count) = 0 THEN 1 
                               ELSE (success_count + fail_count) END * 100, 2) as value
                        FROM fishing_players
                        WHERE room_id = ?
                        ORDER BY value DESC
                        LIMIT 10
                    ''', (room_id,))

                results = cursor.fetchall()
                return [{"user_id": row[0], "value": row[1]} for row in results]

        except Exception as e:
            self._handle_exception(e, "获取排行榜错误")
            return []

    def get_achievements(self, room_id, user_id):
        """获取玩家成就列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT achievement_name, achieved_time
                    FROM achievements
                    WHERE room_id = ? AND user_id = ?
                    ORDER BY achieved_time DESC
                ''', (room_id, user_id))

                results = cursor.fetchall()
                return [
                    {
                        "name": row[0],
                        "time": row[1]
                    }
                    for row in results
                ]

        except Exception as e:
            self._handle_exception(e, "获取成就列表错误")
            return []

    def add_achievement(self, room_id, user_id, achievement_name):
        """添加成就"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO achievements
                    (room_id, user_id, achievement_name)
                    VALUES (?, ?, ?)
                ''', (room_id, user_id, achievement_name))

                conn.commit()
                return cursor.rowcount > 0

        except Exception as e:
            self._handle_exception(e, "添加成就错误")
            return False

    def join_competition(self, room_id, user_id):
        """参加钓鱼比赛"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 检查是否有正在进行的比赛
                cursor.execute('''
                    SELECT id FROM competitions
                    WHERE room_id = ? AND status = 'ongoing'
                    AND end_time > datetime('now')
                ''', (room_id,))

                result = cursor.fetchone()
                if not result:
                    return False

                competition_id = result[0]  # 获取比赛ID

                # 记录参赛信息
                cursor.execute('''
                    INSERT OR IGNORE INTO competition_players
                    (room_id, user_id, competition_id, join_time)
                    VALUES (?, ?, ?, datetime('now'))
                ''', (room_id, user_id, competition_id))

                conn.commit()
                return True

        except Exception as e:
            self._handle_exception(e, "参加比赛错误")
            return False

    def get_competition_rank(self, room_id):
        """获取比赛排名"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT user_id, score
                    FROM competition_players
                    WHERE room_id = ? AND competition_id = (
                        SELECT id FROM competitions
                        WHERE room_id = ? AND status = 'ongoing'
                        AND end_time > datetime('now')
                        LIMIT 1
                    )
                    ORDER BY score DESC
                    LIMIT 10
                ''', (room_id, room_id))

                results = cursor.fetchall()
                return [
                    {
                        "user_id": row[0],
                        "score": row[1]
                    }
                    for row in results
                ]

        except Exception as e:
            self._handle_exception(e, "获取比赛排名错误")
            return []

    def get_tasks(self, room_id, user_id):
        """获取任务列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT task_name, task_type, target, progress,
                           reward_type, reward_amount, expire_time
                    FROM tasks
                    WHERE room_id = ? AND user_id = ?
                    AND expire_time > datetime('now')
                    AND (reward_claimed = 0 OR reward_claimed IS NULL)
                    ORDER BY expire_time DESC
                ''', (room_id, user_id))

                return cursor.fetchall()

        except Exception as e:
            self._handle_exception(e, "获取任务列表错误")
            return []

    def refresh_daily_tasks(self, room_id, user_id):
        """刷新每日任务"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 定义所有可能的任务
                all_tasks = [
                    # 钓鱼次数任务
                    {
                        "name": "初级渔夫",
                        "type": "fishing",
                        "target": 10,
                        "reward": {"type": "coins", "amount": 10000},
                        "description": "发送【抛竿】进行钓鱼，每次成功钓到鱼+1点进度，钓鱼失败不计入进度。需要完成10次成功钓鱼。"
                    },
                    {
                        "name": "中级渔夫",
                        "type": "fishing",
                        "target": 30,
                        "reward": {"type": "coins", "amount": 30000},
                        "description": "发送【抛竿】进行钓鱼，每次成功钓到鱼+1点进度，钓鱼失败不计入进度。需要完成30次成功钓鱼。建议先升级鱼竿提高成功率。"
                    },
                    {
                        "name": "高级渔夫",
                        "type": "fishing",
                        "target": 50,
                        "reward": {"type": "coins", "amount": 50000},
                        "description": "发送【抛竿】进行钓鱼，每次成功钓到鱼+1点进度，钓鱼失败不计入进度。需要完成50次成功钓鱼。建议先升级鱼竿提高成功率。"
                    },

                    # 稀有鱼任务
                    {
                        "name": "稀有收集者",
                        "type": "rare_fish",
                        "target": 1,
                        "reward": {"type": "coins", "amount": 20000},
                        "description": "需要钓到1条稀有或传说鱼。发送【升级鱼竿】提高稀有鱼概率，然后发送【抛竿】钓鱼，成功钓到稀有或传说鱼时+1点进度。"
                    },
                    {
                        "name": "珍稀猎手",
                        "type": "rare_fish",
                        "target": 3,
                        "reward": {"type": "coins", "amount": 60000},
                        "description": "需要钓到3条稀有或传说鱼。发送【升级鱼竿】提高稀有鱼概率，然后发送【抛竿】钓鱼，成功钓到稀有或传说鱼时+1点进度。"
                    },
                    {
                        "name": "传说捕手",
                        "type": "rare_fish",
                        "target": 5,
                        "reward": {"type": "exp", "amount": 10000},
                        "description": "需要钓到5条稀有或传说鱼。发送【升级鱼竿】提高稀有鱼概率，然后发送【抛竿】钓鱼，成功钓到稀有或传说鱼时+1点进度。"
                    },

                    # 收入任务
                    {
                        "name": "小赚一笔",
                        "type": "income",
                        "target": 5000,
                        "reward": {"type": "coins", "amount": 10000},
                        "description": "累计获得5000金币。通过钓鱼和出售鱼获得金币，发送【限时高价】查看高价鱼，卖出高价鱼可以获得更多收益。完成任务奖励和比赛奖励也计入进度。"
                    },
                    {
                        "name": "财富积累",
                        "type": "income",
                        "target": 20000,
                        "reward": {"type": "coins", "amount": 40000},
                        "description": "累计获得20000金币。通过钓鱼和出售鱼获得金币，发送【限时高价】查看高价鱼，卖出高价鱼可以获得更多收益。完成任务奖励和比赛奖励也计入进度。"
                    },
                    {
                        "name": "富甲一方",
                        "type": "income",
                        "target": 50000,
                        "reward": {"type": "exp", "amount": 20000},
                        "description": "累计获得50000金币。通过钓鱼和出售鱼获得金币，发送【限时高价】查看高价鱼，卖出高价鱼可以获得更多收益。完成任务奖励和比赛奖励也计入进度。"
                    }
                ]

                # 随机选择一个任务
                selected_task = random.choice(all_tasks)

                # 先清除所有旧任务
                cursor.execute("DELETE FROM tasks WHERE room_id=? AND user_id=?", (room_id, user_id))

                # 添加新任务
                cursor.execute('''
                    INSERT INTO tasks 
                    (room_id, user_id, task_name, task_type, progress, target,
                     reward_type, reward_amount, expire_time, description)
                    VALUES (?, ?, ?, ?, 0, ?, ?, ?, datetime('now', '+1 day'), ?)
                ''', (room_id, user_id, selected_task['name'], selected_task['type'],
                      selected_task['target'], selected_task['reward']['type'],
                      selected_task['reward']['amount'], selected_task['description']))

                conn.commit()
                return True

        except Exception as e:
            logging.error(f"刷新每日任务错误: {e}")
            return False

    def get_daily_tasks(self, room_id, user_id):
        """获取每日任务"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取当前有效的任务
                cursor.execute('''
                    SELECT task_name, progress, target, reward_type, reward_amount, reward_claimed, task_type, description
                    FROM tasks
                    WHERE room_id = ? AND user_id = ?
                    AND expire_time > datetime('now')
                    AND (reward_claimed = 0 OR reward_claimed IS NULL)
                    LIMIT 1
                ''', (room_id, user_id))

                tasks = cursor.fetchall()

                return [
                    {
                        "name": task[0],
                        "progress": task[1],
                        "target": task[2],
                        "reward_type": task[3],
                        "reward_amount": task[4],
                        "claimed": bool(task[5]),
                        "task_type": task[6],
                        "description": task[7]  # 添加description字段
                    }
                    for task in tasks
                ]

        except Exception as e:
            logging.error(f"获取每日任务错误: {e}")
            return []

    def _validate_task_type(self, task_type):
        """验证任务类型是否有效"""
        valid_types = {
            "fishing": "钓鱼次数",
            "sell": "卖鱼次数",
            "upgrade": "升级次数",
            "competition": "比赛次数",
            "income": "收入任务",
            "rare_fish": "稀有鱼任务"
        }
        return task_type in valid_types

    def update_task_progress(self, room_id, user_id, task_type, progress=1):
        """更新任务进度"""
        try:
            # 验证任务类型
            if not self._validate_task_type(task_type):
                logging.error(f"无效的任务类型: {task_type}")
                return

            logging.info(f"更新任务进度 - 房间: {room_id}, 用户: {user_id}, 类型: {task_type}, 进度: {progress}")

            with self._get_connection() as conn:
                # 获取当前任务
                cur = conn.cursor()

                # 检查是否已经更新过（防止重复更新）
                cur.execute("""
                    SELECT operation_time
                    FROM operation_logs
                    WHERE room_id = ? AND user_id = ? 
                    AND operation_type = 'task_progress_update'
                    AND operation_time > datetime('now', '-1 second')
                    LIMIT 1
                """, (room_id, user_id))

                if cur.fetchone():
                    logging.info("跳过重复的任务进度更新")
                    return

                # 记录本次更新
                cur.execute("""
                    INSERT INTO operation_logs
                    (room_id, user_id, operation_type, operation_time)
                    VALUES (?, ?, 'task_progress_update', datetime('now'))
                """, (room_id, user_id))

                # 获取当前任务
                cur.execute("""
                    SELECT task_name, target, progress, reward_type, reward_amount 
                    FROM tasks 
                    WHERE room_id = ? AND user_id = ? AND task_type = ? 
                    AND expire_time > datetime('now')
                    AND reward_claimed = 0
                """, (room_id, user_id, task_type))
                task = cur.fetchone()

                if not task:
                    logging.warning(f"未找到相关任务 - 房间: {room_id}, 用户: {user_id}, 类型: {task_type}")
                    return

                name, target, current_progress, reward_type, reward_amount = task
                logging.info(f"当前任务信息: 名称={name}, 目标={target}, 当前进度={current_progress}")

                # 更新进度
                new_progress = current_progress + progress
                cur.execute("""
                    UPDATE tasks 
                    SET progress = ? 
                    WHERE room_id = ? AND user_id = ? AND task_type = ? 
                    AND expire_time > datetime('now')
                    AND reward_claimed = 0
                """, (new_progress, room_id, user_id, task_type))

                logging.info(f"更新后进度: {new_progress}")

                # 如果任务完成，发放奖励
                if new_progress >= target and current_progress < target:
                    logging.info(f"任务完成 - 发放奖励: 类型={reward_type}, 数量={reward_amount}")
                    if reward_type == 'coins':
                        self.add_coins(room_id, user_id, reward_amount)
                    elif reward_type == 'exp':
                        self.batch_add_exp(room_id, user_id, reward_amount)

                    # 标记任务为已完成
                    cur.execute("""
                        UPDATE tasks 
                        SET reward_claimed = 1 
                        WHERE room_id = ? AND user_id = ? AND task_type = ? 
                        AND expire_time > datetime('now')
                    """, (room_id, user_id, task_type))

                conn.commit()

        except Exception as e:
            self._handle_exception(e, "更新任务进度错误")

    def create_competition(self, room_id, comp_type, duration):
        """创建新比赛"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                now = datetime.now()
                end_time = now + timedelta(seconds=duration)

                # 使用标准格式存储时间，不包含毫秒
                cursor.execute('''
                    INSERT INTO competitions
                    (room_id, status, start_time, end_time, comp_type)
                    VALUES (?, 'ongoing', ?, ?, ?)
                ''', (
                    room_id,
                    now.strftime("%Y-%m-%d %H:%M:%S"),
                    end_time.strftime("%Y-%m-%d %H:%M:%S"),
                    comp_type
                ))

                conn.commit()
                return True

        except Exception as e:
            logging.error(f"创建比赛错误: {e}")
            logging.error(traceback.format_exc())
            return False

    def end_competition(self, room_id):
        """结束比赛并发放奖励"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取已结束但未处理的比赛
                cursor.execute('''
                    SELECT id FROM competitions
                    WHERE room_id = ? AND status = 'ongoing'
                    AND end_time <= datetime('now')
                ''', (room_id,))

                comp_id = cursor.fetchone()
                if not comp_id:
                    return None

                # 获取前三名
                cursor.execute('''
                    SELECT cp.user_id, cp.score
                    FROM competition_players cp
                    WHERE cp.competition_id = ?
                    ORDER BY cp.score DESC
                    LIMIT 3
                ''', (comp_id[0],))

                winners = cursor.fetchall()

                # 发放奖励
                rewards = [
                    {"coins": 100000, "exp": 1000},  # 第一名
                    {"coins": 50000, "exp": 500},  # 第二名
                    {"coins": 30000, "exp": 300}  # 第三名
                ]

                for i, winner in enumerate(winners):
                    user_id = winner[0]
                    reward = rewards[i]

                    # 更新玩家金币和经验
                    cursor.execute('''
                        UPDATE fishing_players
                        SET coins = coins + ?,
                            exp = exp + ?
                        WHERE room_id = ? AND user_id = ?
                    ''', (reward["coins"], reward["exp"], room_id, user_id))

                    # 记录奖励发放
                    cursor.execute('''
                        INSERT INTO operation_logs
                        (room_id, user_id, operation_type, operation_time, details)
                        VALUES (?, ?, 'competition_reward', datetime('now'), ?)
                    ''', (room_id, user_id, json.dumps({
                        "rank": i + 1,
                        "coins": reward["coins"],
                        "exp": reward["exp"]
                    })))

                # 更新比赛状态
                cursor.execute('''
                    UPDATE competitions
                    SET status = 'ended',
                        winner_id = ?
                    WHERE id = ?
                ''', (winners[0][0] if winners else None, comp_id[0]))

                conn.commit()

                # 返回获奖信息
                return [{
                    "user_id": winner[0],
                    "score": winner[1],
                    "rank": i + 1,
                    "reward_coins": rewards[i]["coins"],
                    "reward_exp": rewards[i]["exp"]
                } for i, winner in enumerate(winners)]

        except Exception as e:
            logging.error(f"结束比赛错误: {e}")
            logging.error(traceback.format_exc())
            return None

    def end_competition_and_reward(self, room_id, rewards):
        """结束比赛并发放奖励"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取比赛参与者
                cursor.execute('''
                    SELECT cp.user_id, cp.score
                    FROM competition_players cp
                    JOIN competitions c ON cp.competition_id = c.id
                    WHERE c.room_id = ? AND c.status = 'ongoing'
                    ORDER BY cp.score DESC
                ''', (room_id,))

                players = cursor.fetchall()
                if not players:
                    return None

                winners = []
                participants = []

                # 处理前三名
                for idx, (user_id, score) in enumerate(players[:3]):
                    if idx < len(rewards['winners']):
                        reward = rewards['winners'][idx]
                        # 发放奖励
                        cursor.execute('''
                            UPDATE fishing_players
                            SET coins = coins + ?,
                                exp = exp + ?
                            WHERE room_id = ? AND user_id = ?
                        ''', (reward['coins'], reward['exp'], room_id, user_id))

                        # 检查升级
                        self.check_and_process_level_up(room_id, user_id, conn)

                        winners.append({
                            'user_id': user_id,
                            'score': score,
                            'reward_coins': reward['coins'],
                            'reward_exp': reward['exp']
                        })

                # 处理其他参与者
                for user_id, score in players[3:]:
                    # 发放参与奖励
                    cursor.execute('''
                        UPDATE fishing_players
                        SET coins = coins + ?,
                            exp = exp + ?
                        WHERE room_id = ? AND user_id = ?
                    ''', (rewards['participation']['coins'],
                          rewards['participation']['exp'],
                          room_id, user_id))

                    # 检查升级
                    self.check_and_process_level_up(room_id, user_id, conn)

                    participants.append({
                        'user_id': user_id,
                        'score': score,
                        'reward_coins': rewards['participation']['coins'],
                        'reward_exp': rewards['participation']['exp']
                    })

                # 更新比赛状态
                cursor.execute('''
                    UPDATE competitions
                    SET status = 'ended',
                        winner_id = ?
                    WHERE room_id = ? AND status = 'ongoing'
                ''', (winners[0]['user_id'] if winners else None, room_id))

                conn.commit()

                return {
                    'winners': winners,
                    'participants': participants
                }

        except Exception as e:
            logging.error(f"结束比赛并发放奖励错误: {e}")
            logging.error(traceback.format_exc())
            return None

    def _execute_with_retry(self, conn, sql, params=None, max_retries=3):
        """带重试的SQL执行"""
        for i in range(max_retries):
            try:
                cursor = conn.cursor()
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                return cursor
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and i < max_retries - 1:
                    time.sleep(0.1 * (i + 1))  # 递增延迟
                    continue
                raise

    def get_inventory(self, room_id, user_id):
        """获取玩家背包"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT fp.name, fp.fish_count, fp.fish_price,
                           sp.special_price, sp.end_time
                    FROM fish_pond fp
                    LEFT JOIN special_price sp ON fp.name = sp.fish_name
                    WHERE fp.room_id = ? AND fp.user_id = ?
                    ORDER BY fp.fish_price DESC
                ''', (room_id, user_id))

                results = cursor.fetchall()
                return [
                    {
                        "name": row[0],
                        "count": row[1],
                        "price": row[2],
                        "special_price": row[3],
                        "special_end": row[4]
                    }
                    for row in results
                ]

        except Exception as e:
            self._handle_exception(e, "获取背包错误")
            return []

    def add_cooldown(self, room_id, user_id, action_type, duration):
        """添加冷却时间"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                end_time = datetime.now() + timedelta(seconds=duration)
                cursor.execute('''
                    INSERT OR REPLACE INTO action_cooldowns
                    (room_id, user_id, action_type, end_time)
                    VALUES (?, ?, ?, ?)
                ''', (room_id, user_id, action_type, end_time))
                return True
        except Exception as e:
            self._handle_exception(e, "添加冷却时间错误")
            return False

    def check_cooldown(self, room_id, user_id, action_type):
        """检查冷却时间"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT end_time 
                    FROM action_cooldowns
                    WHERE room_id = ? AND user_id = ? AND action_type = ?
                ''', (room_id, user_id, action_type))
                result = cursor.fetchone()
                if not result:
                    return 0

                try:
                    # 尝试使用带微秒的格式解析
                    end_time = self._safe_parse_datetime(result[0])
                except ValueError:
                    try:
                        # 尝试使用不带微秒的格式解析
                        end_time = self._safe_parse_datetime(result[0])
                    except ValueError:
                        # 如果还是失败，尝试去掉微秒部分再解析
                        end_time = self._safe_parse_datetime(result[0].split('.')[0])

                remaining = (end_time - datetime.now()).total_seconds()
                return max(0, int(remaining))
        except Exception as e:
            self._handle_exception(e, "检查冷却时间错误")
            return 0

    def get_current_competition(self, room_id):
        """获取当前正在进行的比赛"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT id, room_id, status, start_time, end_time
                    FROM competitions
                    WHERE room_id = ? AND status = 'ongoing'
                    AND end_time > datetime('now')
                    ORDER BY start_time DESC
                    LIMIT 1
                ''', (room_id,))

                result = cursor.fetchone()
                if result:
                    # 处理带毫秒的时间格式
                    def parse_datetime(dt_str):
                        try:
                            # 首先尝试标准格式
                            return self._safe_parse_datetime(dt_str)
                        except ValueError:
                            try:
                                # 如果失败，尝试处理带毫秒的格式
                                return self._safe_parse_datetime(dt_str.split('.')[0])
                            except Exception:
                                # 如果还是失败，返回当前时间
                                logging.error(f"无法解析时间字符串: {dt_str}")
                                return datetime.now()

                    return {
                        'id': result[0],
                        'room_id': result[1],
                        'status': result[2],
                        'start_time': parse_datetime(result[3]),
                        'end_time': parse_datetime(result[4])
                    }
                return None

        except Exception as e:
            logging.error(f"获取当前比赛错误: {e}")
            logging.error(traceback.format_exc())
            return None

    def get_ending_competitions(self):
        """获取需要结束的比赛"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT room_id
                    FROM competitions
                    WHERE status = 'ongoing'
                    AND end_time <= datetime('now')
                ''')
                return [{'room_id': row[0]} for row in cursor.fetchall()]
        except Exception as e:
            self._handle_exception(e, "获取结束比赛错误")
            return []

    def _log_serious_violation(self, room_id, user_id, violation_count):
        """记录严重违规"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO violation_logs
                    (room_id, user_id, violation_type, violation_count, log_time)
                    VALUES (?, ?, 'spam_command', ?, datetime('now'))
                ''', (room_id, user_id, violation_count))
                conn.commit()
        except Exception as e:
            self._handle_exception(e, "记录严重违规错误")

    def check_and_punish_abuse(self, room_id, user_id, action_type, reward_amount):
        """检查并惩罚滥用行为"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = datetime.now()

                # 获取最近5秒内的相同操作次数
                cursor.execute('''
                    SELECT COUNT(*) FROM operation_logs
                    WHERE room_id = ? AND user_id = ? 
                    AND operation_type = ?
                    AND operation_time > datetime('now', '-5 seconds')
                ''', (room_id, user_id, action_type))

                count = cursor.fetchone()[0]

                # 如果5秒内操作超过3次，执行惩罚
                if count >= 3:
                    # 获取玩家最值钱的鱼
                    cursor.execute('''
                        SELECT fish_name, fish_count, fish_price
                        FROM fish_pond
                        WHERE room_id = ? AND user_id = ?
                        ORDER BY fish_price DESC
                        LIMIT 1
                    ''', (room_id, user_id))

                    fish = cursor.fetchone()
                    if fish:
                        fish_name, fish_count, fish_price = fish

                        # 删除这条鱼
                        cursor.execute('''
                            DELETE FROM fish_pond
                            WHERE room_id = ? AND user_id = ? AND fish_name = ?
                        ''', (room_id, user_id, fish_name))

                        # 扣除异常获得的金币
                        cursor.execute('''
                            UPDATE fishing_players
                            SET coins = CASE 
                                WHEN coins >= ? THEN coins - ?
                                ELSE 0
                            END
                            WHERE room_id = ? AND user_id = ?
                        ''', (reward_amount, reward_amount, room_id, user_id))

                        conn.commit()

                        return {
                            "punished": True,
                            "message": f"⚠️ 检测到异常操作！\n💢 系统没收了你的{fish_name} x{fish_count}！\n💰 并扣除了{reward_amount}金币作为惩罚！\n🚫 请勿频繁操作以免遭受更严重处罚！"
                        }

                # 记录本次操作
                cursor.execute('''
                    INSERT INTO operation_logs
                    (room_id, user_id, operation_type, operation_time)
                    VALUES (?, ?, ?, ?)
                ''', (room_id, user_id, action_type, current_time))

                conn.commit()
                return {"punished": False}

        except Exception as e:
            logging.error(f"检查滥用行为错误: {e}")
            logging.error(traceback.format_exc())
            return {"punished": False}

    def check_operation_lock(self, room_id, user_id, action_type):
        """检查操作锁"""
        try:
            key = f"{room_id}_{user_id}_{action_type}"
            current_time = time.time()

            # 清理过期的锁
            self.operation_locks = {k: v for k, v in self.operation_locks.items()
                                    if current_time - v < 0.3}  # 改为0.3秒锁定时间

            # 如果存在锁，返回False
            if key in self.operation_locks:
                return False

            # 添加新锁
            self.operation_locks[key] = current_time
            return True

        except Exception as e:
            logging.error(f"检查操作锁错误: {e}")
            return False

    def is_admin(self, user_id):
        """检查是否是管理员"""
        try:
            # 直接在代码中设置管理员列表
            admin_list = [
                "fengin",  # 这里填写管理员的wxid
                "wxid_xxx2"  # 可以设置多个管理员
            ]
            return user_id in admin_list

        except Exception as e:
            logging.error(f"检查管理员权限错误: {e}")
            return False

    def add_admin(self, user_id):
        """添加管理员"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO fishing_admins (user_id)
                    VALUES (?)
                ''', (user_id,))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"添加管理员错误: {e}")
            return False

    def admin_give_coins(self, admin_id, target_room_id, target_user_id, amount):
        """管理员给玩家发放金币"""
        try:
            if not self.is_admin(admin_id):
                return {"success": False, "message": "你没有管理员权限"}

            # 限制单次赠送金额不超过100万
            if amount > 1000000:
                return {"success": False, "message": "单次赠送金额不能超过100万"}

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 给玩家加金币
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins + ?
                    WHERE room_id = ? AND user_id = ?
                ''', (amount, target_room_id, target_user_id))

                if cursor.rowcount == 0:
                    return {
                        "success": False,
                        "message": "找不到该玩家，请确认玩家已经开始钓鱼游戏"
                    }

                conn.commit()
                return {
                    "success": True,
                    "message": f"已成功给你发放{amount}金币"
                }

        except Exception as e:
            logging.error(f"管理员发放金币错误: {e}")
            return {
                "success": False,
                "message": "发放金币失败，请检查日志"
            }

    def sell_high_price_fish(self, room_id, user_id):
        """一键卖出限时高价鱼"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取所有限时高价的鱼
                cursor.execute('''
                    SELECT fp.name, fp.fish_count, fp.fish_price, sp.special_price, fp.catch_time
                    FROM fish_pond fp
                    INNER JOIN special_price sp ON fp.name = sp.fish_name
                    WHERE fp.room_id = ? AND fp.user_id = ?
                    AND sp.end_time > datetime('now')
                ''', (room_id, user_id))

                results = cursor.fetchall()
                if not results:
                    return {"success": False, "message": "没有可以高价卖出的鱼哦~"}

                total_income = 0
                sold_fish = []

                for fish_name, count, base_price, special_price, catch_time in results:
                    income = count * special_price
                    total_income += income

                    # 添加到已售出鱼列表
                    sold_fish.append({
                        "name": fish_name,
                        "count": count,
                        "price": special_price,
                        "income": income,
                        "catch_time": catch_time  # 添加捕获时间
                    })

                    # 删除已卖出的鱼
                    cursor.execute('''
                        DELETE FROM fish_pond
                        WHERE room_id = ? AND user_id = ? AND name = ?
                    ''', (room_id, user_id, fish_name))

                # 更新玩家金币
                cursor.execute('''
                    UPDATE fishing_players
                    SET coins = coins + ?,
                        total_income = total_income + ?
                    WHERE room_id = ? AND user_id = ?
                ''', (total_income, total_income, room_id, user_id))

                # 如果在比赛中，更新比赛积分
                cursor.execute('''
                    UPDATE competition_players
                    SET score = score + ?
                    WHERE room_id = ? AND user_id = ?
                    AND competition_id IN (
                        SELECT id FROM competitions 
                        WHERE room_id = ? AND status = 'ongoing'
                        AND end_time > datetime('now')
                    )
                ''', (total_income, room_id, user_id, room_id))

                conn.commit()

                return {
                    "success": True,
                    "message": "成功卖出高价鱼！",
                    "sold_fish": sold_fish,
                    "total_income": total_income
                }

        except Exception as e:
            logging.error(f"一键卖高价鱼错误: {e}")
            logging.error(traceback.format_exc())
            return {
                "success": False,
                "message": "卖鱼失败，请稍后再试",
                "sold_fish": [],
                "total_income": 0
            }

    def is_in_competition(self, room_id, user_id):
        """检查玩家是否已经参加比赛"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT cp.id
                    FROM competition_players cp
                    JOIN competitions c ON cp.competition_id = c.id
                    WHERE c.room_id = ? 
                    AND c.status = 'ongoing'
                    AND cp.user_id = ?
                ''', (room_id, user_id))

                result = cursor.fetchone()
                return bool(result)

        except Exception as e:
            logging.error(f"检查比赛参与状态错误: {e}")
            logging.error(traceback.format_exc())
            return False

    def add_buff(self, room_id, user_id, buff_type, buff_value, duration):
        """添加临时增益效果"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                end_time = datetime.now() + timedelta(seconds=duration)

                cursor.execute('''
                    INSERT OR REPLACE INTO player_buffs
                    (room_id, user_id, buff_type, buff_value, end_time)
                    VALUES (?, ?, ?, ?, ?)
                ''', (room_id, user_id, buff_type, buff_value, end_time))

                conn.commit()
                return True

        except Exception as e:
            logging.error(f"添加buff错误: {e}")
            logging.error(traceback.format_exc())
            return False

    def _get_connection(self):
        """获取数据库连接"""
        try:
            return sqlite3.connect(self.db_path)
        except Exception as e:
            logging.error(f"获取数据库连接错误: {e}")
            logging.error(traceback.format_exc())
            raise

    def remove_fish_from_pool(self, fish_name):
        """从鱼池中移除一条鱼，如果鱼池被清空则返回True"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 更新鱼的数量
                cursor.execute('''
                    UPDATE fish_pool
                    SET count = count - 1
                    WHERE name = ? AND count > 0
                ''', (fish_name,))

                # 删除数量为0的鱼
                cursor.execute('''
                    DELETE FROM fish_pool
                    WHERE count <= 0
                ''')

                # 检查鱼池是否为空
                cursor.execute('SELECT COUNT(*) FROM fish_pool')
                count = cursor.fetchone()[0]

                return count == 0

        except Exception as e:
            logging.error(f"从鱼池移除鱼错误: {e}")
            return False

    def create_daily_task(self, room_id, user_id, task):
        """创建每日任务"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 设置任务过期时间为24小时后
                expire_time = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")

                cursor.execute('''
                    INSERT OR REPLACE INTO daily_tasks 
                    (room_id, user_id, task_name, task_type, progress, target,
                     reward_type, reward_amount, expire_time, description)
                    VALUES (?, ?, ?, ?, 0, ?, ?, ?, ?, ?)
                ''', (room_id, user_id, task['name'], task['type'], task['target'],
                      task['reward']['type'], task['reward']['amount'], expire_time, task['desc']))

                conn.commit()
                return True

        except Exception as e:
            self._handle_exception(e, "创建每日任务错误")
            return False

    def update_last_steal(self, room_id, user_id):
        """更新最后偷鱼时间"""
        try:
            # 使用操作锁防止并发问题
            lock_key = f"update_steal_{room_id}_{user_id}"
            current_time = time.time()

            # 检查是否有锁
            if hasattr(self, 'cooldown_locks') and lock_key in self.cooldown_locks:
                # 如果锁的时间小于1秒，说明有其他线程正在处理，直接返回
                if current_time - self.cooldown_locks[lock_key] < 1:
                    logging.warning(f"更新偷鱼时间并发控制 - 房间:{room_id} 用户:{user_id}")
                    # 直接返回成功，因为这个方法不直接与用户交互
                    return True

            # 设置锁
            if not hasattr(self, 'cooldown_locks'):
                self.cooldown_locks = {}
            self.cooldown_locks[lock_key] = current_time

            with sqlite3.connect(self.db_path) as conn:
                # 设置更高的隔离级别
                conn.isolation_level = 'EXCLUSIVE'
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE fishing_players
                    SET last_steal = ?
                    WHERE room_id = ? AND user_id = ?
                ''', (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), room_id, user_id))
                conn.commit()

                # 释放锁
                if hasattr(self, 'cooldown_locks'):
                    self.cooldown_locks.pop(lock_key, None)

                return True
        except Exception as e:
            # 确保异常情况下也释放锁
            if hasattr(self, 'cooldown_locks'):
                self.cooldown_locks.pop(f"update_steal_{room_id}_{user_id}", None)
            logging.error(f"更新偷鱼时间错误: {e}")
            return False

    def update_task_progress_db(self, room_id, user_id, task_type, new_progress):
        """直接更新任务进度到指定值"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 更新任务进度
                cursor.execute("""
                    UPDATE tasks 
                    SET progress = ? 
                    WHERE room_id = ? AND user_id = ? AND task_type = ? 
                    AND expire_time > datetime('now')
                    AND reward_claimed = 0
                """, (new_progress, room_id, user_id, task_type))

                if cursor.rowcount > 0:
                    conn.commit()
                    logging.info(
                        f"任务进度已更新 - 房间:{room_id} 用户:{user_id} 类型:{task_type} 新进度:{new_progress}")
                    return True
                else:
                    logging.warning(f"未找到可更新的任务 - 房间:{room_id} 用户:{user_id} 类型:{task_type}")
                    return False

        except Exception as e:
            self._handle_exception(e, "直接更新任务进度错误")
            return False

    def mark_task_completed(self, room_id, user_id, task_type):
        """标记任务为已完成并已领取奖励"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 标记任务为已完成
                cursor.execute("""
                    UPDATE tasks 
                    SET reward_claimed = 1 
                    WHERE room_id = ? AND user_id = ? AND task_type = ? 
                    AND expire_time > datetime('now')
                    AND reward_claimed = 0
                """, (room_id, user_id, task_type))

                if cursor.rowcount > 0:
                    conn.commit()
                    logging.info(f"任务已标记为完成 - 房间:{room_id} 用户:{user_id} 类型:{task_type}")
                    return True
                else:
                    logging.warning(f"未找到可标记完成的任务 - 房间:{room_id} 用户:{user_id} 类型:{task_type}")
                    return False

        except Exception as e:
            self._handle_exception(e, "标记任务完成错误")
            return False

    def set_steal_cooldown(self, room_id, user_id, cooldown_seconds=10):
        """设置偷鱼冷却时间
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            cooldown_seconds: 冷却时间（秒），默认为10秒
            
        Returns:
            bool: 是否设置成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 直接设置last_steal时间为当前时间
                # 然后在get_steal_cooldown方法中传入指定的冷却时间
                cursor.execute('''
                    UPDATE fishing_players
                    SET last_steal = ?
                    WHERE room_id = ? AND user_id = ?
                ''', (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), room_id, user_id))

                conn.commit()
                logging.info(f"设置偷鱼冷却时间: {cooldown_seconds}秒 - 房间:{room_id} 用户:{user_id}")
                return True

        except Exception as e:
            logging.error(f"设置偷鱼冷却时间错误: {e}")
            return False

    def set_steal_cooldown_10s(self, room_id, user_id):
        """设置偷鱼失败的10秒冷却时间
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            
        Returns:
            bool: 是否设置成功
        """
        try:
            # 使用操作锁防止并发问题
            lock_key = f"set_steal_cooldown_{room_id}_{user_id}"
            current_time = time.time()

            # 检查是否有锁
            if hasattr(self, 'cooldown_locks') and lock_key in self.cooldown_locks:
                # 如果锁的时间小于1秒，说明有其他线程正在处理，直接返回
                if current_time - self.cooldown_locks[lock_key] < 1:
                    logging.warning(f"设置偷鱼冷却并发控制 - 房间:{room_id} 用户:{user_id}")
                    # 直接返回成功，因为这个方法不直接与用户交互
                    return True

            # 设置锁
            if not hasattr(self, 'cooldown_locks'):
                self.cooldown_locks = {}
            self.cooldown_locks[lock_key] = current_time

            # 创建一个特殊的标记，表示这是一个10秒冷却
            special_time = "10s_" + datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            with sqlite3.connect(self.db_path) as conn:
                # 设置更高的隔离级别
                conn.isolation_level = 'EXCLUSIVE'
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE fishing_players
                    SET last_steal = ?
                    WHERE room_id = ? AND user_id = ?
                ''', (special_time, room_id, user_id))

                conn.commit()
                logging.info(f"设置偷鱼失败10秒冷却时间 - 房间:{room_id} 用户:{user_id}")

                # 释放锁
                if hasattr(self, 'cooldown_locks'):
                    self.cooldown_locks.pop(lock_key, None)

                return True

        except Exception as e:
            # 确保异常情况下也释放锁
            if hasattr(self, 'cooldown_locks'):
                self.cooldown_locks.pop(f"set_steal_cooldown_{room_id}_{user_id}", None)
            logging.error(f"设置偷鱼失败冷却时间错误: {e}")
            return False

    def update_competition_score(self, room_id, user_id, score_to_add):
        """更新比赛分数
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            score_to_add: 要增加的分数
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 如果在比赛中，更新比赛积分
                cursor.execute('''
                    UPDATE competition_players
                    SET score = score + ?
                    WHERE room_id = ? AND user_id = ?
                    AND competition_id IN (
                        SELECT id FROM competitions 
                        WHERE room_id = ? AND status = 'ongoing'
                        AND end_time > datetime('now')
                    )
                ''', (score_to_add, room_id, user_id, room_id))

                conn.commit()

                # 检查是否有更新
                if cursor.rowcount > 0:
                    logging.info(f"更新比赛分数成功 - 房间:{room_id} 用户:{user_id} 增加分数:{score_to_add}")
                    return True
                else:
                    # 没有更新可能是因为用户没有参加比赛，这不是错误
                    logging.info(f"用户未参加比赛，无需更新分数 - 房间:{room_id} 用户:{user_id}")
                    return False

        except Exception as e:
            self._handle_exception(e, "更新比赛分数错误")
            return False

    def get_active_rooms(self):
        """获取所有活跃的房间ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 从fishing_players表中获取所有不同的room_id
                cursor.execute('''
                    SELECT DISTINCT room_id FROM fishing_players
                ''')

                results = cursor.fetchall()
                return [row[0] for row in results]

        except Exception as e:
            logging.error(f"获取活跃房间错误: {e}")
            logging.error(traceback.format_exc())
            return []

    def update_multiple_stats(self, room_id, user_id, stats_updates):
        """批量更新多个统计数据，减少数据库连接次数
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            stats_updates: 字典，键为字段名，值为增加的值
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 开始事务
                cursor.execute("BEGIN TRANSACTION")

                try:
                    # 批量更新多个字段
                    for stat, value in stats_updates.items():
                        cursor.execute(f"""
                            UPDATE fishing_players
                            SET {stat} = {stat} + ?
                            WHERE room_id = ? AND user_id = ?
                        """, (value, room_id, user_id))

                    # 提交事务
                    conn.commit()
                    return True
                except Exception as e:
                    # 回滚事务
                    conn.rollback()
                    logging.error(f"批量更新统计数据错误: {e}")
                    return False

        except Exception as e:
            self._handle_exception(e, "批量更新统计数据错误")
            return False

    def batch_update_fishing_stats(self, room_id, user_id, success=True):
        """批量更新钓鱼统计，使用延迟写入
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            success: 是否钓鱼成功
        """
        if success:
            sql = '''
                UPDATE fishing_players
                SET success_count = success_count + 1
                WHERE room_id = ? AND user_id = ?
            '''
        else:
            sql = '''
                UPDATE fishing_players
                SET fail_count = fail_count + 1
                WHERE room_id = ? AND user_id = ?
            '''

        # 添加到批量写入队列
        self.batch_writer.add_update(sql, (room_id, user_id))

    def batch_add_exp(self, room_id, user_id, exp_amount):
        """批量添加经验，使用延迟写入
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            exp_amount: 经验值
        """
        sql = '''
            UPDATE fishing_players
            SET exp = exp + ?
            WHERE room_id = ? AND user_id = ?
        '''

        # 添加到批量写入队列
        #self.batch_writer.add_update(sql, (exp_amount, room_id, user_id))
        Thread(target=self.batch_writer.add_update, args=(sql, (exp_amount, room_id, user_id))).start()

    def batch_add_coins(self, room_id, user_id, amount):
        """批量添加金币，使用延迟写入
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            amount: 金币数量
        """
        sql = '''
            UPDATE fishing_players
            SET coins = coins + ?
            WHERE room_id = ? AND user_id = ?
        '''

        # 添加到批量写入队列
        self.batch_writer.add_update(sql, (amount, room_id, user_id))

    def _get_connection_from_pool(self):
        """从连接池获取一个连接"""
        return self.connection_pool.get_connection()

    def _release_connection(self, conn):
        """释放一个连接回连接池"""
        self.connection_pool.release_connection(conn)

    def get_player_info_with_pool(self, room_id, user_id):
        """使用连接池获取玩家信息
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            
        Returns:
            dict: 玩家信息
        """
        try:
            conn = self._get_connection_from_pool()
            try:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))

                result = cursor.fetchone()
                if not result:
                    return None

                # 获取列名
                columns = [description[0] for description in cursor.description]

                # 构建玩家信息字典
                player_info = {}
                for i, column in enumerate(columns):
                    player_info[column] = result[i]

                return player_info
            finally:
                self._release_connection(conn)

        except Exception as e:
            self._handle_exception(e, "获取玩家信息错误")
            return None

    def get_fish_pond_with_pool(self, room_id, user_id):
        """使用连接池获取鱼塘信息
        
        Args:
            room_id: 房间ID
            user_id: 用户ID
            
        Returns:
            list: 鱼塘信息列表
        """
        try:
            conn = self._get_connection_from_pool()
            try:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT name, fish_count, fish_price, catch_time
                    FROM fish_pond
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))

                results = cursor.fetchall()
                if not results:
                    return []

                fish_list = []
                for row in results:
                    fish_list.append({
                        'name': row[0],
                        'count': row[1],
                        'price': row[2],
                        'catch_time': row[3] if len(row) > 3 else None
                    })

                return fish_list
            finally:
                self._release_connection(conn)

        except Exception as e:
            self._handle_exception(e, "获取鱼塘信息错误")
            return []

    def close(self):
        """关闭数据库连接"""
        try:
            # 关闭连接池中的所有连接
            if hasattr(self, 'connection_pool'):
                self.connection_pool.close_all()

            # 刷新批量写入器中的所有待处理操作
            if hasattr(self, 'batch_writer'):
                self.batch_writer.flush()

            logging.info("数据库连接已关闭")
        except Exception as e:
            logging.error(f"关闭数据库连接错误: {e}")
            logging.error(traceback.format_exc())

    def transfer_fish(self, from_room_id, from_user_id, to_room_id, to_user_id, fish_name, fish_count, fish_price):
        """将鱼从一个玩家转移到另一个玩家
        
        Args:
            from_room_id: 源房间ID
            from_user_id: 源用户ID
            to_room_id: 目标房间ID
            to_user_id: 目标用户ID
            fish_name: 鱼的名称
            fish_count: 转移的数量
            fish_price: 鱼的价格
            
        Returns:
            bool: 是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 设置更高的隔离级别
                conn.isolation_level = 'EXCLUSIVE'
                cursor = conn.cursor()

                # 从源玩家鱼塘减少
                cursor.execute('''
                    SELECT fish_count
                    FROM fish_pond
                    WHERE room_id = ? AND user_id = ? AND name = ?
                ''', (from_room_id, from_user_id, fish_name))

                result = cursor.fetchone()
                if not result or result[0] < fish_count:
                    return False

                if result[0] <= fish_count:
                    cursor.execute('''
                        DELETE FROM fish_pond
                        WHERE room_id = ? AND user_id = ? AND name = ?
                    ''', (from_room_id, from_user_id, fish_name))
                else:
                    cursor.execute('''
                        UPDATE fish_pond
                        SET fish_count = fish_count - ?
                        WHERE room_id = ? AND user_id = ? AND name = ?
                    ''', (fish_count, from_room_id, from_user_id, fish_name))

                # 添加到目标玩家的鱼塘
                cursor.execute('''
                    INSERT INTO fish_pond (room_id, user_id, name, fish_count, fish_price, catch_time)
                    VALUES (?, ?, ?, ?, ?, ?)
                    ON CONFLICT(room_id, user_id, name) 
                    DO UPDATE SET fish_count = fish_count + excluded.fish_count
                ''', (to_room_id, to_user_id, fish_name, fish_count, fish_price,
                      datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

                # 更新偷鱼时间
                cursor.execute('''
                    UPDATE fishing_players
                    SET last_steal = ?
                    WHERE room_id = ? AND user_id = ?
                ''', (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), to_room_id, to_user_id))

                conn.commit()
                return True

        except Exception as e:
            self._handle_exception(e, "转移鱼错误")
            return False

    def get_task_progress(self, room_id, user_id):
        """获取任务进度

        Args:
            room_id: 房间ID
            user_id: 用户ID

        Returns:
            list: 任务进度列表，每个元素包含任务类型、进度、目标和完成状态
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT task_type, progress, target, claimed
                    FROM daily_tasks
                    WHERE room_id = ? AND user_id = ?
                """, (room_id, user_id))
                tasks = cursor.fetchall()

                result = []
                for task in tasks:
                    result.append({
                        'task_type': task[0],
                        'progress': task[1],
                        'target': task[2],
                        'completed': task[3] == 1
                    })

                return result

        except Exception as e:
            self._handle_exception(e, "获取任务进度错误")
            return []

    def get_player_title(self, room_id, user_id):
        """获取玩家称号"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT title
                    FROM fishing_players
                    WHERE room_id = ? AND user_id = ?
                ''', (room_id, user_id))
                result = cursor.fetchone()
                return result[0] if result else "钓鱼新手"
        except Exception as e:
            self._handle_exception(e, "获取玩家称号错误")
            return "钓鱼新手"

    def get_title_buff(self, title_name):
        """获取称号对应的buff"""
        try:
            for title in self.titles:
                if title[0] == title_name:
                    return {
                        "buff_type": title[3],
                        "buff_value": title[4]
                    }
            return {"buff_type": None, "buff_value": 0}
        except Exception as e:
            self._handle_exception(e, "获取称号buff错误")
            return {"buff_type": None, "buff_value": 0}

    def _invalidate_player_cache(self, room_id, user_id):
        """使玩家相关的缓存失效，通知FishingSystem更新缓存"""
        # 这个方法在数据库类中调用，但实际的缓存在FishingSystem类中
        # 我们可以通过发送一个事件或者在下次获取数据时强制刷新来实现
        # 这里我们简单地记录一下，实际的缓存失效会在FishingSystem中处理
        logging.info(f"标记玩家缓存需要更新: 房间{room_id} 用户{user_id}")
        # 如果有全局事件系统，可以在这里触发一个缓存失效事件

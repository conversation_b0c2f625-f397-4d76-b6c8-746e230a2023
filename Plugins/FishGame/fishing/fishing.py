import os
import re
import time
import json
import yaml
import random
import logging
import traceback
import threading
import weakref
from datetime import datetime
from collections import OrderedDict
from threading import Thread
from Plugins.FishGame.fishing.database import FishingDB
from PIL import Image, ImageDraw
import sqlite3

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class PlayerCache:
    """玩家数据缓存类，用于减少数据库访问"""

    def __init__(self, max_size=1000, ttl=60):  # 默认缓存60秒
        self.cache = {}
        self.max_size = max_size
        self.ttl = ttl
        self.lock = threading.Lock()

    def get(self, key):
        """获取缓存数据，如果过期或不存在则返回None"""
        with self.lock:
            if key in self.cache:
                data, timestamp = self.cache[key]
                if time.time() - timestamp < self.ttl:
                    return data
                else:
                    # 过期数据
                    del self.cache[key]
            return None

    def set(self, key, value):
        """设置缓存数据"""
        with self.lock:
            # 如果缓存已满，清除最旧的20%数据
            if len(self.cache) >= self.max_size:
                # 按时间戳排序
                sorted_items = sorted(self.cache.items(), key=lambda x: x[1][1])
                # 删除最旧的20%
                items_to_remove = int(len(sorted_items) * 0.2)
                for i in range(items_to_remove):
                    if i < len(sorted_items):
                        del self.cache[sorted_items[i][0]]

            self.cache[key] = (value, time.time())

    def invalidate(self, key):
        """使指定键的缓存失效"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]

    def clear(self):
        """清空所有缓存"""
        with self.lock:
            self.cache.clear()


class FishingSystem:
    """钓鱼系统类，负责处理钓鱼、升级、交易等逻辑"""

    def __init__(self, wcf):
        """初始化钓鱼系统"""
        try:
            self.wcf = wcf
            self.db = FishingDB()

            # 统计数据
            self.stats = {
                "total_fish": 0,
                "total_coins": 0,
                "rare_fish": 0,
                "special_events": 0,
                "successful_steals": 0
            }

            # 用于并发控制和防刷的数据结构
            # 使用OrderedDict限制大小，防止无限增长
            self.user_last_actions = OrderedDict()  # 格式: {f"{room_id}_{user_id}_{action}": timestamp}
            self.user_freeze_status = {}  # 格式: {f"{room_id}_{user_id}": {"until": timestamp, "reason": reason}}
            self.concurrent_threshold = 0.1  # 100毫秒内的操作视为并发
            self.freeze_duration = 180  # 冻结时间3分钟（180秒）

            # 设置字典大小限制
            self.MAX_ACTION_ENTRIES = 10000  # 最多记录10000条操作记录
            self.MAX_FREEZE_ENTRIES = 1000  # 最多记录1000条冻结状态
            self.MAX_COOLDOWN_LOCKS = 5000  # 最多记录5000个冷却锁

            # 使用弱引用字典存储临时数据
            self.temp_cache = weakref.WeakValueDictionary()  # 临时缓存，会自动清理不再使用的对象

            # 初始化玩家数据缓存
            self.player_cache = PlayerCache(max_size=500, ttl=60)  # 玩家信息缓存60秒
            self.fish_pond_cache = PlayerCache(max_size=300, ttl=30)  # 鱼塘信息缓存30秒
            self.inventory_cache = PlayerCache(max_size=300, ttl=30)  # 背包信息缓存30秒

            # 初始化操作记录和限制
            self.action_records = {}  # 用于记录用户操作频率
            self.banned_users = {}  # 用于记录被封禁的用户

            # 定义不同操作类型的限制
            self.action_limits = {
                "fishing": {"count": 1, "window": 60},  # 60秒内最多钓鱼5次
                "steal_fish": {"count": 5, "window": 30},  # 30秒内最多偷鱼3次
                "sell": {"count": 20, "window": 60},  # 60秒内最多卖鱼20次
                "upgrade": {"count": 50, "window": 30},  # 30秒内最多升级50次
                "buy": {"count": 5, "window": 30},  # 30秒内最多购买5次
                "search_dog": {"count": 2, "window": 600}  # 10分钟内最多找狗2次
            }

            # 违规操作警告消息
            self.violation_messages = [
                "⚠️ 操作太频繁了，请慢一点！",
                "🚫 检测到异常操作，请正常游戏~",
                "⏰ 系统提示：操作速度过快，请稍后再试",
                "🔍 安全系统：检测到可疑操作，请注意游戏规则",
                "🛑 请不要频繁操作，否则可能被临时封禁"
            ]

            # 加载配置
            self._load_config()

            # 初始化定时器
            self._init_timers()

            # 初始化图片资源
            ##self._init_image_resources()

            logging.info("钓鱼系统初始化完成")

        except Exception as e:
            logging.error(f"初始化钓鱼系统错误: {e}")
            self._handle_exception(e)
            # 确保即使出错也初始化基本数据结构，防止后续操作出错
            if not hasattr(self, 'user_last_actions'):
                self.user_last_actions = OrderedDict()
            if not hasattr(self, 'user_freeze_status'):
                self.user_freeze_status = {}

    def _load_config(self):
        """加载配置文件"""
        try:
            # 尝试多种路径方式加载配置文件
            possible_paths = [
                # 方式1：使用相对路径（从当前文件位置）
                os.path.join(os.path.dirname(__file__), "../config.yaml"),
                # 方式2：使用相对路径（从当前工作目录）
                os.path.join("fishing", "Fish.yaml"),
                # 方式4：直接使用绝对路径
                "D:\\code\\bot\\mossbot_v2\\Plugins\\FishGame\\config.yaml"
            ]

            config_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    logging.info(f"找到配置文件: {path}")
                    break

            if not config_path:
                logging.error("无法找到配置文件，尝试的路径: " + ", ".join(possible_paths))
                raise FileNotFoundError("无法找到配置文件 Fish.yaml")

            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 保存完整配置
            self.config = config

            # 基础配置
            self.cooldown = config['cooldown']['fishing']
            self.steal_cooldown = config['cooldown']['steal']
            self.dog_search_cooldown = config['cooldown']['search_dog']
            self.pool_refresh_interval = config['refresh_interval']['pool']
            self.market_refresh_interval = config['refresh_interval']['market']

            # 偷鱼惩罚配置
            self.steal_punish_min = config.get('steal_punish', {}).get('min', 200)
            self.steal_punish_max = config.get('steal_punish', {}).get('max', 4000)

            # 大鱼偷取配置
            self.steal_big_line = config.get('steal_big', {}).get('line', 5999)
            self.steal_big_rate = config.get('steal_big', {}).get('rate', 0.7)

            # 稀有度配置
            self.rarity_weights = config['rarity_weights']

            # 升级配置
            self.rod_upgrade = config['upgrade']['rod']
            self.dog_upgrade = config['upgrade']['dog']

            # 特殊事件配置
            self.events = config['events']

            logging.info("配置加载完成")

        except Exception as e:
            logging.error(f"加载配置文件错误: {e}")
            self._handle_exception(e)
            # 使用默认配置
            self._load_default_config()

    def _handle_exception(self, e, context=""):
        """统一异常处理"""
        error_msg = f"{context}: {str(e)}" if context else str(e)
        logging.error(error_msg)
        logging.error(traceback.format_exc())

        # 记录错误统计
        error_key = type(e).__name__
        if not hasattr(self, 'error_stats'):
            self.error_stats = {}
        self.error_stats[error_key] = self.error_stats.get(error_key, 0) + 1

    def update_stats(self, stat_type, value=1):
        """更新统计数据"""
        try:
            if stat_type in self.stats:
                self.stats[stat_type] += value

            # 定期保存统计数据
            if self.stats["total_fish"] % 100 == 0:
                self._save_stats()

        except Exception as e:
            self._handle_exception(e, "更新统计数据错误")

    def _save_stats(self):
        """保存统计数据"""
        try:
            stats_path = os.path.join(os.path.dirname(__file__), "stats.json")
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "stats": self.stats,
                    "error_stats": getattr(self, 'error_stats', {}),
                    "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self._handle_exception(e, "保存统计数据错误")

    def show_stats(self, room_id, user_id):
        """显示统计信息"""
        try:
            msg = (
                "🎣 钓鱼系统统计 📊\n\n"
                f"总钓鱼次数：{self.stats['total_fish']}\n"
                f"总收入：{self.stats['total_coins']}金币\n"
                f"稀有鱼获得：{self.stats['rare_fish']}\n"
                f"特殊事件触发：{self.stats['special_events']}\n"
                f"成功偷鱼次数：{self.stats['successful_steals']}\n\n"
                "系统状态：✅ 正常运行中"
            )
            self._send_message(room_id, msg)

        except Exception as e:
            self._handle_exception(e, "显示统计信息错误")
            self._send_message(room_id, "获取统计信息失败，请稍后再试")

    def handle_message(self, msg):
        """处理钓鱼相关消息"""
        #roomId = msgData.get('fromWxId')
        #sender = msgData.get('finalFromWxId')
        #robotId = msgData.get('robotId')
        #wxName = msgData.get('finalFromNickName')
        #atWxIdList = msgData.get('atWxIdList')
        #content = msgData.get('message').strip()

        atWxIdList = msg.get('atWxIdList')
        room_id = msg.get('fromWxId')
        user_id = msg.get('finalFromWxId')
        wxName = msg.get('finalFromNickName')
        try:
            content = msg.get('message').strip().lower()

            # 检查用户是否被冻结
            freeze_key = f"{room_id}_{user_id}"
            if freeze_key in self.user_freeze_status:
                freeze_info = self.user_freeze_status[freeze_key]
                current_time = time.time()

                # 如果冻结时间已过，解除冻结
                if current_time >= freeze_info["until"]:
                    del self.user_freeze_status[freeze_key]
                    logging.info(f"用户 {user_id} 在房间 {room_id} 的冻结已解除")
                else:
                    # 计算剩余冻结时间
                    remaining_time = int(freeze_info["until"] - current_time)
                    self._send_message(
                        room_id,
                        f"⚠️ {freeze_info['reason']}，冻结你啦~\n"
                        f"剩余冻结时间: {remaining_time}秒\n"
                        f"请耐心等待，不要继续尝试操作",
                        user_id
                    )
                    return

            # 装饰系统的固定关键词映射
            deco_buy_map = {
                "买锦鲤池": "锦鲤池",
                "买珊瑚礁": "珊瑚礁",
                "买防盗门": "防盗门",
                "买幸运喷泉": "幸运喷泉",
                "买黄金地板": "黄金地板"
            }

            # 装饰升级的固定关键词映射
            deco_upgrade_map = {
                "升级锦鲤池": "锦鲤池",
                "升级珊瑚礁": "珊瑚礁",
                "升级防盗门": "防盗门",
                "升级幸运喷泉": "幸运喷泉",
                "升级黄金地板": "黄金地板",
                # 添加简短命令支持
                "升锦鲤池": "锦鲤池",
                "升珊瑚礁": "珊瑚礁",
                "升防盗门": "防盗门",
                "升幸运喷泉": "幸运喷泉",
                "升黄金地板": "黄金地板"
            }

            # 提取命令关键词
            command = content
            if " " in content:
                command = content.split(" ")[0]
            elif "#" in content:
                command = content.split("#")[0]

            # 检查并发操作
            action_key = f"{room_id}_{user_id}_{command}"
            current_time = time.time()

            if action_key in self.user_last_actions and "升级" not in command:
                last_action_time = self.user_last_actions[action_key]
                time_diff = current_time - last_action_time

                # 如果操作间隔小于阈值，视为并发操作
                if time_diff < self.concurrent_threshold:
                    logging.warning(
                        f"检测到并发操作: 用户 {user_id} 在房间 {room_id} 连续发送 {command} 命令，间隔 {time_diff:.3f}秒")

                    # 冻结用户
                    self.user_freeze_status[freeze_key] = {
                        "until": current_time + self.freeze_duration,
                        "reason": "你小子再刷bug看看！"
                    }

                    # 发送警告消息
                    self._send_message(
                        room_id,
                        f"🚫 警告: 检测到疑似刷BUG行为!\n"
                        f"你已被冻结 {self.freeze_duration}秒\n"
                        f"请不要尝试利用系统漏洞，否则可能被永久封禁",
                        user_id
                    )
                    return

            # 更新最后操作时间
            self.user_last_actions[action_key] = current_time

            # 检查并限制user_last_actions大小
            if len(self.user_last_actions) > self.MAX_ACTION_ENTRIES:
                # 移除最旧的条目（OrderedDict中的第一个条目）
                self.user_last_actions.popitem(last=False)

            # 检查并限制user_freeze_status大小
            if len(self.user_freeze_status) > self.MAX_FREEZE_ENTRIES:
                # 找出最早过期的条目删除
                earliest_expiry = None
                earliest_key = None
                for key, data in self.user_freeze_status.items():
                    if earliest_expiry is None or data["until"] < earliest_expiry:
                        earliest_expiry = data["until"]
                        earliest_key = key
                if earliest_key:
                    del self.user_freeze_status[earliest_key]

            # 以下是原有的命令处理逻辑
            if content == "钓鱼" or content == "0":
                self.show_info(room_id, user_id, wxName)

            elif content == "抛竿" or content == "1":
                self.start_fishing(room_id, user_id,wxName)

            elif content == "鱼塘" or content == "2":
                self.show_pond(room_id, user_id)

            elif content == "一键卖鱼":
                self.sell_all_fish(room_id, user_id)

            elif content == "特价卖鱼" or content == "一键卖高价" or content == "40":
                self.sell_high_price_fish(room_id, user_id)

            elif content.startswith("卖鱼#"):
                fish_name = content.split("#")[1].strip()
                self.sell_fish(room_id, user_id, fish_name)

            elif content == "升级鱼竿":
                self.upgrade_rod(room_id, user_id)

            elif content == "升级旺财":
                self.upgrade_dog(room_id, user_id)

            elif content == "找旺财" or content == "找回旺财":
                self.search_dog(room_id, user_id)

            elif content == "确认雇佣":
                self.confirm_hire_dog(room_id, user_id)

            elif content == "偷鱼" and hasattr(self.wcf, "get_message_at_users"):
                # 处理@方式的偷鱼
                # at_users = self.wcf.get_message_at_users(msg)
                if atWxIdList:
                    self.steal_fish(room_id, user_id, atWxIdList[0])
                else:
                    self._send_message(room_id, "请@要偷鱼的玩家，例如：偷鱼 @玩家")

            elif content == "钓鱼帮助" or content == "玩法说明" or content == "帮助":
                self.show_help(room_id, user_id)

            elif content == "钓鱼信息":
                self.show_info(room_id, user_id, wxName)

            elif content == "刷新鱼池":
                self.refresh_fish_pool(room_id, user_id)

            elif content == "背包" or content == "3":
                self.show_inventory(room_id, user_id)

            elif content == "鱼市" or content == "限时高价" or content == "4":
                self.show_special_price(room_id, user_id)

            elif content == "鱼池" or content == "5":
                self.show_fish_pool(room_id, user_id)

            elif content == "成就":
                self.show_achievements(room_id, user_id)

            elif content == "排行榜":
                self.show_leaderboard(room_id, user_id)

            elif content == "财富榜":
                self.show_leaderboard(room_id, user_id, "wealth")

            elif content == "等级榜":
                self.show_leaderboard(room_id, user_id, "level")

            elif content == "钓鱼榜":
                self.show_leaderboard(room_id, user_id, "fish_count")

            elif content == "开始比赛" or content == "钓鱼比赛" or content == "6":
                self.start_competition(room_id)

            elif content == "结束比赛":
                self.end_competition(room_id)

            elif content == "参加比赛" or content == "60":
                self.join_competition(room_id, user_id)

            elif content == "比赛排名" or content == "61":
                self.show_competition_rank(room_id, user_id)

            elif content.startswith("使用#"):
                item_name = content.split("#")[1].strip()
                self.use_item(room_id, user_id, item_name)

            elif content == "道具":
                self.show_items(room_id, user_id)

            elif content == "任务" or content == "每日任务":
                self.show_tasks(room_id, user_id)

            elif content == "刷新任务":
                self.refresh_daily_tasks(room_id, user_id)

            elif content == "装饰" or content == "我的鱼塘":
                self.show_decorations(room_id, user_id)

            elif content == "装饰商店":
                self.show_deco_shop(room_id, user_id)

            elif content in deco_buy_map:
                deco_name = deco_buy_map[content]
                self.buy_decoration(room_id, user_id, deco_name)

            elif content in deco_upgrade_map:
                deco_name = deco_upgrade_map[content]
                self.upgrade_decoration(room_id, user_id, deco_name)

            elif content.startswith("升级#"):
                deco_name = content.split("#")[1].strip()
                self.upgrade_decoration(room_id, user_id, deco_name)

            elif content.startswith("参观#"):
                target_id = content.split("#")[1].strip()
                self.visit_pond(room_id, user_id, target_id)

            elif content == "参观" and hasattr(self.wcf, "get_message_at_users"):
                # 处理@方式的参观
                #at_users = self.wcf.get_message_at_users(msg)
                if atWxIdList:
                    self.visit_pond(room_id, user_id, atWxIdList[0])
                else:
                    self._send_message(room_id, "请@要参观的玩家，例如：参观 @玩家")

            elif content == "钓鱼统计":
                self.show_stats(room_id, user_id)

            # 管理员命令
            elif content.startswith("送金币#") and "#" in content:
                parts = content.split("#")
                if len(parts) == 3:
                    target_id = parts[1].strip()
                    try:
                        amount = int(parts[2].strip())
                        self.admin_give_coins(room_id, user_id, target_id, amount)
                    except ValueError:
                        pass

        except Exception as e:
            self._handle_exception(e, "处理消息错误")
            self._send_message(room_id, "处理命令出错，请稍后再试", user_id)

    def start_fishing(self, room_id, user_id,wxName):
        """开始钓鱼"""
        try:
            # 检查是否被禁止
            if self._is_banned(room_id, user_id):
                return

            # 检查频率限制
            allowed, message = self._check_action_limit(room_id, user_id, "fishing")
            if not allowed:
                self._send_message(room_id, message, user_id)
                return

            # 检查冷却时间
            remaining = self._get_remaining_cooldown(room_id, user_id)
            if remaining > 0:
                self._send_message(room_id, f"⏰ 钓鱼冷却中,剩余{remaining}秒", user_id)
                return

            # 获取玩家信息和鱼竿信息
            player = self.db.get_player_info(room_id, user_id)
            if not player:
                player = self.db.create_player(room_id, user_id)

            # 获取玩家昵称
            # nickname = player.get('nickname') or self.wcf.get_alias_in_chatroom(roomid=room_id, wxid=user_id)

            # 更新最后钓鱼时间
            self.db.update_last_fishing(room_id, user_id)

            # 获取鱼竿成功率
            rod_info = self._get_rod_info(player["rod_level"])
            base_success_rate = rod_info["success_rate"] / 100

            # 应用装饰提供的成功率加成
            success_rate = self._apply_buffs(room_id, user_id, base_success_rate, 'success_rate')
            logging.info(f'钓鱼命中率为：{success_rate}')
            # 计算钓鱼结果
            if random.random() < success_rate:
                # 钓鱼成功
                # 稀有度处理
                rarity = self._random_rarity(room_id, user_id)

                # 随机鱼
                catch_info = self._random_fish(rarity)
                if not catch_info:
                    # 出错了，返回默认鱼
                    catch_info = self._get_default_fish(rarity)

                # 检查是否触发特殊事件
                event_result = {
                    "desc": "",
                    "count": 0
                }
                event = self._check_special_events(room_id, user_id)
                if event:
                    event_result = self._handle_special_event(room_id, user_id, event)
                    self.update_stats("special_events")

                # 添加鱼到玩家背包
                fish_name = catch_info.get("name", "未知鱼")
                fish_price = catch_info.get("price", 0)
                count = 1
                if event_result["count"] > 0:
                    count = count + event_result["count"]
                self.db.add_fish_to_pond(room_id, user_id, fish_name, fish_price, count)
                # 从鱼池中减少对应的鱼
                pool_empty = self.db.remove_fish_from_pool(fish_name)

                if pool_empty:
                    # 如果鱼池已空，刷新鱼池
                    self.db.refresh_fish_pool()
                    self._broadcast_message("🎣 鱼池已经被钓空啦！\n🌊 新的鱼群已经来临~")

                # 增加场次
                self.db.update_fishing_stats(room_id, user_id, success=True)

                # 处理钓鱼结果
                self._handle_fishing_result(room_id, user_id, catch_info, event_result,wxName)

                # 更新任务进度
                self.db.update_task_progress(room_id, user_id, "fishing", 1)
                # 使用大小写不敏感的比较来检查稀有度
                rarity_lower = rarity.lower()
                if "稀有" in rarity_lower or "珍稀" in rarity_lower or "传说" in rarity_lower:
                    self.db.update_task_progress(room_id, user_id, "rare_fish", 1)

                    # 更新稀有鱼和传说鱼计数
                    with sqlite3.connect(self.db.db_path) as conn:
                        cursor = conn.cursor()
                        if "传说" in rarity_lower:
                            cursor.execute('''
                                UPDATE fishing_players
                                SET legendary_fish_count = legendary_fish_count + 1
                                WHERE room_id = ? AND user_id = ?
                            ''', (room_id, user_id))
                        if "稀有" in rarity_lower or "珍稀" in rarity_lower:
                            cursor.execute('''
                                UPDATE fishing_players
                                SET rare_fish_count = rare_fish_count + 1
                                WHERE room_id = ? AND user_id = ?
                            ''', (room_id, user_id))

                # 检查成就和称号
                self.db.check_title_upgrade(room_id, user_id)
            else:
                # 钓鱼失败
                self.db.update_fishing_stats(room_id, user_id, success=False)

                fail_msgs = [
                    f"💦 钓了半天，一无所获...",
                    f"💦 钓到了一只破鞋，扔回了水里...",
                    f"💦 感觉有鱼上钩，结果只是海藻...",
                    f"💦 鱼竿一动不动，什么也没钓到...",
                    f"💦 似乎选错了钓点，一条鱼都没看到...",
                    f"💦 你太丑了，鱼全吓跑了...",
                    f"💦 上完厕所不擦屎，鱼都臭晕了..."
                ]
                self._send_message(room_id, random.choice(fail_msgs), user_id)

        except Exception as e:
            self._handle_exception(e, "开始钓鱼错误")

    def _check_special_events(self, room_id, user_id):
        """检查特殊事件"""
        try:
            # 基础触发概率为35%
            base_chance = 0.30

            # 应用装饰提供的特殊事件触发概率加成
            event_chance = self._apply_buffs(room_id, user_id, base_chance, 'event_rate')
            logging.info(f'特殊事件触发概率为：{event_chance}')
            # 随机判断是否触发特殊事件
            if random.random() < event_chance:
                # 获取配置中的特殊事件
                events = self.config.get('events', {})
                if not events:
                    return None

                # 计算权重总和
                weight_sum = sum(event.get('chance', 0.05) for event in events.values())

                # 随机选择一个事件
                rand_val = random.random() * weight_sum
                cumulative = 0

                for event_name, event_data in events.items():
                    cumulative += event_data.get('chance', 0.05)
                    if rand_val <= cumulative:
                        # 构建事件信息
                        event = {
                            'name': event_data.get('name', event_name),
                            'desc': event_data.get('desc', '触发了特殊事件！'),
                            'type': event_name
                        }

                        # 添加特殊事件的特定属性
                        if event_name == 'double_catch':
                            event['duration'] = event_data.get('duration', 300)
                        if event_name == 'supper_attack':
                            event['duration'] = event_data.get('duration', 300)
                        elif event_name == 'fish_swarm':
                            event['count'] = event_data.get('count', 3)
                        elif event_name == 'treasure':
                            reward_type = random.choice(event_data.get('rewards', [{'type': 'coins'}]))
                            event['reward_type'] = reward_type.get('type', 'coins')
                            event['reward_name'] = reward_type.get('name', '金币')
                            event['reward_min'] = reward_type.get('min', 1000)
                            event['reward_max'] = reward_type.get('max', 5000)
                        elif event_name == 'fisher_blessing':
                            event['buff'] = event_data.get('buff', 1.5)
                            event['duration'] = event_data.get('duration', 600)
                        elif event_name == 'golden_rod':
                            event['duration'] = event_data.get('duration', 300)

                        return event

            return None

        except Exception as e:
            self._handle_exception(e, "检查特殊事件错误")
            return None

    def _get_rod_info(self, level):
        # 基础成功率50%，每级提升0.5%，最高80%
        success_rate = min(50 + level * 0.5, 88)

        # 升级费用：基础500，每级增加10%
        base_cost = 500
        if level < 50:
            upgrade_cost = int(base_cost * (1 + level * 0.1))
        elif level <= 99:
            upgrade_cost = int(base_cost * (1 + level * 0.3))
        else:
            upgrade_cost = int(base_cost * (1 + level * (level-85)))

        # 鱼竿等级描述
        rod_names = {
            0: "简易木竿",
            10: "青铜渔竿",
            20: "白银渔竿",
            30: "黄金渔竿",
            40: "铂金渔竿",
            50: "钻石渔竿",
            60: "星辰渔竿",
            70: "神龙渔竿",
            80: "混沌渔竿",
            90: "创世神竿",
            99: "无极仙竿",
            110: "末世神竿"
        }

        # 获取当前等级对应的名称
        current_name = None
        for req_level, name in sorted(rod_names.items(), reverse=True):
            if level >= req_level:
                current_name = name
                break

        return {
            "name": current_name,
            "success_rate": success_rate,
            "upgrade_cost": upgrade_cost,
            "max_level": 120
        }

    def upgrade_rod(self, room_id, user_id):
        """升级鱼竿"""
        try:
            # 获取玩家信息
            player = self.db.get_player_info(room_id, user_id)
            if not player:
                self._send_message(room_id, "获取玩家信息失败", user_id)
                return

            # 获取当前鱼竿信息
            current_rod = self._get_rod_info(player['rod_level'])

            # 检查是否达到最高等级
            if player['rod_level'] >= current_rod['max_level']:
                self._send_message(room_id, "你的鱼竿已经达到最高等级啦！", user_id)
                return

            # 获取下一级信息
            next_rod = self._get_rod_info(player['rod_level'] + 1)

            # 检查金币是否足够
            if player['coins'] < next_rod['upgrade_cost']:
                fail_messages = [
                    f"😅 钱包不够鼓，升级失败！\n需要{next_rod['upgrade_cost']}金币\n当前金币：{int(player['coins'])}",
                    f"💸 穷鬼，去多钓点鱼吧！\n需要{next_rod['upgrade_cost']}金币\n当前金币：{int(player['coins'])}",
                    f"🎣 这根鱼竿太贵啦！\n需要{next_rod['upgrade_cost']}金币\n当前金币：{int(player['coins'])}",
                    f"😢 金币不足，继续努力吧！\n需要{next_rod['upgrade_cost']}金币\n当前金币：{int(player['coins'])}",
                    f"🌟 攒够{next_rod['upgrade_cost']}金币再来升级吧！\n当前金币：{int(player['coins'])}"
                ]
                self._send_message(room_id, random.choice(fail_messages), user_id)
                return

            # 执行升级
            if self.db.upgrade_rod(room_id, user_id, next_rod['upgrade_cost']):
                # 清理玩家缓存，确保金币数更新
                self._invalidate_player_cache(room_id, user_id)

                msg = (
                    "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                    "🎊 鱼竿升级成功！\n\n"
                    f"🎣 {current_rod['name']} → {next_rod['name']}\n"
                    f"💰 花费: {next_rod['upgrade_cost']}金币\n"
                    f"📈 钓鱼成功率: {current_rod['success_rate']}% → {next_rod['success_rate']}%\n"
                    f"⭐ 当前等级: {player['rod_level'] + 1}/{current_rod['max_level']}\n"
                    "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                )
                # 更新升级任务进度
                self.db.update_task_progress(room_id, user_id, "upgrade", 1)
                self._send_message(room_id, msg, user_id)
            else:
                self._send_message(room_id, "升级失败，你没钱了吧？", user_id)

        except Exception as e:
            self._handle_exception(e, "升级鱼竿错误")

    def _get_dog_info(self, level):
        """获取旺财信息"""
        # 基础防盗率20%，每级提升0.15%，最高60%
        anti_steal_rate = min(20 + level * 0.15, 60)

        # 升级费用：基础2000，每级增加15%
        base_cost = 2000
        if level <= 99:
            upgrade_cost = int(base_cost * (1 + level * 3))
        else:
            upgrade_cost = int(base_cost * (1 + level * (level-92)))

        # 旺财等级描述
        dog_names = {
            0: "见习旺财",
            10: "警卫旺财",
            20: "巡逻旺财",
            30: "守护旺财",
            40: "精英旺财",
            50: "统领旺财",
            60: "将军旺财",
            70: "护法旺财",
            80: "神威旺财",
            90: "天尊旺财",
            99: "仙宠旺财",
            100: "紫麒麟",
            110: "神兽"
        }

        # 获取当前等级对应的名称
        current_name = None
        for req_level, name in sorted(dog_names.items(), reverse=True):
            if level >= req_level:
                current_name = name
                break

        return {
            "name": current_name,
            "anti_steal_rate": anti_steal_rate,
            "upgrade_cost": upgrade_cost,
            "max_level": 120
        }

    def upgrade_dog(self, room_id, user_id):
        """升级旺财"""
        try:
            # 获取玩家信息
            player = self.db.get_player_info(room_id, user_id)
            if not player:
                self._send_message(room_id, "获取玩家信息失败", user_id)
                return

            # 检查是否已经雇佣旺财
            if player['dog_level'] == 0 and player['dog_status'] == 0:
                self._send_message(room_id, "你还没有雇佣旺财哦，发送【确认雇佣】来雇佣旺财吧！", user_id)
                return

            # 获取当前旺财信息
            current_dog = self._get_dog_info(player['dog_level'])

            # 检查是否达到最高等级
            if player['dog_level'] >= current_dog['max_level']:
                self._send_message(room_id, "旺财已经达到最高等级啦！", user_id)
                return

            # 获取下一级信息
            next_dog = self._get_dog_info(player['dog_level'] + 1)

            # 检查金币是否足够
            if player['coins'] < next_dog['upgrade_cost']:
                fail_messages = [
                    f"😅 钱包不够鼓，升级失败！\n需要{next_dog['upgrade_cost']}金币\n当前金币：{player['coins']}",
                    f"🐕 旺财表示很失望！\n需要{next_dog['upgrade_cost']}金币\n当前金币：{player['coins']}",
                    f"🎭 穷成这样还想升级？\n需要{next_dog['upgrade_cost']}金币\n当前金币：{player['coins']}",
                    f"😢 金币不足，继续努力吧！\n需要{next_dog['upgrade_cost']}金币\n当前金币：{player['coins']}",
                    f"🌟 攒够{next_dog['upgrade_cost']}金币再来升级吧！\n当前金币：{player['coins']}"
                ]
                self._send_message(room_id, random.choice(fail_messages), user_id)
                return

            # 执行升级
            if self.db.upgrade_dog(room_id, user_id, next_dog['upgrade_cost']):
                # 清理玩家缓存，确保金币数更新
                self._invalidate_player_cache(room_id, user_id)

                msg = (
                    "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                    "🎊 旺财升级成功！\n\n"
                    f"🐕 {current_dog['name']} → {next_dog['name']}\n"
                    f"💰 花费: {next_dog['upgrade_cost']}金币\n"
                    f"🛡️ 防盗成功率: {current_dog['anti_steal_rate']:.1f}% → {next_dog['anti_steal_rate']:.1f}%\n"
                    f"⭐ 当前等级: {player['dog_level'] + 1}/{current_dog['max_level']}\n"
                    "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                )
                # 更新升级任务进度
                self.db.update_task_progress(room_id, user_id, "upgrade", 1)
                self._send_message(room_id, msg, user_id)
            else:
                self._send_message(room_id, "升级失败，你没钱了吧？", user_id)

        except Exception as e:
            self._handle_exception(e, "升级旺财错误")

    def upgrade_decoration(self, room_id, user_id, deco_name):
        """升级装饰"""
        try:
            # 获取玩家信息和装饰信息
            player = self.db.get_player_info(room_id, user_id)
            deco = self.db.get_decoration(room_id, user_id, deco_name)

            if not player or not deco:
                self._send_message(room_id, "你还没有这个装饰哦~", user_id)
                return

            # 新的升级费用公式：基础价格 * (1.3 ^ 当前等级)
            # 相比原来的1.5次方，这个曲线更平缓
            upgrade_cost = int(deco["price"] * (1.3 ** deco["level"]))

            # 检查金币是否足够
            if player['coins'] < upgrade_cost:
                fail_messages = [
                    f"😅 钱包不够鼓，升级失败！\n需要{upgrade_cost}金币\n当前金币：{player['coins']}",
                    f"🏰 装饰太贵啦！\n需要{upgrade_cost}金币\n当前金币：{player['coins']}",
                    f"💫 先去多钓点鱼吧！\n需要{upgrade_cost}金币\n当前金币：{player['coins']}",
                    f"😢 金币不足，继续努力吧！\n需要{upgrade_cost}金币\n当前金币：{player['coins']}",
                    f"🌟 攒够{upgrade_cost}金币再来升级吧！\n当前金币：{player['coins']}"
                ]
                self._send_message(room_id, random.choice(fail_messages), user_id)
                return

            # 计算新的buff值（递减回报）
            # 不同装饰有不同的增长幅度
            if deco_name == "锦鲤池":
                buff_increase = 0.01  # 经验加成每级+1%
            elif deco_name == "珊瑚礁":
                buff_increase = 0.02  # 稀有概率每级+2%
            elif deco_name == "防盗门":
                buff_increase = 0.01  # 防盗能力每级+1%
            elif deco_name == "幸运喷泉":
                buff_increase = 0.01  # 收入加成每级+1%
            elif deco_name == "黄金地板":
                buff_increase = 0.005  # 全局加成每级+0.5%
            else:
                buff_increase = 0.01  # 默认加成

            # 计算新的buff值
            new_buff_value = deco['buff_value'] + buff_increase

            # 执行升级
            if self.db.upgrade_decoration(room_id, user_id, deco_name, upgrade_cost, new_buff_value):
                # 清理玩家缓存，确保金币数更新
                self._invalidate_player_cache(room_id, user_id)

                # 获取buff类型的中文名称
                buff_type_names = {
                    "exp_rate": "经验获取",
                    "rare_rate": "稀有鱼概率",
                    "anti_steal": "防偷鱼几率",
                    "income_rate": "金币收入",
                    "all_rate": "全属性",
                    "event_rate": "特殊事件概率",
                    "success_rate": "钓鱼成功率"
                }

                buff_desc = buff_type_names.get(deco['buff_type'], deco['buff_type'])

                msg = (
                    "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                    f"🎊 {deco_name} 升级成功!\n\n"
                    f"📈 等级: {deco['level']} → {deco['level'] + 1}\n"
                    f"💰 花费: {upgrade_cost}金币\n"
                    f"💫 加成: +{int(deco['buff_value'] * 100)}% → "
                    f"+{int(new_buff_value * 100)}%\n"
                    f"🌟 效果: {buff_desc}增加了{buff_increase * 100}%\n"
                    "╰┄··┄┄┄┄┄┄┄┄┄┄╯"
                )
                self._send_message(room_id, msg, user_id)
            else:
                self._send_message(room_id, "升级失败，请稍后再试", user_id)
        except Exception as e:
            self._handle_exception(e, "升级装饰错误")

    def show_inventory(self, room_id, user_id):
        """显示背包信息"""
        try:
            # 尝试从缓存获取背包信息
            cache_key = f"{room_id}_{user_id}_inventory"
            inventory = self.inventory_cache.get(cache_key)

            if inventory is None:
                # 缓存未命中，从数据库获取
                inventory = self.db.get_inventory(room_id, user_id)
                # 更新缓存
                if inventory:
                    self.inventory_cache.set(cache_key, inventory)

            if not inventory:
                self._send_message(room_id, "你的背包是空的,快去钓鱼吧~", user_id)
                return

            msg = "🎒 背包信息 🎒\n\n"
            for item in inventory:
                msg += f"【{item['name']}】x{item['count']}\n"

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "显示背包错误")

    def show_help(self, room_id, user_id, help_type="general"):
        """显示帮助信息"""
        try:
            # 基础帮助
            if help_type == "general":
                msg = (
                    "🎣 钓鱼小游戏帮助 🎣\n\n"
                    "🎮 基础命令：\n"
                    "• 钓鱼 - 开始钓鱼\n"
                    "• 鱼塘 - 查看已捕获的鱼\n"
                    "• 卖鱼+名称 - 卖出指定的鱼\n"
                    "• 卖全部 - 卖出所有的鱼\n"
                    "• 卖高价 - 卖出当前特价高价鱼\n"
                    "• 我的信息 - 查看个人信息\n"
                    "• 升级鱼竿 - 提高钓鱼成功率\n"
                    "• 找狗 - 寻找丢失的捕鱼犬\n"
                    "• 升级狗 - 提高捕鱼犬等级\n\n"

                    "🏆 进阶功能：\n"
                    "• 排行榜 - 查看财富排行\n"
                    "• 成就 - 查看可获得的成就\n"
                    "• 特价鱼 - 查看当前特价鱼\n"
                    "• 偷鱼@xxx - 偷取他人的鱼\n"
                    "• 装饰商店 - 查看可购买的装饰\n"
                    "• 我的装饰 - 查看已购买的装饰\n"
                    "• 买装饰+名称 - 购买装饰\n"
                    "• 升级装饰+名称 - 升级装饰\n"
                    "• 任务 - 查看每日任务\n\n"

                    "🔍 更多帮助：\n"
                    "• 发送「帮助装饰」查看装饰系统详情\n"
                    "• 发送「帮助升级」查看升级系统详情\n"
                    "• 发送「帮助任务」查看任务系统详情\n"
                )
            # 装饰系统帮助
            elif help_type == "decoration" or help_type == "decorations" or help_type == "装饰":
                msg = (
                    "🏡 装饰系统帮助 🏡\n\n"
                    "💡 装饰可以提供各种游戏加成效果：\n"
                    "• 锦鲤池 (8,000金币) - 提升经验获取 +12%\n"
                    "• 珊瑚礁 (12,000金币) - 提升稀有鱼概率 +8%\n"
                    "• 防盗门 (18,000金币) - 降低被偷鱼概率 +15%\n"
                    "• 幸运喷泉 (25,000金币) - 提升卖鱼收入 +12%\n"
                    "• 黄金地板 (40,000金币) - 提升所有属性 +7%\n\n"

                    "💫 装饰升级说明：\n"
                    "• 每次升级都会提升装饰的加成效果\n"
                    "• 升级成本：基础价格 × (1.3 ^ 当前等级)\n"
                    "• 升级加成：不同装饰有不同加成幅度\n"
                    "  - 锦鲤池：每级+8%\n"
                    "  - 珊瑚礁：每级+6%\n"
                    "  - 防盗门：每级+7%\n"
                    "  - 幸运喷泉：每级+8%\n"
                    "  - 黄金地板：每级+5%\n\n"

                    "🔸 推荐购买顺序：\n"
                    "1. 初级玩家(1-15级)：锦鲤池、珊瑚礁\n"
                    "2. 中级玩家(16-30级)：防盗门、幸运喷泉\n"
                    "3. 高级玩家(31级+)：黄金地板\n\n"

                    "💡 装饰效果已全面应用于：\n"
                    "• 钓鱼成功率\n"
                    "• 钓鱼经验获取\n"
                    "• 卖鱼收入加成\n"
                    "• 特殊事件触发\n"
                    "• 偷鱼防御几率\n"
                )
            # 升级系统帮助
            elif help_type == "upgrade" or help_type == "升级":
                msg = (
                    "📈 升级系统帮助 📈\n\n"
                    "🎣 鱼竿升级：\n"
                    "• 提升钓鱼成功率，基础30%，每级+2%\n"
                    "• 升级成本：500 × (2 ^ 当前等级)\n"
                    "• 最高成功率可达90%\n\n"

                    "🐕 捕鱼犬升级：\n"
                    "• 提升偷鱼成功率，基础40%，每级+3%\n"
                    "• 升级成本：2000 × (1.5 ^ 当前等级)\n"
                    "• 最高成功率可达80%\n\n"

                    "🏠 装饰升级：\n"
                    "• 提升相应属性加成\n"
                    "• 升级成本：基础价格 × (1.3 ^ 当前等级)\n"
                    "• 不同装饰有不同加成幅度\n\n"

                    "👤 角色升级：\n"
                    "• 通过获取经验提升等级\n"
                    "• 每个等级需要的经验：100 × (当前等级 ^ 1.5)\n"
                    "• 升级会获得金币奖励\n"
                )
            # 任务系统帮助
            elif help_type == "task" or help_type == "tasks" or help_type == "任务":
                msg = (
                    "📝 任务系统帮助 📝\n\n"
                    "🔄 每日任务：\n"
                    "• 每天自动刷新3个随机任务\n"
                    "• 完成任务可获得金币和经验奖励\n"
                    "• 发送「刷新任务」可手动刷新（消耗金币）\n\n"

                    "📊 任务类型：\n"
                    "• 钓鱼任务 - 完成指定次数的钓鱼\n"
                    "• 卖鱼任务 - 卖出指定数量的鱼\n"
                    "• 升级任务 - 完成指定次数的升级\n"
                    "• 稀有鱼任务 - 钓到指定数量的稀有鱼\n"
                    "• 收入任务 - 获得指定数量的金币\n\n"

                    "💰 奖励说明：\n"
                    "• 奖励与任务难度和玩家等级相关\n"
                    "• 金币奖励：500-5000不等\n"
                    "• 经验奖励：50-300不等\n"
                )
            else:
                msg = "没有找到相关帮助信息，请发送「帮助」查看基本指令。"

            self._send_message(room_id, msg, user_id)
        except Exception as e:
            self._handle_exception(e, "显示帮助出错")

    def _send_message(self, room_id, msg, at_user=None):
        """发送消息"""
        try:
            # 获取用户别名（如果指定了at_user）
            alias = None
            #if at_user:
                #alias = self.wcf.get_alias_in_chatroom(roomid=room_id, wxid=at_user)
                #alias = self.wcf.getGroupUerInfo(robotId='wxid_cbgpauriedg629', roomId=room_id, wxId=at_user)['nickname']

            # 处理消息格式
            # 1. 移除消息中可能已有的边框
            if msg.startswith("╭"):
                lines = msg.split("\n")
                if len(lines) > 2 and lines[-1].startswith("╰"):
                    msg = "\n".join(lines[1:-1])

            # 2. 确保每行内容以emoji开头（除了可能的子列表）
            formatted_lines = []
            for line in msg.split("\n"):
                # 跳过空行
                if not line.strip():
                    formatted_lines.append(line)
                    continue

                # 如果是子列表项（以空格开头），保持原样
                if line.startswith(" ") or line.startswith("├ ") or line.startswith("  ") or line.startswith("\t") or line.startswith("•") or line.startswith("-"):
                    formatted_lines.append(line)
                # 如果行已经以emoji开头，保持原样
                elif any(line.startswith(emoji) for emoji in
                         ["❌","🌱","⚪", "🔵", "🟣", "🟡", "💎", "💦", "🎣", "🐟", "💰", "🚫", "⚠️", "⏰", "📊", "🎮", "🎯", "🏆", "💫", "✨", "🐕", "🌟", "🔍", "📈", "🎁",
                          "💡", "🎋", "🏡", "🌊", "🎊", "📋", "⭐", "🐠", "🐡", "🐋", "🏷️", "⚖️", "📍","🥇","🥈","🥉","🐶","🛡️","📢","⌛","🔥","🎒"]):
                    formatted_lines.append(line)
                # 否则，添加一个通用emoji
                else:
                    formatted_lines.append(f"📌 {line}")

            # 3. 重新组合消息，添加边框
            formatted_msg = "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n" + "\n".join(formatted_lines) + "\n╰┄┄┄┄┄┄┄┄┄┄··┄╯"

            # 4. 如果指定了at_user，在消息开头添加@
            #if alias:
                #formatted_msg = f"@{alias}\n{formatted_msg}"

            #self.wcf.send_text(msg=formatted_msg,receiver=room_id,aters = at_user)
            self.wcf.sendText(robotId='wxid_cbgpauriedg629', receive=room_id, message=formatted_msg, aters=f'{at_user}')

        except Exception as e:
            self._handle_exception(e, "发送消息错误")

    def show_info(self, room_id, user_id, wxName):
        """显示玩家信息"""
        try:
            # 获取玩家信息
            player = self.db.get_player_info(room_id, user_id)
            if not player:
                player = self.db.create_player(room_id, user_id)

            # 获取玩家昵称
            #nickname = player.get('nickname', self.wcf.get_alias_in_chatroom(roomid=room_id, wxid=user_id))
            nickname = wxName
            # 构建消息
            msg = (
                f"🏆 【{nickname}的钓手信息】 🏆\n\n"
                f"💫 等级: {player['level']} 级\n"
                f"✨ 经验: {int(player['exp'])}/{self._calculate_next_level_exp(player['level'])}\n"
                f"💰 金币: {int(player['coins'])}\n"
                f"🎣 鱼竿等级: {player['rod_level']} 级\n"
                f"🐕 捕鱼犬等级: {player['dog_level'] if player['dog_status'] == 1 else '未拥有'}\n\n"
                f"📊 钓鱼统计:\n"
                f"• 成功次数: {player['success_count']}\n"
                f"• 失败次数: {player['fail_count']}\n"
                f"• 成功率: {int(player['success_count'] / (player['success_count'] + player['fail_count'] or 1) * 100)}%\n\n"
                f"🎯 稀有鱼捕获: {player['rare_fish_count']} 条\n"
                f"🌟 传说鱼捕获: {player['legendary_fish_count']} 条\n"
            )

            # 增加系统更新提示 (只在2023年12月15日后的一周内显示)
            current_date = datetime.now()
            if current_date >= datetime(2023, 12, 15) and current_date <= datetime(2023, 12, 22):
                msg += (
                    "\n🔔 系统公告:\n"
                    "游戏经济系统已全面更新！装饰效果现在应用于所有游戏环节。\n"
                    "查看「帮助装饰」了解详情。\n"
                )

            self._send_message(room_id, msg, user_id)
        except Exception as e:
            self._handle_exception(e, "显示玩家信息错误")

    def refresh_fish_pool(self, room_id, user_id):
        """刷新鱼池"""
        try:
            self.db.refresh_fish_pool()
            self._send_message(room_id, "🎣 鱼池已刷新!")
        except Exception as e:
            self._handle_exception(e, "刷新鱼池错误")
            self._send_message(room_id, "刷新鱼池失败,请稍后再试")

    def _random_fish(self, rarity):
        """根据稀有度随机生成鱼"""
        try:
            # 获取当前鱼池
            pool_info = self.db.get_fish_pool()
            if not pool_info or not pool_info["fish_list"]:
                # 如果鱼池为空,刷新鱼池
                self.db.refresh_fish_pool()
                pool_info = self.db.get_fish_pool()
                if not pool_info:
                    return None

            # 按稀有度过滤当前鱼池的鱼
            rarity_fish = []
            for fish in pool_info["fish_list"]:
                # 只选择还有剩余数量的鱼
                if fish["count"] <= 0:
                    continue

                # 根据价格判断稀有度
                if (rarity == "普通" and fish["price"] < 1000) or \
                        (rarity == "稀有" and 1000 <= fish["price"] < 5000) or \
                        (rarity == "珍稀" and 5000 <= fish["price"] < 20000) or \
                        (rarity == "传说" and fish["price"] >= 20000):
                    fish["rarity"] = rarity  # 添加稀有度信息
                    rarity_fish.append(fish)

            if not rarity_fish:
                # 如果该稀有度没有鱼，从剩余的鱼中随机选择一条
                available_fish = []
                for fish in pool_info["fish_list"]:
                    if fish["count"] > 0:
                        # 判断稀有度并添加
                        if fish["price"] < 1000:
                            fish["rarity"] = "普通"
                        elif 1000 <= fish["price"] < 5000:
                            fish["rarity"] = "稀有"
                        elif 5000 <= fish["price"] < 20000:
                            fish["rarity"] = "珍稀"
                        else:
                            fish["rarity"] = "传说"
                        available_fish.append(fish)

                if available_fish:
                    return random.choice(available_fish)
                else:
                    # 如果鱼池已空，刷新鱼池
                    self.db.refresh_fish_pool()
                    return self._random_fish(rarity)

            return random.choice(rarity_fish)

        except Exception as e:
            self._handle_exception(e, "随机选择鱼错误")
            # 如果出错，重新刷新鱼池并尝试
            self.db.refresh_fish_pool()
            return self._random_fish(rarity)

    def show_pond(self, room_id, user_id):
        """显示鱼塘信息"""
        try:
            # 尝试从缓存获取鱼塘信息
            cache_key = f"{room_id}_{user_id}_pond"
            fish_list = self.fish_pond_cache.get(cache_key)

            if fish_list is None:
                # 缓存未命中，从数据库获取
                # 使用连接池获取鱼塘信息
                fish_list = self.db.get_fish_pond_with_pool(room_id, user_id)
                # 更新缓存
                if fish_list:
                    self.fish_pond_cache.set(cache_key, fish_list)

            if not fish_list:
                self._send_message(room_id, "你的鱼塘还是空的,快去钓鱼吧~", user_id)
                return

            total_value = sum(fish['price'] * fish['count'] for fish in fish_list)

            msg = (
                "🌊 我的鱼塘 🐟\n\n"
            )

            for fish in fish_list:
                msg += (
                    f"【{fish['name']}】x{fish['count']}\n"
                    f"💰 单价：{fish['price']}金币\n\n"
                )

            msg += (
                f"📊 总计：{len(fish_list)}种鱼\n"
                f"💎 总价值：{total_value}金币\n\n"
                "💡 发送【卖鱼#鱼名】出售指定鱼\n"
                "💡 发送【一键卖鱼】全部出售"
            )

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "显示鱼塘错误")

    def refresh_special_price(self):
        """刷新限时高价"""
        try:
            # 直接调用数据库的刷新方法
            self.db.refresh_special_prices()

            # 获取当前特价鱼信息
            special_fish = self.db.get_special_prices()

            if not special_fish or len(special_fish) == 0:
                logging.warning("刷新限时高价后没有获取到特价鱼信息")
                return

            # 从配置文件获取刷新间隔，默认为1800秒（30分钟）
            refresh_interval = self.market_refresh_interval  # 使用加载配置时保存的值

            # 构建消息
            msg = (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                "限时高价收购\n\n"
            )

            for fish in special_fish:
                msg += f"【{fish['name']}】￥{fish['special_price']}\n"

            msg += f"\n本轮剩余时间：{refresh_interval}秒\n"
            msg += "麦掉就是赚到,冲冲冲！\n"
            msg += f"每隔{refresh_interval // 60}分钟刷新鱼市！\n"
            msg += "╰┄┄┄┄┄┄┄┄┄┄··┄╯"

            # 广播到所有房间
            self._broadcast_message(msg)

        except Exception as e:
            logging.error(f"刷新限时高价错误: {e}")
            logging.error(traceback.format_exc())

    def sell_fish(self, room_id, user_id, fish_name):
        """卖出指定鱼"""
        try:
            # 获取鱼的信息
            result = self.db.sell_fish(room_id, user_id, fish_name)
            if not result["success"]:
                self._send_message(room_id, result["message"], user_id)
                return

            # 应用装饰提供的收入加成
            income_multiplier = self._apply_buffs(room_id, user_id,1.0, 'income_rate')

            # 计算基础价值和养殖加成
            base_price = result['price']
            special_price = result.get('special_price')
            fish_count = result['count']
            catch_time = result.get('catch_time')

            # 计算养殖加成
            farming_bonus = 0
            if catch_time:
                farming_bonus = self._calculate_farming_bonus(catch_time, base_price)

            # 计算最终价格（包含特价、养殖加成和装饰加成）
            sell_price = special_price if special_price else base_price
            base_income = fish_count * sell_price
            farming_income = fish_count * farming_bonus if farming_bonus > 0 else 0
            
            # 应用收入加成
            final_income = int((base_income + farming_income) * income_multiplier)
            income_bonus = int((base_income + farming_income) * (income_multiplier - 1.0)) if income_multiplier > 1.0 else 0

            # 如果有收入加成，添加到玩家金币中
            if income_bonus > 0:
                self.db.add_coins(room_id, user_id, income_bonus)
                # 更新比赛分数
                self.db.update_competition_score(room_id, user_id, income_bonus)

            # 构建消息
            msg = (
                f"🐠 成功卖出 {fish_name} × {fish_count}！\n"
                f"💰 总收入: {final_income} 金币"
            )

            # 添加价格明细
            msg += f"\n📊 价格明细:"

            # 添加特价信息
            if special_price:
                price_diff = special_price - base_price
                price_percent = int((price_diff / base_price) * 100)
                msg += f"\n ├ 高价：{special_price}金币(+{price_percent}%)"
            else:
                msg += f"\n ├ 价格: {base_price}金币"

            # 添加养殖奖励信息
            if farming_bonus > 0:
                farming_percent = int((farming_bonus / base_price) * 100)
                msg += f"\n ├ 养殖: +{farming_income}金币(+{farming_percent}%)"

            # 添加收入加成信息
            if income_multiplier > 1.0:
                msg += f"\n └ 装饰加成: +{income_bonus}金币({int((income_multiplier - 1) * 100)}%)"
            else:
                msg += f"\n └ 装饰加成: 无"

            # 发送消息
            self._send_message(room_id, msg, user_id)

            # 清理玩家缓存
            self._invalidate_player_cache(room_id, user_id)

            # 更新任务进度
            self.db.update_task_progress(room_id, user_id, "sell", 1)
            self.db.update_task_progress(room_id, user_id, "income", final_income)

        except Exception as e:
            self._handle_exception(e, "卖鱼错误")
            self._send_message(room_id, "卖鱼失败，请稍后再试", user_id)

    def sell_all_fish(self, room_id, user_id):
        """一键卖出所有鱼"""
        try:
            result = self.db.sell_all_fish(room_id, user_id)
            if not result["success"]:
                self._send_message(room_id, result["message"], user_id)
                return

            # 应用装饰提供的收入加成
            income_multiplier = self._apply_buffs(room_id, user_id,1.0, 'income_rate')

            # 计算每条鱼的养殖加成和最终价值
            total_base_income = 0
            total_farming_bonus = 0
            fish_summary = []

            for fish in result["fish_details"]:
                # 获取基础信息
                base_price = fish["price"]
                fish_count = fish["count"]
                catch_time = fish.get("catch_time")
                is_special = fish.get("is_special", False)

                # 计算养殖加成
                farming_bonus = 0
                if catch_time:
                    farming_bonus = self._calculate_farming_bonus(catch_time, base_price)
                
                # 计算这条鱼的总收入
                base_income = fish_count * base_price
                farming_income = fish_count * farming_bonus if farming_bonus > 0 else 0

                total_base_income += base_income
                total_farming_bonus += farming_income

                # 添加到鱼类汇总
                fish_summary.append({
                    "name": fish["name"],
                    "count": fish_count,
                    "base_price": base_price,
                    "farming_bonus": farming_bonus,
                    "is_special": is_special
                })

            # 计算最终收入（应用装饰加成）
            subtotal = total_base_income + total_farming_bonus
            income_bonus = int(subtotal * (income_multiplier - 1.0)) if income_multiplier > 1.0 else 0
            final_income = int(subtotal * income_multiplier)

            # 如果有收入加成，添加到玩家金币中
            if income_bonus > 0:
                self.db.add_coins(room_id, user_id, income_bonus)
                # 更新比赛分数
                self.db.update_competition_score(room_id, user_id, income_bonus)

            # 构建消息
            msg = (
                f"🎣 成功卖出所有鱼！\n"
                f"📦 总数量：{result['count']}条\n"
                f"💰 总收入：{final_income}金币\n\n"
                "📊 详细信息："
            )

            # 添加每种鱼的详细信息
            for fish in fish_summary:
                msg += f"\n【{fish['name']}】x{fish['count']}"
                if fish["is_special"]:
                    msg += "（限时高价）"
                if fish["farming_bonus"] > 0:
                    bonus_percent = int((fish["farming_bonus"] / fish["base_price"]) * 100)
                    msg += f"\n养殖：+{bonus_percent}%"

            # 添加总体加成信息
            if total_farming_bonus > 0:
                farming_percent = int((total_farming_bonus / total_base_income) * 100)
                msg += f"\n\n🌱 总养殖：+{total_farming_bonus}金币(+{farming_percent}%)"

            if income_multiplier > 1.0:
                msg += f"\n💫 装饰加成：+{income_bonus}金币({int((income_multiplier - 1) * 100)}%)"

            # 发送消息
            self._send_message(room_id, msg, user_id)

            # 清理玩家缓存
            self._invalidate_player_cache(room_id, user_id)

            # 更新任务进度
            self.db.update_task_progress(room_id, user_id, "sell", result["count"])
            self.db.update_task_progress(room_id, user_id, "income", final_income)

        except Exception as e:
            self._handle_exception(e, "一键卖鱼错误")
            self._send_message(room_id, "卖鱼失败，请稍后再试", user_id)

    def _get_default_fish(self, rarity):
        """获取默认鱼类"""
        try:
            default_fish = {
                "legendary": {
                    "name": "深海魔鲸",
                    "price": 45000,
                    "weight": random.randint(800, 1000),
                    "rarity": "legendary"
                },
                "epic": {
                    "name": "帝王蟹",
                    "price": 15000,
                    "weight": random.randint(500, 800),
                    "rarity": "epic"
                },
                "rare": {
                    "name": "河豚宝宝",
                    "price": 1999,
                    "weight": random.randint(300, 500),
                    "rarity": "rare"
                },
                "uncommon": {
                    "name": "金丝鱼",
                    "price": 800,
                    "weight": random.randint(100, 300),
                    "rarity": "uncommon"
                },
                "common": {
                    "name": "鲫鱼",
                    "price": 100,
                    "weight": random.randint(50, 100),
                    "rarity": "common"
                }
            }
            return default_fish.get(rarity, default_fish["common"])
        except Exception as e:
            self._handle_exception(e, "获取默认鱼类错误")
            return {
                "name": "小鱼干",
                "price": 50,
                "weight": random.randint(10, 30),
                "rarity": "common"
            }

    def _init_timers(self):
        """初始化定时器"""
        try:
            # 使用配置文件中的刷新间隔
            pool_interval = self.pool_refresh_interval
            market_interval = self.market_refresh_interval

            logging.info(f"初始化定时器 - 鱼池刷新间隔: {pool_interval}秒, 市场刷新间隔: {market_interval}秒")

            # 刷新鱼池的定时器
            def refresh_fish_pool_timer():
                while True:
                    try:
                        time.sleep(pool_interval)
                        self.db.refresh_fish_pool()
                        logging.info(f"定时刷新鱼池成功，下次刷新时间: {pool_interval}秒后")
                    except Exception as e:
                        logging.error(f"刷新鱼池定时器错误: {e}")
                        time.sleep(60)  # 出错后等待1分钟再试

            # 刷新限时高价的定时器
            def refresh_special_price_timer():
                while True:
                    try:
                        self.refresh_special_price()
                        logging.info(f"定时刷新限时高价成功，下次刷新时间: {market_interval}秒后")
                        time.sleep(market_interval)
                    except Exception as e:
                        logging.error(f"刷新限时高价定时器错误: {e}")
                        time.sleep(60)  # 出错后等待1分钟再试

            # 清理操作记录的定时器（每5分钟执行一次）
            def cleanup_timer():
                while True:
                    try:
                        time.sleep(300)  # 5分钟
                        self._cleanup_action_records()
                    except Exception as e:
                        logging.error(f"清理定时器错误: {e}")
                        time.sleep(60)  # 出错后等待1分钟再试

            # 启动定时器线程
            threading.Thread(target=refresh_fish_pool_timer, daemon=True).start()
            threading.Thread(target=refresh_special_price_timer, daemon=True).start()
            threading.Thread(target=cleanup_timer, daemon=True).start()

            # 初始化比赛定时器
            self._init_competition_timer()

            logging.info("定时器初始化完成")

        except Exception as e:
            logging.error(f"初始化定时器错误: {e}")
            self._handle_exception(e)

    def _cleanup_action_records(self):
        """清理过期的操作记录和冻结状态"""
        try:
            current_time = time.time()

            # 清理过期的操作记录（保留最近10分钟的记录）
            expired_actions = []
            for action_key, timestamp in self.user_last_actions.items():
                if current_time - timestamp > 600:  # 10分钟
                    expired_actions.append(action_key)

            for key in expired_actions:
                del self.user_last_actions[key]

            # 清理过期的冻结状态
            expired_freezes = []
            for freeze_key, freeze_info in self.user_freeze_status.items():
                if current_time >= freeze_info["until"]:
                    expired_freezes.append(freeze_key)

            for key in expired_freezes:
                del self.user_freeze_status[key]

            # 清理cooldown_locks（保留最近5分钟的记录）
            if hasattr(self, 'cooldown_locks'):
                expired_locks = []
                for lock_key, lock_time in self.cooldown_locks.items():
                    if current_time - lock_time > 300:  # 5分钟
                        expired_locks.append(lock_key)

                for key in expired_locks:
                    self.cooldown_locks.pop(key, None)

            # 清理其他可能的内存结构
            if hasattr(self, 'action_records'):
                expired_records = []
                for record_key, timestamps in self.action_records.items():
                    # action_records中存储的是时间戳列表，检查是否所有时间戳都已过期
                    if not timestamps or (isinstance(timestamps, list) and
                                          all(current_time - t > 1800 for t in timestamps)):  # 30分钟
                        expired_records.append(record_key)

                for key in expired_records:
                    self.action_records.pop(key, None)

            # 清理banned_users中已过期的封禁
            if hasattr(self, 'banned_users'):
                expired_bans = []
                for ban_key, ban_info in self.banned_users.items():
                    if current_time >= ban_info.get("until", 0):
                        expired_bans.append(ban_key)

                for key in expired_bans:
                    self.banned_users.pop(key, None)

            # 清理并修复潜在的旺财状态不一致问题
            try:
                with sqlite3.connect(self.db.db_path) as conn:
                    cursor = conn.cursor()
                    # 检查dog_level > 0但dog_status = 0的情况
                    cursor.execute('''
                        SELECT room_id, user_id 
                        FROM fishing_players
                        WHERE dog_level > 0 AND dog_status = 0
                    ''')

                    inconsistent_dogs = cursor.fetchall()
                    fixed_count = 0

                    for room_id, user_id in inconsistent_dogs:
                        # 修复状态
                        cursor.execute('''
                            UPDATE fishing_players
                            SET dog_status = 1
                            WHERE room_id = ? AND user_id = ? AND dog_level > 0
                        ''', (room_id, user_id))
                        fixed_count += cursor.rowcount

                    if fixed_count > 0:
                        conn.commit()
                        logging.info(f"旺财状态自动修复: 修复了 {fixed_count} 条不一致记录")
            except Exception as e:
                logging.error(f"旺财状态自动修复失败: {e}")

            logging.info(
                f"清理完成: 删除了 {len(expired_actions)} 条过期操作记录, {len(expired_freezes)} 条过期冻结状态")

        except Exception as e:
            self._handle_exception(e, "清理操作记录错误")

    def _handle_special_event(self, room_id, user_id, event):
        event_result = {
            "desc": "",
            "count": 0
        }
        try:
            if not event:
                return event_result

            logging.info(f"处理特殊事件 - 房间: {room_id}, 用户: {user_id}, 事件类型: {event['type']}")

            if event['type'] == 'treasure':
                # 处理宝藏奖励
                # 从reward_*属性中构造reward对象
                reward = {
                    'type': event.get('reward_type', 'coins'),
                    'amount': random.randint(
                        event.get('reward_min', 1000),
                        event.get('reward_max', 5000)
                    )
                }

                if reward['type'] == 'coins':
                    # 应用收益buff
                    base_amount = reward['amount']
                    final_amount = self._apply_buffs(room_id, user_id, base_amount, 'income_rate')
                    self.db.add_coins(room_id, user_id, final_amount)
                    # 更新收入任务进度
                    self.db.update_task_progress(room_id, user_id, "income", final_amount)
                    event_result['desc'] = f"发现宝藏，获得{int(final_amount)}金币"
                    logging.info(f"宝藏奖励 - 金币: {final_amount}")
                elif reward['type'] == 'exp':
                    # 应用经验buff
                    base_exp = reward['amount']
                    final_exp = self._apply_buffs(room_id, user_id, base_exp, 'exp_rate')
                    self.db.batch_add_exp(room_id, user_id, final_exp)
                    event_result['desc'] = f"发现宝藏，获得{int(final_exp)}经验"
                    logging.info(f"宝藏奖励 - 经验: {final_exp}")
            elif event['type'] == 'supper_attack':
                buff_value = self.events[event['type']].get('buff', 1) # 默认值为1，其实用不上，关键是有记录和有效期
                duration = event['duration']
                buff_type = "supper_attack"
                self.db.add_buff(room_id, user_id, buff_type, buff_value, duration)
                event_result['desc'] = f"获得偷鱼隐身穿墙能力,\n 持续时间:{duration}"

            elif event['type'] in ['fisher_blessing', 'golden_rod']:
                # 添加buff效果
                buff_type = 'success_rate' if event['type'] == 'fisher_blessing' else 'income_rate'
                buff_value = self.events[event['type']].get('buff', 1.5)  # 默认值为1.5
                duration = event['duration']
                self.db.add_buff(room_id, user_id, buff_type, buff_value, duration)
                if buff_type == 'success_rate':
                    event_result['desc'] = f"渔神祝福,钓鱼加持{buff_value},\n 持续时间:{duration}"
                elif buff_type == 'income_rate':
                    event_result['desc'] = f"渔神祝福,收入加持{buff_value}，\n 持续时间:{duration}"

                logging.info(f"添加Buff - 类型: {buff_type}, 值: {buff_value}, 持续时间: {duration}秒")

            elif event['type'] == 'fish_swarm':
                # 额外获得鱼
                count = event.get('count', 2)  # 默认值为2
                event_result['desc'] = f"鱼群来了，获得{count+1}条鱼"
                event_result['count'] = count
                logging.info(f"鱼群效果 - 额外获得{count}条鱼")

            elif event['type'] == 'double_catch':
                event_result['desc'] = "鱼儿进窝，双飞两条"
                event_result['count'] = 1
                logging.info("双倍收获效果已应用")
            return event_result
        except Exception as e:
            self._handle_exception(e, "处理特殊事件错误")
            return event_result

    def confirm_hire_dog(self, room_id, user_id):
        """确认雇佣旺财"""
        try:
            player = self.db.get_player_info(room_id, user_id)
            if not player:
                self._send_message(room_id, "请先开始钓鱼吧~", user_id)
                return

            cost = 5000  # 雇佣旺财的固定费用

            if player['dog_status'] != 0:
                self._send_message(room_id, "你已经有旺财了!", user_id)
                return

            if player['coins'] < cost:
                self._send_message(room_id, f"金币不足,需要{cost}金币", user_id)
                return

            if self.db.hire_dog(room_id, user_id, cost):
                msg = (
                    "🐕 成功雇佣旺财!\n"
                    f"💰 花费金币：{cost}\n"
                    "🎁 效果：降低被偷鱼概率\n"
                    "💡 提示：发送【找回旺财】让旺财回到岗位"
                )
            else:
                msg = "雇佣失败,请稍后再试"

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "确认雇佣旺财错误")
            self._send_message(room_id, "雇佣出错了,请稍后再试", user_id)

    def _apply_buffs(self, room_id, user_id, base_value, buff_type='success_rate'):
        try:
            # 获取玩家所有buff(包括装饰)
            buffs = self.db.get_player_buffs(room_id, user_id)

            # 初始化倍率
            multiplier = 1.0

            # 应用所有buff效果
            for buff in buffs:
                buff_name, value = buff
                # 如果是对应类型的buff或全局buff
                if buff_name == buff_type or buff_name == 'all_rate':
                    multiplier *= (1 + value)

            # 计算最终值
            final_value = base_value * multiplier

            # 根据buff类型设置上限
            if buff_type == 'success_rate':
                return min(final_value, 0.95)  # 最高95%成功率
            elif buff_type == 'anti_steal':
                return min(final_value, 0.75)  # 最高75%防盗率
            else:
                return final_value

        except Exception as e:
            logging.error(f"应用buff错误: {e}")
            return base_value

    def steal_fish(self, room_id, user_id, target_id):
        try:
            # 使用操作锁防止并发问题
            if not self.db.check_operation_lock(room_id, user_id, "steal_fish"):
                # 使用更友好、更有游戏感的提示信息
                messages = [
                    "🚫 你小子想卡BUG么？别这么频繁操作！",
                    "⚠️ 渔场管理员：操作太快了，会吓跑鱼的！",
                    "🐟 鱼儿们：这个人好可怕，一直想偷我们！",
                    "🕹️ 系统提示：连续点击是没用的，冷静一下吧！",
                    "🔒 安全系统：检测到异常操作，请正常游戏~"
                ]
                self._send_message(room_id, random.choice(messages), user_id)
                return

            # 检查冷却时间，使用配置文件中的偷鱼冷却时间
            cooldown = self.db.get_steal_cooldown(room_id, user_id, self.steal_cooldown)
            if cooldown > 0:
                self._send_message(room_id, f"⏰ 偷鱼冷却中,剩余{cooldown}秒", user_id)
                return

            # 检查目标是否是自己
            if user_id == target_id:
                self._send_message(room_id, "不能偷自己的鱼！", user_id)
                # 设置10秒冷却时间
                self.db.set_steal_cooldown_10s(room_id, user_id)
                return

            # 获取目标玩家信息
            target_player = self.db.get_player_info(room_id, target_id)
            if not target_player:
                self._send_message(room_id, "找不到目标玩家", user_id)
                # 设置10秒冷却时间
                self.db.set_steal_cooldown_10s(room_id, user_id)
                return

            # 先检查目标是否有防盗门装饰
            target_decorations = self.db.get_decorations(room_id, target_id)
            # 防盗能力
            anti_steal_rate = 0
            # 是不是最牛逼的狗狗
            has_super_dog = False

            for deco in target_decorations:
                if deco['name'] == '防盗门':
                    has_anti_steal_deco = True
                    # 获取基础防盗值并根据等级计算
                    anti_steal_rate = anti_steal_rate + deco['buff_value']
                if deco['name'] == '黄金地板':
                    anti_steal_rate = anti_steal_rate + deco['buff_value']

            # 检查目标是否有旺财守护，叠加防盗系数
            if target_player['dog_status'] == 1 and target_player['dog_level'] > 0:
                dog_level = target_player['dog_level']
                dog_info = self._get_dog_info(dog_level)
                anti_steal_rate = anti_steal_rate + dog_info['anti_steal_rate'] / 100
                if dog_level >= 110:
                    has_super_dog = True

            #检查是否有攻击加成（特殊事件加成）
            no_attack_buff = True
            # 获取玩家所有buff
            buffs = self.db.get_player_buffs(room_id, user_id)
            for buff in buffs:
                buff_name, value = buff
                if buff_name == 'supper_attack':
                    no_attack_buff = False

            if anti_steal_rate > 0 and no_attack_buff:
                logging.info(f"防盗概率为：{anti_steal_rate}")
                # 应用buff效果
                anti_steal_rate = self._apply_buffs(room_id, target_id, anti_steal_rate, 'anti_steal')
                if has_super_dog:
                    anti_steal_rate = anti_steal_rate + 0.10
                logging.info(f"叠加buff后，防盗概率为：{anti_steal_rate}")
                if random.random() < anti_steal_rate:
                    # 随机扣除金币
                    punish_amount = random.randint(self.steal_punish_min, self.steal_punish_max)

                    # 获取玩家信息
                    player = self.db.get_player_info(room_id, user_id)
                    if player:
                        # 确保不会扣成负数
                        punish_amount = min(punish_amount, player['coins'])
                        super_dog_str = "旺财"
                        if has_super_dog:
                            super_dog_str = "神兽"
                            punish_amount = punish_amount * 3
                        # 扣除金币
                        if punish_amount > 0:
                            # 从偷鱼者账户扣除金币
                            self.db.add_coins_atonce(room_id, user_id, -punish_amount)

                            # 将金币加到被偷者账户上
                            self.db.add_coins_atonce(room_id, target_id, punish_amount)

                            # 发送一条合并的消息到聊天室
                            self._send_message(
                                room_id,
                                f"🐕 我去，门外就被{super_dog_str}发现了！\n"
                                f"还狠狠地咬了一口 \n"
                                f"抢走了你{punish_amount}金币\n"
                                f"摇着尾巴交给了鱼塘主人",
                                user_id
                            )
                        else:
                            self._send_message(room_id, f"🐕 被{super_dog_str}发现了！\n幸好你已经没钱了，{super_dog_str}只是瞪了你一眼。",
                                               user_id)
                    else:
                        self._send_message(room_id, f"🐕 糟糕！被旺财发现了！", user_id)

                    # 发送旺财GIF表情
                    gif_path = r'code\mossbot\BotServer\BotFunction\fishing\images\dog.gif'
                    if os.path.exists(gif_path):
                        try:
                            self.wcf.send_emotion(path=gif_path, receiver=room_id)
                        except Exception as e:
                            logging.error(f"发送旺财表情错误: {e}")

                    # 设置偷鱼冷却时间（偷失败10秒）
                    self.db.set_steal_cooldown_10s(room_id, user_id)
                    return

            # 获取目标玩家的鱼塘
            target_fish_pond = self.db.get_fish_pond(room_id, target_id)
            if not target_fish_pond:
                self._send_message(room_id, "对方鱼塘是空的", user_id)
                # 设置统一的冷却时间
                self.db.set_steal_cooldown_10s(room_id, user_id)
                return

            # 随机选择一条鱼
            random_fish = random.choice(target_fish_pond)
            fish_name = random_fish['name']
            fish_price = random_fish['price']

            # 每次只偷一条鱼
            steal_count = 1

            # 对大鱼实施额外的概率检查
            if fish_price >= self.steal_big_line:
                # 有一半概率溜走 3999元以上的大鱼
                if has_super_dog and random.random() > 0.85:
                    # 设置统一的冷却时间
                    self.db.set_steal_cooldown_10s(room_id, user_id)
                    self._send_message(room_id, "我操，塘主养有神兽，凶煞双眼早就看到你了！", user_id)
                elif random.random() > self.steal_big_rate:
                    # 设置统一的冷却时间
                    self.db.set_steal_cooldown_10s(room_id, user_id)
                    self._send_message(room_id, "做贼心虚手一抖，鱼溜回鱼塘了！", user_id)
                    return
                # 85%命中率，15%溜走
                elif fish_price > 10000 and random.random() > 0.90:
                    self.db.set_steal_cooldown_10s(room_id, user_id)
                    self._send_message(room_id, "这条鱼太大了，\n 抓不牢溜回鱼塘了！", user_id)
                    return
                # 75%命中率，25%溜走
                elif fish_price > 20000 and random.random() > 0.85:
                    self.db.set_steal_cooldown_10s(room_id, user_id)
                    self._send_message(room_id, "这条鱼有点大，你不会抓鱼，\n 溜回鱼塘了！", user_id)
                    return
                # 65%命中率，35%溜走
                elif fish_price > 40000 and random.random() > 0.75:
                    self.db.set_steal_cooldown_10s(room_id, user_id)
                    self._send_message(room_id, "好高伙，这么大鱼，\n 你没办法抓住！", user_id)
                    return
                elif fish_price > 60000 and random.random() > 0.40:
                    self.db.set_steal_cooldown_10s(room_id, user_id)
                    self._send_message(room_id, "这么大的鱼，尾巴直接拍晕你了，\n 又溜回鱼塘了！", user_id)
                    return
            # 执行偷鱼操作
            success = self.db.transfer_fish(
                room_id, target_id,  # 源
                room_id, user_id,  # 目标
                fish_name, steal_count, fish_price
            )

            if success:
                # 更新消息，强调只偷了一条鱼
                msg = (
                    f"🎣 成功偷到 1条【{fish_name}】\n"
                    f"💰 价值：{fish_price}金币"
                )

                # 使相关缓存失效，确保数据一致性
                self._invalidate_player_cache(room_id, user_id)
                self._invalidate_player_cache(room_id, target_id)  # 也要使被偷者的缓存失效
            else:
                msg = "偷鱼失败，可能鱼已经被别人偷走了"
                # 设置10秒冷却时间
                self.db.set_steal_cooldown_10s(room_id, user_id)

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "偷鱼错误")
            self._send_message(room_id, "偷鱼失败，请稍后再试", user_id)

    def search_dog(self, room_id, user_id):
        """找回/购买旺财"""
        try:
            player = self.db.get_player_info(room_id, user_id)
            if not player:
                self._send_message(room_id, "请先开始钓鱼吧~", user_id)
                return

            # 检查旺财状态和等级是否一致
            if player['dog_level'] == 0:
                # 玩家没有旺财，显示购买选项
                cost = 5000  # 购买旺财的费用
                msg = (
                    "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                    "🐕 雇佣旺财 🐕\n\n"
                    f"💰 费用：{cost}金币\n"
                    "🎁 效果：守护鱼塘，降低被偷鱼概率\n\n"
                    "发送【确认雇佣】购买旺财\n"
                    "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                )
                self._send_message(room_id, msg, user_id)
                return

            # 修复可能的状态不一致问题
            if player['dog_level'] > 0 and player['dog_status'] == 0:
                # 有狗但状态是0，这是不一致的状态
                logging.info(f"发现旺财状态不一致 - 房间:{room_id} 玩家:{user_id}")

            # 已有旺财，执行找回逻辑
            if not self._check_search_cooldown(room_id, user_id):
                remaining = self._get_remaining_search_cooldown(room_id, user_id)
                self._send_message(room_id, f"⏰ 找回旺财冷却中,剩余{remaining}秒", user_id)
                return

            if player['dog_status'] == 1:
                self._send_message(room_id, "旺财已经在岗位上了!", user_id)
                return

            # 确保dog_level与dog_status状态同步
            if player['dog_level'] == 0:
                self._send_message(room_id, "你还没有雇佣旺财，请先购买一只！", user_id)
                return

            # 计算找回成功率
            success_rate = 0.5 + player['dog_level'] * 0.1
            if random.random() < success_rate:
                if self.db.update_dog_status(room_id, user_id, 1):
                    msg = (
                        "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                        "🐶 成功找回旺财!\n"
                        "现在它会继续守护你的鱼塘~\n"
                        "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                    )
                else:
                    msg = "找回旺财失败,请稍后再试"
            else:
                msg = (
                    "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                    "😅 没找到旺财\n"
                    "它可能去玩了,过会再来找找吧~\n"
                    "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                )

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "找回旺财错误")
            self._send_message(room_id, "找回旺财出错了,请稍后再试", user_id)

    def sell_high_price_fish(self, room_id, user_id):
        """一键卖出限时高价鱼"""
        try:
            result = self.db.sell_high_price_fish(room_id, user_id)
            if not result["success"]:
                self._send_message(room_id, result["message"], user_id)
                return

            if not result["sold_fish"]:
                self._send_message(room_id, "没有可以高价卖出的鱼哦~", user_id)
                return

            # 应用装饰提供的收入加成
            income_multiplier = self._apply_buffs(room_id, user_id,1.0, 'income_rate')

            # 计算每条鱼的养殖加成和最终价值
            total_base_income = 0
            total_farming_bonus = 0
            fish_summary = []

            for fish in result["sold_fish"]:
                # 获取基础信息
                base_price = fish["price"]  # 这里已经是特价了
                fish_count = fish["count"]
                catch_time = fish.get("catch_time")

                # 计算养殖加成
                farming_bonus = 0
                if catch_time:
                    farming_bonus = self._calculate_farming_bonus(catch_time, base_price)
                
                # 计算这条鱼的总收入
                base_income = fish_count * base_price
                farming_income = fish_count * farming_bonus if farming_bonus > 0 else 0

                total_base_income += base_income
                total_farming_bonus += farming_income

                # 添加到鱼类汇总
                fish_summary.append({
                    "name": fish["name"],
                    "count": fish_count,
                    "base_price": base_price,
                    "farming_bonus": farming_bonus,
                    "income": base_income + farming_income
                })

            # 计算最终收入（应用装饰加成）
            subtotal = total_base_income + total_farming_bonus
            income_bonus = int(subtotal * (income_multiplier - 1.0)) if income_multiplier > 1.0 else 0
            final_income = int(subtotal * income_multiplier)

            # 如果有收入加成，添加到玩家金币中
            if income_bonus > 0:
                self.db.add_coins(room_id, user_id, income_bonus)
                # 更新比赛分数
                self.db.update_competition_score(room_id, user_id, income_bonus)

            # 构建消息
            msg = (
                "🔥 限时高价鱼已卖出！\n\n"
                "📊 详细信息："
            )

            # 添加每种鱼的详细信息
            for fish in fish_summary:
                msg += f"\n【{fish['name']}】x{fish['count']}"
                msg += f"\n💰 收入：{fish['income']}金币"
                if fish["farming_bonus"] > 0:
                    bonus_percent = int((fish["farming_bonus"] / fish["base_price"]) * 100)
                    msg += f"\n🌱 养殖：+{bonus_percent}%"
                msg += "\n"

            # 添加总体加成信息
            msg += f"\n💎 总收入：{final_income}金币"
            if total_farming_bonus > 0:
                farming_percent = int((total_farming_bonus / total_base_income) * 100)
                msg += f"\n🌱 总养殖：+{total_farming_bonus}金币(+{farming_percent}%)"

            if income_multiplier > 1.0:
                msg += f"\n💫 装饰加成：+{income_bonus}金币({int((income_multiplier - 1) * 100)}%)"

            # 发送消息
            self._send_message(room_id, msg, user_id)

            # 清理玩家缓存
            self._invalidate_player_cache(room_id, user_id)

            # 更新任务进度
            total_fish_count = sum(fish["count"] for fish in result["sold_fish"])
            self.db.update_task_progress(room_id, user_id, "sell", total_fish_count)
            self.db.update_task_progress(room_id, user_id, "income", final_income)

        except Exception as e:
            self._handle_exception(e, "一键卖高价鱼错误")
            self._send_message(room_id, "卖鱼失败，请稍后再试", user_id)

    def _check_search_cooldown(self, room_id, user_id):
        """检查找回旺财冷却"""
        last_search = self.db.get_last_search_time(room_id, user_id)
        if not last_search:
            return True
        elapsed = (datetime.now() - last_search).total_seconds()
        return elapsed >= self.dog_search_cooldown

    def _get_remaining_cooldown(self, room_id, user_id):
        """获取剩余冷却时间"""
        last_time = self.db.get_last_fishing_time(room_id, user_id)
        if not last_time:
            return 0
        elapsed = (datetime.now() - last_time).total_seconds()
        return max(0, int(self.cooldown - elapsed))

    def _get_remaining_search_cooldown(self, room_id, user_id):
        """获取找回旺财剩余冷却时间"""
        last_time = self.db.get_last_search_time(room_id, user_id)
        if not last_time:
            return 0
        elapsed = (datetime.now() - last_time).total_seconds()
        return max(0, int(self.dog_search_cooldown - elapsed))

    def _random_rarity(self, room_id, user_id):
        """随机生成稀有度"""
        try:
            # 获取配置中的稀有度权重
            rarity_weights = self.config.get("rarity_weights", {})
            rarities = list(rarity_weights.keys())
            weights = list(rarity_weights.values())

            # 获取稀有度加成
            rare_boost = self._apply_buffs(room_id, user_id, 1.0, 'rare_rate') - 1.0

            # 调整权重 - 提高稀有和以上的概率
            if rare_boost > 0:
                # 确保低等级珊瑚礁也有明显效果
                min_boost = 0.05  # 设置最小5%的加成效果
                rare_boost = max(rare_boost, min_boost)
                logging.info(f'稀有度计算加成: {rare_boost}')
                # 降低普通鱼的权重
                normal_idx = rarities.index("普通")
                weights[normal_idx] = max(0.1, weights[normal_idx] * (1 - rare_boost * 0.5))

                # 增加其他稀有度的权重
                for i in range(len(weights)):
                    if i != normal_idx:
                        weights[i] *= (1 + rare_boost)

                # 重新归一化权重
                total = sum(weights)
                weights = [w / total for w in weights]

            # 使用权重随机选择
            return random.choices(rarities, weights=weights, k=1)[0]
        except Exception as e:
            self._handle_exception(e, "随机稀有度错误")
        return "普通"  # 默认返回普通稀有度 

    def _load_default_config(self):
        """加载默认配置"""
        # 保存完整配置
        self.config = {}

        self.cooldown = 45
        self.steal_cooldown = 60
        self.dog_search_cooldown = 600
        self.pool_refresh_interval = 1800
        self.market_refresh_interval = 1800

        # 偷鱼惩罚配置
        self.steal_punish_min = 200
        self.steal_punish_max = 4000

        # 大鱼偷取配置
        self.steal_big_line = 5999
        self.steal_big_rate = 0.7

        self.rarity_weights = {
            "普通": 0.6,
            "稀有": 0.25,
            "珍稀": 0.1,
            "传说": 0.05
        }

        self.rod_upgrade = {
            "base_cost": 500,
            "cost_multiplier": 2
        }

        self.dog_upgrade = {
            "base_cost": 2000,
            "cost_multiplier": 1.5
        }

        self.events = {
            "double_catch": {
                "chance": 0.05,
                "duration": 300
            },
            "fish_swarm": {
                "chance": 0.03,
                "count": 3
            },
            "treasure": {
                "chance": 0.02,
                "rewards": [
                    {"type": "coins", "min": 1000, "max": 10000},
                    {"type": "exp", "min": 100, "max": 1000}
                ]
            },
            "fisher_blessing": {
                "chance": 0.04,
                "buff": 1.5,
                "duration": 600
            },
            "golden_rod": {
                "chance": 0.01,
                "duration": 300
            }
        }

        logging.warning("使用默认配置")

    def _create_default_image(self, path):
        """创建默认图片"""
        try:
            img = Image.new('RGB', (200, 200), color='white')
            draw = ImageDraw.Draw(img)
            draw.text((50, 90), "暂无图片", fill='black')
            img.save(path)
        except Exception as e:
            self._handle_exception(e, "创建默认图片错误")

    def show_special_price(self, room_id, user_id):
        """显示限时高价"""
        try:
            special_prices = self.db.get_special_prices()
            if not special_prices:
                self._send_message(room_id, "当前没有限时高价的鱼", user_id)
                return

            msg = (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                "💰 限时高价收购 💰\n\n"
            )

            for fish in special_prices:
                msg += f"🐟 【{fish['name']}】💎 ￥{fish['special_price']}\n"

            msg += (
                f"\n⏰ 本轮剩余时间：{special_prices[0]['remaining_time']}秒\n"
                "💡 麦掉就是赚到,冲冲冲！\n"
                "📢 每隔30分钟刷新鱼市！\n"
                "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
            )

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "显示限时高价错误")

    def _handle_fishing_result(self, room_id, user_id, catch_info, event_result, wxName):
        """处理钓鱼结果"""
        try:
            # 检查是否成功
            success = catch_info.get("success", True)

            # 获取玩家昵称
            #player_name = self.wcf.get_alias_in_chatroom(roomid=room_id, wxid=user_id)
            player_name = wxName

            if success:
                # 格式化捕获信息
                fish_name = catch_info.get("name", "未知鱼")
                fish_rarity = catch_info.get("rarity", "普通")
                fish_price = catch_info.get("price", 0)

                # 应用经验加成
                base_exp = catch_info.get("exp", 10)
                exp_multiplier = self._apply_buffs(room_id, user_id,1.0, 'exp_rate')
                final_exp = int(base_exp * exp_multiplier)

                # 格式化捕获信息（包含经验加成信息）
                catch_msg = self._format_catch_message(catch_info, player_name, exp_multiplier, final_exp)
                # 事件信息
                if event_result and not event_result['desc'] == '':
                    catch_msg += f"\n\n🌟事件-{event_result['desc']}"
                self._send_message(room_id, catch_msg, user_id)

                # 添加经验并检查升级
                added_exp = self.db.batch_add_exp(room_id, user_id, final_exp)

                # 检查是否升级
                level_up_info = self.db.check_and_process_level_up(room_id, user_id)
                if level_up_info and level_up_info.get("level_up"):
                    new_level = level_up_info.get("new_level")
                    reward_coins = level_up_info.get("reward_coins", 0)

                    level_up_msg = (
                        f"🎊 升级了！恭喜达到 Lv.{new_level}\n"
                        f"🎁 奖励: {reward_coins} 金币"
                    )
                    self._send_message(room_id, level_up_msg, user_id)

                    # 更新任务进度（升级奖励也计入收入）
                    self.db.update_task_progress(room_id, user_id, "income", reward_coins)
            else:
                # 钓鱼失败的消息已在start_fishing中处理
                pass

        except Exception as e:
            self._handle_exception(e, "处理钓鱼结果错误")

    def _format_catch_message(self, catch_info, user_name, exp_multiplier=1.0, final_exp=None):
        """格式化钓鱼信息"""
        try:
            fish_name = catch_info.get("name", "未知鱼")
            fish_rarity = catch_info.get("rarity", "普通")
            fish_price = catch_info.get("price", 0)
            base_exp = catch_info.get("exp", 10)

            # 如果没有提供最终经验值，则使用基础经验值
            if final_exp is None:
                final_exp = base_exp

            icons = {
                "普通": "🐟",
                "稀有": "🐠",
                "珍稀": "🐡",
                "传说": "🐋"
            }

            icon = icons.get(fish_rarity, "🐟")

            # 根据稀有度选择颜色
            colors = {
                "普通": "⚪",
                "稀有": "🔵",
                "珍稀": "🟣",
                "传说": "🟡"
            }

            color = colors.get(fish_rarity, "⚪")

            # 构建消息
            msg = (
                f"{icon} 钓到 {fish_name}\n"
                f"{color} {fish_rarity}\n"
                f"💰 价值: {fish_price}金币\n"
            )

            # 添加经验信息
            if exp_multiplier > 1.0:
                bonus_exp = final_exp - base_exp
                msg += f"\n✨ 经验: +{final_exp} "
            else:
                msg += f"\n✨ 经验: +{base_exp}"

            return msg

        except Exception as e:
            self._handle_exception(e, "格式化钓鱼信息错误")
            return f"恭喜钓到了 {catch_info.get('name', '未知鱼')}！"

    def _calculate_next_level_exp(self, current_level):
        """计算下一级所需经验"""
        try:
            # 基础经验1000，每级增加500
            base_exp = 1000
            exp_increment = 500
            return base_exp + (current_level * exp_increment)
        except Exception as e:
            self._handle_exception(e, "计算升级经验错误")
            return 1000

    def show_fish_pool(self, room_id, user_id):
        """显示鱼池信息"""
        try:
            pool_info = self.db.get_fish_pool()
            if not pool_info:
                # 如果鱼池为空，尝试刷新
                self.db.refresh_fish_pool()
                pool_info = self.db.get_fish_pool()
                if not pool_info:
                    self._send_message(room_id, "鱼池正在刷新中...", user_id)
                    return

            msg = (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                "🎣 本轮鱼池 🎣\n\n"
            )

            for fish in pool_info["fish_list"]:
                msg += f"🐟 【{fish['name']}】￥{fish['price']}({fish['count']}条)\n"

            msg += (
                "\n📢 鱼池钓空时刷新\n"
                "⏰ 每30分钟自动刷新鱼池\n"
            )

            if pool_info["remaining_time"] > 0:
                msg += f"⌛ 剩余时间：{pool_info['remaining_time']}秒\n"

            msg += "╰┄┄┄┄┄┄┄┄┄┄··┄╯"

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "显示鱼池错误")

    def _check_achievements(self, room_id, user_id):
        """检查成就完成情况"""
        try:
            player = self.db.get_player_info(room_id, user_id)
            if not player:
                return

            achievements = {
                "初出茅庐": {"condition": ("success_count", 1), "reward": 10000},
                "渔场新星": {"condition": ("success_count", 10), "reward": 50000},
                "渔场达人": {"condition": ("success_count", 100), "reward": 100000},
                "渔场大师": {"condition": ("success_count", 1000), "reward": 150000},
                "稀有收藏家": {"condition": ("rare_fish_count", 10), "reward": 100000},
                "传说收藏家": {"condition": ("legendary_fish_count", 5), "reward": 300000},
                "百万富翁": {"condition": ("total_income", 1000000), "reward": 3000000}
            }

            for name, info in achievements.items():
                stat_name, required_value = info["condition"]
                if player[stat_name] >= required_value:
                    if self.db.add_achievement(room_id, user_id, name):
                        msg = (
                            "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                            "🎊 解锁新成就!\n"
                            f"🏆 成就: {name}\n"
                            f"💰 奖励: {info['reward']}金币\n"
                            "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                        )
                        self._send_message(room_id, msg, user_id)
                        self.db.add_coins(room_id, user_id, info['reward'])
                        # 更新收入任务进度（成就奖励也计入收入）
                        self.db.update_task_progress(room_id, user_id, "income", info['reward'])

        except Exception as e:
            self._handle_exception(e, "检查成就错误")

    def show_achievements(self, room_id, user_id):
        """显示成就列表"""
        try:
            achievements = self.db.get_achievements(room_id, user_id)

            msg = (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                "🏆 成就系统 🏆\n\n"
            )

            if achievements:
                for ach in achievements:
                    time_str = ach['time'].split()[0] if ach['time'] else ""
                    msg += f"✨ {ach['name']}\n📅 获得时间: {time_str}\n\n"
            else:
                msg += "暂无成就，继续努力吧~\n"

            msg += "╰┄┄┄┄┄┄┄┄┄┄··┄╯"

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "显示成就错误")

    def show_leaderboard(self, room_id, user_id, board_type="wealth"):
        """显示排行榜"""
        try:
            leaderboard = self.db.get_leaderboard(room_id, board_type)

            type_emoji = {
                "wealth": "💰",
                "level": "📊",
                "rare": "✨",
                "success": "🎯"
            }

            type_name = {
                "wealth": "财富",
                "level": "等级",
                "rare": "稀有鱼",
                "success": "成功率"
            }

            msg = (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                f"{type_emoji[board_type]} {type_name[board_type]}排行榜 {type_emoji[board_type]}\n\n"
            )
            members = self.wcf.getGroupMemberInfos(robotId='wxid_cbgpauriedg629',roomId=room_id).get('data')
            for idx, player in enumerate(leaderboard[:15], 1):
                #name = self.wcf.get_alias_in_chatroom(roomid=room_id, wxid=player['user_id'])
                name = self.getNameFromMembers(player['user_id'], members)
                if board_type == "wealth":
                    value = f"{int(player['value'])}金币"
                elif board_type == "level":
                    value = f"Lv.{int(player['value'])}"
                elif board_type == "rare":
                    value = f"{int(player['value'])}条"
                else:
                    value = f"{int(player['value'])}%"

                msg += f"第{idx}名 {name}: {value}\n"

            msg += "╰┄┄┄┄┄┄┄┄┄┄··┄╯"

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "显示排行榜错误")
    def getNameFromMembers(self, wxId, members):
        for key in members.keys():
            if key == wxId:
                return members[key]['nickname']
        return ''
    def start_competition(self, room_id, comp_type="normal"):
        """开始钓鱼比赛"""
        try:
            # 先检查是否有正在进行的比赛
            current_comp = self.db.get_current_competition(room_id)
            if current_comp:
                remaining_time = int((current_comp['end_time'] - datetime.now()).total_seconds())
                if remaining_time > 0:
                    msg = (
                        "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                        "❌ 已有比赛正在进行!\n\n"
                        f"⏰ 剩余时间: {remaining_time // 60}分{remaining_time % 60}秒\n"
                        "💡 发送【比赛排名】查看当前排名\n"
                        "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                    )
                    self._send_message(room_id, msg)
                    return
                else:
                    # 如果比赛已经结束但还未发放奖励，立即结束它
                    self.end_competition(room_id)

            # 设置比赛时间为30分钟
            duration = 1800  # 3分钟 = 180秒

            if self.db.create_competition(room_id, comp_type, duration):
                msg = (
                    "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                    "🎊 钓鱼比赛开始! 🎊\n\n"
                    "⏰ 持续时间: 30分钟\n"
                    "🎯 比赛规则:\n"
                    "- 比赛期间钓鱼收益翻倍\n"
                    "- 稀有鱼出现概率提升\n"
                    "- 统计总收入最高者获胜\n\n"
                    "💰 奖励:\n"
                    "🥇 第一名: 100000金币\n"
                    "🥈 第二名: 50000金币\n"
                    "🥉 第三名: 30000金币\n\n"
                    "💡 发送【参加比赛】加入比赛\n"
                    "💡 发送【比赛排名】查看排名\n"
                    "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                )
                self._send_message(room_id, msg)

        except Exception as e:
            self._handle_exception(e, "开始比赛错误")

    def end_competition(self, room_id):
        """结束钓鱼比赛"""
        try:
            # 获取比赛结果并发放奖励
            results = self.db.end_competition_and_reward(room_id, {
                "winners": [
                    {"coins": 100000, "exp": 1000},  # 第一名
                    {"coins": 50000, "exp": 500},  # 第二名
                    {"coins": 30000, "exp": 300}  # 第三名
                ],
                "participation": {"coins": 5000, "exp": 100}  # 参与奖励
            })

            if not results:
                return

            # 构建获奖消息
            msg = (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                "🎊 钓鱼比赛结束! 🎊\n\n"
                "🏆 比赛结果:\n\n"
            )

            # 添加前三名信息
            members = self.wcf.getGroupMemberInfos(robotId='wxid_cbgpauriedg629', roomId=room_id).get('data')
            for idx, player in enumerate(results['winners'], 1):
                #name = self.wcf.get_alias_in_chatroom(roomid=room_id, wxid=player['user_id'])
                name = self.getNameFromMembers(player['user_id'], members)
                msg += (
                    f"{'🥇' if idx == 1 else '🥈' if idx == 2 else '🥉'} {name}\n"
                    f"💰 奖励: {player['reward_coins']}金币\n"
                    f"📈 经验: {player['reward_exp']}点\n"
                    f"🎯 得分: {player['score']}金币\n\n"
                )
                # 更新获奖者的收入任务进度
                self.db.update_task_progress(room_id, player['user_id'], "income", player['reward_coins'])

            msg += "奖励已发放，请查收！\n"
            msg += "╰┄┄┄┄┄┄┄┄┄┄··┄╯"

            # 发送比赛结果消息
            self._send_message(room_id, msg)

            # 给参与者发送参与奖励消息并更新任务进度
            for player in results['participants']:
                participant_msg = (
                    "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                    "🎊 感谢参与钓鱼比赛!\n\n"
                    f"💰 参与奖励: {player['reward_coins']}金币\n"
                    f"📈 额外经验: {player['reward_exp']}点\n"
                    f"🎯 最终得分: {player['score']}金币\n"
                    "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                )
                self._send_message(room_id, participant_msg, player['user_id'])
                # 更新参与者的收入任务进度
                self.db.update_task_progress(room_id, player['user_id'], "income", player['reward_coins'])

        except Exception as e:
            self._handle_exception(e, "结束比赛错误")

    def use_item(self, room_id, user_id, item_name):
        """使用道具"""
        try:
            result = self.db.use_item(room_id, user_id, item_name)
            if not result["success"]:
                self._send_message(room_id, result["message"], user_id)
                return

            effects = {
                "双倍经验卡": {"type": "exp", "value": 2, "duration": 1800},
                "高级鱼饵": {"type": "rare_rate", "value": 1.5, "duration": 1800},
                "防盗卡": {"type": "anti_steal", "value": 2, "duration": 3600},
                "寻宝卡": {"type": "treasure", "value": 2, "duration": 1800}
            }

            effect = effects[item_name]
            self.db.add_buff(room_id, user_id, effect["type"], effect["value"], effect["duration"])

            msg = (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                f"🎁 成功使用 {item_name}!\n"
                f"⏰ 持续时间: {effect['duration'] // 60}分钟\n"
                "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
            )
            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "使用道具错误")

    def show_items(self, room_id, user_id):
        """显示道具背包"""
        try:
            items = self.db.get_items(room_id, user_id)

            msg = (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                "🎒 道具背包 🎒\n\n"
            )

            if items:
                for item in items:
                    expire = f"(⏰ {item['expire_time']}到期)" if item['expire_time'] else ""
                    msg += f"📦 {item['name']} x{item['count']} {expire}\n"
            else:
                msg += "背包空空如也~\n"

            msg += "\n💡 发送【使用#道具名】使用道具\n"
            msg += "╰┄┄┄┄┄┄┄┄┄┄··┄╯"

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "显示道具错误")

    def show_tasks(self, room_id, user_id):
        """显示任务列表"""
        try:
            tasks = self.db.get_daily_tasks(room_id, user_id)
            if not tasks:
                # 如果没有任务，自动刷新一次
                if not hasattr(self, '_task_refresh_lock'):
                    self._task_refresh_lock = {}

                # 检查是否在锁定期
                lock_key = f"{room_id}_{user_id}"
                current_time = time.time()
                if lock_key not in self._task_refresh_lock or \
                        current_time - self._task_refresh_lock[lock_key] > 5:  # 5秒锁定期
                    self._task_refresh_lock[lock_key] = current_time

                    # 刷新任务
                    self.db.refresh_daily_tasks(room_id, user_id)
                    tasks = self.db.get_daily_tasks(room_id, user_id)
                else:
                    self._send_message(room_id, "暂无任务，请稍后再试~", user_id)
                    return

            # 显示任务
            if tasks:
                # 获取第一个任务（因为每天只有一个任务）
                task = tasks[0]

                # 计算任务进度百分比
                progress_percent = min(100, int((task['progress'] / task['target']) * 100))

                # 构建进度条 (减少到5个格)
                progress_bar = "▰" * (progress_percent // 20) + "▱" * (5 - progress_percent // 20)

                msg = (
                    "╭══════════╮\n"
                    "📋 每日任务 📋\n\n"
                    f"🎯 任务：{task['name']}\n"
                    f"📊 进度：{task['progress']}/{task['target']}\n"
                    f"⏳ 进度：{progress_bar} {progress_percent}%\n"
                    f"🎁 奖励：{task['reward_amount']}{'金币' if task['reward_type'] == 'coins' else '经验'}\n\n"
                    f"💡 提示：{task['description']}\n"
                    "╰══════════╯"
                )

                # 如果任务完成，添加完成提示
                if task['claimed']:
                    msg = msg.replace("📋 每日任务 📋", "📋 每日任务(已完成) 📋")
                    msg = msg.replace("╰══════════╯", "🎊 恭喜完成任务！\n╰══════════╯")

                self._send_message(room_id, msg, user_id)
            else:
                self._send_message(room_id, "暂无任务，请稍后再试~", user_id)

        except Exception as e:
            self._handle_exception(e, "显示任务列表错误")
            self._send_message(room_id, "获取任务信息失败，请稍后再试", user_id)

    def refresh_daily_tasks(self, room_id, user_id):
        """刷新每日任务"""
        try:
            # 检查是否已有未完成的任务
            current_tasks = self.db.get_daily_tasks(room_id, user_id)
            if current_tasks:
                for task in current_tasks:
                    if not task['claimed']:
                        # 如果有未完成的任务，直接显示
                        self.show_tasks(room_id, user_id)
                        return

            # 定义所有可能的任务
            all_tasks = [
                # 钓鱼次数任务
                {
                    "name": "初级渔夫",
                    "type": "fishing",
                    "target": 10,
                    "reward": {"type": "coins", "amount": 10000},
                    "desc": "发送【抛竿】进行钓鱼，每次成功钓到鱼+1点进度，钓鱼失败不计入进度。需要完成10次成功钓鱼。"
                },
                {
                    "name": "中级渔夫",
                    "type": "fishing",
                    "target": 30,
                    "reward": {"type": "coins", "amount": 30000},
                    "desc": "发送【抛竿】进行钓鱼，每次成功钓到鱼+1点进度，钓鱼失败不计入进度。需要完成30次成功钓鱼。建议先升级鱼竿提高成功率。"
                },
                {
                    "name": "高级渔夫",
                    "type": "fishing",
                    "target": 50,
                    "reward": {"type": "coins", "amount": 50000},
                    "desc": "发送【抛竿】进行钓鱼，每次成功钓到鱼+1点进度，钓鱼失败不计入进度。需要完成50次成功钓鱼。建议先升级鱼竿提高成功率。"
                },

                # 稀有鱼任务
                {
                    "name": "稀有收集者",
                    "type": "rare_fish",
                    "target": 1,
                    "reward": {"type": "coins", "amount": 20000},
                    "desc": "需要钓到1条稀有或传说鱼。发送【升级鱼竿】提高稀有鱼概率，然后发送【抛竿】钓鱼，成功钓到稀有或传说鱼时+1点进度。"
                },
                {
                    "name": "珍稀猎手",
                    "type": "rare_fish",
                    "target": 3,
                    "reward": {"type": "coins", "amount": 60000},
                    "desc": "需要钓到3条稀有或传说鱼。发送【升级鱼竿】提高稀有鱼概率，然后发送【抛竿】钓鱼，成功钓到稀有或传说鱼时+1点进度。"
                },
                {
                    "name": "传说捕手",
                    "type": "rare_fish",
                    "target": 5,
                    "reward": {"type": "exp", "amount": 10000},
                    "desc": "需要钓到5条稀有或传说鱼。发送【升级鱼竿】提高稀有鱼概率，然后发送【抛竿】钓鱼，成功钓到稀有或传说鱼时+1点进度。"
                },

                # 收入任务
                {
                    "name": "小赚一笔",
                    "type": "income",
                    "target": 5000,
                    "reward": {"type": "coins", "amount": 10000},
                    "desc": "累计获得5000金币。通过钓鱼和出售鱼获得金币，发送【限时高价】查看高价鱼，卖出高价鱼可以获得更多收益。完成任务奖励和比赛奖励也计入进度。"
                },
                {
                    "name": "财富积累",
                    "type": "income",
                    "target": 20000,
                    "reward": {"type": "coins", "amount": 40000},
                    "desc": "累计获得20000金币。通过钓鱼和出售鱼获得金币，发送【限时高价】查看高价鱼，卖出高价鱼可以获得更多收益。完成任务奖励和比赛奖励也计入进度。"
                },
                {
                    "name": "富甲一方",
                    "type": "income",
                    "target": 50000,
                    "reward": {"type": "exp", "amount": 20000},
                    "desc": "累计获得50000金币。通过钓鱼和出售鱼获得金币，发送【限时高价】查看高价鱼，卖出高价鱼可以获得更多收益。完成任务奖励和比赛奖励也计入进度。"
                }
            ]

            # 随机选择一个任务
            task = random.choice(all_tasks)

            # 创建新任务
            if self.db.create_daily_task(room_id, user_id, task):
                logging.info(f"创建新任务 - 房间:{room_id} 用户:{user_id} "
                             f"任务:{task['name']} 类型:{task['type']}")
                # 显示新任务
                self.show_tasks(room_id, user_id)
            else:
                self._send_message(room_id, "创建任务失败，请稍后再试", user_id)

        except Exception as e:
            self._handle_exception(e, "刷新每日任务错误")
            self._send_message(room_id, "刷新任务失败，请稍后再试", user_id)

    def show_decorations(self, room_id, user_id):
        """显示玩家装饰"""
        try:
            # 获取玩家装饰
            decorations = self.db.get_decorations(room_id, user_id)

            # 获取玩家信息
            player = self.db.get_player_info(room_id, user_id)

            # 装饰类型到中文描述的映射
            buff_type_desc = {
                "exp_rate": "经验",
                "rare_rate": "稀有",
                "anti_steal": "防盗",
                "income_rate": "收入",
                "all_rate": "全局",
                "event_rate": "事件",
                "success_rate": "钓鱼"
            }

            # 获取玩家昵称
            # nickname = self.wcf.get_alias_in_chatroom(roomid=room_id, wxid=user_id)
            nickname = self.wcf.getGroupUerInfo(robotId='wxid_cbgpauriedg629', roomId=room_id, wxId=at_user)['nickname']

            # 构建消息

            if decorations:
                msg = (f"🏡 【{nickname} 的家园装饰】🏡\n\n")
                active_buffs = {}
                steal_rate = 0
                all_rate = 0
                for deco in decorations:
                    # 将buff类型转为更友好的描述
                    buff_desc = buff_type_desc.get(deco["buff_type"], deco["buff_type"])
                    if deco['name'] == '防盗门':
                        steal_rate = steal_rate + int(deco['buff_value'])
                    if deco['name'] == '黄金':
                        all_rate = int(deco['buff_value'])
                    msg += (
                        f"🎋 {deco['name']} Lv.{deco['level']}\n"
                        f"💫 {buff_desc}: +{int(deco['buff_value'] * 100)}%\n\n"
                    )

                    # 统计激活的buff
                    buff_type = deco["buff_type"]
                    if buff_type == "all_rate":
                        # 全局buff影响所有类型
                        for key in buff_type_desc.keys():
                            active_buffs[key] = active_buffs.get(key, 0) + deco['buff_value']
                    else:
                        active_buffs[buff_type] = active_buffs.get(buff_type, 0) + deco['buff_value']

                if player['dog_status'] == 1 and player['dog_level'] > 0:
                    user_dog = self._get_dog_info(player['dog_level'])
                    dog_rate = user_dog['anti_steal_rate']
                    steal_rate = steal_rate + dog_rate
                    msg += (
                        f"🐶 狗子等级 {player['dog_level']}\n"
                        f"💫 看门能力: +{dog_rate}%\n\n"
                    )
                # 添加buff总结部分
                if active_buffs:
                    msg += "✨ 当前激活的效果 ✨\n"
                    for buff_type, value in active_buffs.items():
                        if buff_type in buff_type_desc:
                            if buff_type == "anti_steal":
                                rate = steal_rate + all_rate
                                msg += f"{buff_type_desc[buff_type]}: +{rate}%\n"
                            else:
                                if buff_type == "all_rate":
                                    msg += f"{buff_type_desc[buff_type]}: +{int(value * 100)}%\n"
                                else:
                                    rate = float(value) + all_rate
                                    msg += f"{buff_type_desc[buff_type]}: +{int(rate * 100)}%\n"
                # 添加升级提示
                msg += (
                    "\n💡 发送「升+装饰名」升级\n"
                    "例如：升锦鲤池"
                )
            else:
                msg = "你还没有购买任何装饰哦~\n发送「装饰商店」查看可购买的装饰"

            self._send_message(room_id, msg, user_id)
        except Exception as e:
            self._handle_exception(e, "显示装饰信息出错")

    def show_deco_shop(self, room_id, user_id):
        """显示装饰商店"""
        try:
            # 装饰列表
            deco_list = {
                "锦鲤池": {
                    "price": 8000,
                    "buff_type": "经验加成",
                    "buff_value": 0.12,
                    "desc": "提升钓鱼获得的经验"
                },
                "珊瑚礁": {
                    "price": 12000,
                    "buff_type": "稀有概率",
                    "buff_value": 0.08,
                    "desc": "提升钓到稀有鱼的概率"
                },
                "防盗门": {
                    "price": 18000,
                    "buff_type": "防盗能力",
                    "buff_value": 0.15,
                    "desc": "降低被偷鱼的概率"
                },
                "幸运喷泉": {
                    "price": 25000,
                    "buff_type": "收入加成",
                    "buff_value": 0.12,
                    "desc": "提升卖鱼获得的金币"
                },
                "黄金地板": {
                    "price": 40000,
                    "buff_type": "全局加成",
                    "buff_value": 0.07,
                    "desc": "提升所有属性"
                }
            }

            msg = (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                "🏪 装饰商店 🏪\n\n"
            )

            for name, info in deco_list.items():
                msg += (
                    f"🎋 {name}\n"
                    f"💰 价格: {info['price']}金币\n"
                    f"📈 效果: {info['desc']}\n"
                    f"💫 加成: +{int(info['buff_value'] * 100)}%\n\n"
                )

            # 添加推荐信息
            msg += (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                "🔸 推荐购买顺序 🔸\n"
                "1️⃣ 初级玩家(1-15级): 锦鲤池、珊瑚礁\n"
                "2️⃣ 中级玩家(16-30级): 防盗门、幸运喷泉\n"
                "3️⃣ 高级玩家(31级+): 黄金地板\n"
                "╰┄··┄┄┄┄┄┄┄┄┄┄╯\n"
            )

            msg += (
                "╰┄··┄┄┄┄┄┄┄┄┄┄╯\n"
                "💡 发送「买装饰+名称」购买\n"
                "例如：买装饰锦鲤池"
            )

            self._send_message(room_id, msg, user_id)
        except Exception as e:
            self._handle_exception(e, "显示装饰商店出错")

    def buy_decoration(self, room_id, user_id, deco_name):
        """购买装饰"""
        try:
            # 修改装饰列表，与商店显示保持一致
            deco_list = {
                "锦鲤池": {
                    "price": 8000,
                    "buff_type": "经验加成",
                    "buff_value": 0.12,
                    "desc": "提升钓鱼获得的经验",
                    "db_buff_type": "exp_rate"  # 数据库中存储的buff类型
                },
                "珊瑚礁": {
                    "price": 12000,
                    "buff_type": "稀有概率",
                    "buff_value": 0.08,
                    "desc": "提升钓到稀有鱼的概率",
                    "db_buff_type": "rare_rate"
                },
                "防盗门": {
                    "price": 18000,
                    "buff_type": "防盗能力",
                    "buff_value": 0.15,
                    "desc": "降低被偷鱼的概率",
                    "db_buff_type": "anti_steal"
                },
                "幸运喷泉": {
                    "price": 25000,
                    "buff_type": "收入加成",
                    "buff_value": 0.12,
                    "desc": "提升卖鱼获得的金币",
                    "db_buff_type": "income_rate"
                },
                "黄金地板": {
                    "price": 40000,
                    "buff_type": "全局加成",
                    "buff_value": 0.07,
                    "desc": "提升所有属性",
                    "db_buff_type": "all_rate"
                }
            }

            if deco_name not in deco_list:
                self._send_message(room_id, "没有这个装饰哦~", user_id)
                return

            deco = deco_list[deco_name]
            if self.db.buy_decoration(room_id, user_id, deco_name, deco["price"], deco["db_buff_type"],
                                      deco["buff_value"]):
                msg = (
                    "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                    f"🎊 成功购买 {deco_name}!\n"
                    f"💰 花费: {deco['price']}金币\n"
                    f"📈 效果: {deco['desc']}\n"
                    f"💫 加成: +{int(deco['buff_value'] * 100)}%\n"
                    "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                )
            else:
                msg = "购买失败，金币不足~"

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "购买装饰错误")

    def visit_pond(self, room_id, user_id, target_id):
        """参观他人鱼塘"""
        try:
            # 获取目标玩家信息
            target_player = self.db.get_player_info(room_id, target_id)
            if not target_player:
                self._send_message(room_id, "该玩家还没有开始钓鱼游戏~", user_id)
                return

            # 获取装饰信息
            decorations = self.db.get_decorations(room_id, target_id)

            # 获取鱼塘信息
            fish_pond = self.db.get_fish_pond(room_id, target_id)

            # 获取玩家名称
            #nickname = self.wcf.get_alias_in_chatroom(roomid=room_id, wxid=target_id)
            nickname = self.wcf.getGroupUerInfo(robotId='wxid_cbgpauriedg629', roomId=room_id, wxId=target_id)['nickname']

            # buff类型中英文映射
            buff_names = {
                "exp_rate": "经验",
                "rare_rate": "稀有",
                "anti_steal": "防盗",
                "income_rate": "收入",
                "all_rate": "全局",
                "event_rate": "事件",
                "success_rate": "钓鱼"
            }

            msg = (
                f"🌊 【{nickname}的鱼塘】 Lv.{target_player['level']} 🌊\n\n"
            )

            # 显示装饰信息
            if decorations:
                msg += "🏡 家园装饰:\n"
                active_buffs = {}  # 用于统计激活的buff总效果

                for deco in decorations:
                    # 所有装饰都是激活的
                    buff_type = deco['buff_type']
                    buff_value = deco['buff_value']

                    if buff_type in active_buffs:
                        active_buffs[buff_type] += buff_value
                    else:
                        active_buffs[buff_type] = buff_value

                    # 显示装饰信息，包括等级和效果
                    buff_name = buff_names.get(deco['buff_type'], deco['buff_type'])
                    buff = f"{buff_name}+{int(deco['buff_value'] * 100)}%" if deco['buff_type'] else ""
                    msg += f"🎋 {deco['name']} Lv.{deco['level']} ({buff})\n"
            # 检查目标是否有旺财守护
            if target_player['dog_status'] == 1 and target_player['dog_level'] > 0:
                dog_info = self._get_dog_info(target_player['dog_level'])
                anti_steal_rate = dog_info['anti_steal_rate'] / 100
                msg += f"🐶 旺财守护: 防盗概率+{int(anti_steal_rate * 100)}%\n"
            msg += "\n"
            # 显示鱼塘信息
            if fish_pond:
                # 计算总鱼数和总价值
                total_fish = sum(fish['count'] for fish in fish_pond)
                total_value = sum(fish['price'] * fish['count'] for fish in fish_pond)

                # 找出最值钱的鱼
                most_valuable_fish = max(fish_pond, key=lambda x: x['price'], default=None)

                msg += f"📊 鱼塘鱼 {total_fish} 条，价值 {total_value} 金币\n"

                if most_valuable_fish and most_valuable_fish['price'] > 5000:
                    msg += f"💎 最珍贵的鱼:\n 🐠{most_valuable_fish['name']} ({most_valuable_fish['price']}金币)\n"

                # 展示部分鱼类
                if len(fish_pond) > 0:
                    msg += "\n🔍 部分鱼类:\n"
                    # 最多显示5种鱼
                    for fish in sorted(fish_pond, key=lambda x: x['price'], reverse=True)[:5]:
                        msg += f"🐠 {fish['name']} x{fish['count']} ({fish['price']}金币/条)\n"
            else:
                msg += "🔍 这个鱼塘里还没有鱼~\n"

            # 添加偷鱼提示
            msg += "\n💡 发送「偷鱼@玩家」偷Ta的鱼"

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "参观鱼塘错误")

    def join_competition(self, room_id, user_id):
        """参加钓鱼比赛"""
        try:
            # 检查是否有正在进行的比赛
            current_comp = self.db.get_current_competition(room_id)
            if not current_comp:
                self._send_message(room_id, "当前没有正在进行的比赛~", user_id)
                return

            # 检查比赛是否已经结束
            remaining_time = int((current_comp['end_time'] - datetime.now()).total_seconds())
            if remaining_time <= 0:
                self._send_message(room_id, "比赛已经结束啦~", user_id)
                return

            # 检查是否已经参加
            if self.db.is_in_competition(room_id, user_id):
                self._send_message(room_id, "你已经参加了比赛，快去钓鱼吧~", user_id)
                return

            # 加入比赛
            if self.db.join_competition(room_id, user_id):
                msg = (
                    "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                    "🎊 成功参加比赛!\n\n"
                    "💡 比赛规则:\n"
                    "- 比赛期间钓鱼收益翻倍\n"
                    "- 稀有鱼出现概率提升\n"
                    f"⏰ 剩余时间: {remaining_time // 60}分{remaining_time % 60}秒\n"
                    "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                )
                # 传入 user_id 以实现 @ 功能
                self._send_message(room_id, msg, user_id)
            else:
                msg = "参加比赛失败，请稍后再试~"
                self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "参加比赛错误")

    def show_competition_rank(self, room_id, user_id):
        """显示比赛排名"""
        try:
            comp_info = self.db.get_current_competition(room_id)
            if not comp_info:
                self._send_message(room_id, "当前没有正在进行的比赛~", user_id)
                return

            rank_info = self.db.get_competition_rank(room_id)
            remaining_time = int((comp_info['end_time'] - datetime.now()).total_seconds())

            if remaining_time <= 0:
                # 如果比赛已经结束但还未发放奖励，立即结束
                self.end_competition(room_id)
                return

            msg = (
                "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                "🏆 比赛排行榜 🏆\n\n"
            )
            members = self.wcf.getGroupMemberInfos(robotId='wxid_cbgpauriedg629', roomId=room_id).get('data')
            for idx, player in enumerate(rank_info[:10], 1):
                #name = self.wcf.get_alias_in_chatroom(roomid=room_id, wxid=player['user_id'])
                name = self.getNameFromMembers(player['user_id'], members)
                msg += f"第{idx}名 {name}: {player['score']}金币\n"

            msg += f"\n⏰ 剩余时间: {remaining_time // 60}分{remaining_time % 60}秒\n"
            msg += "╰┄┄┄┄┄┄┄┄┄┄··┄╯"

            self._send_message(room_id, msg, user_id)

        except Exception as e:
            self._handle_exception(e, "显示比赛排名错误")

    def _broadcast_message(self, msg):
        """广播消息到所有房间"""
        try:
            # 获取所有活跃房间
            active_rooms = self.db.get_active_rooms()
            for room_id in active_rooms:
                self._send_message(room_id, msg)
        except Exception as e:
            self._handle_exception(e, "广播消息错误")

    def cleanup(self):
        """清理资源"""
        try:
            # 保存统计数据
            self._save_stats()

            # 关闭数据库连接
            if hasattr(self, 'db'):
                self.db.close()

            logging.info("钓鱼系统资源已清理")
        except Exception as e:
            self._handle_exception(e, "清理资源错误")

    def _check_action_limit(self, room_id, user_id, action_type):
        """检查操作频率限制"""
        try:
            # 检查是否被封禁
            ban_remaining = self._get_ban_remaining(room_id, user_id)
            if ban_remaining > 0:
                minutes = ban_remaining // 60
                seconds = ban_remaining % 60
                time_str = f"{minutes}分{seconds}秒" if minutes > 0 else f"{seconds}秒"

                messages = [
                    f"😅 你这么频繁抛竿，鱼全吓走了！\n 再等{time_str}鱼再回来~",
                    f"⏰ 因为钓鱼太快鱼杆坏啦！{time_str}后恢复",
                    f"🐟 鱼儿们都被你吓跑啦！{time_str}后再来钓鱼吧~",
                    f"👮 你冷铺下，钓鱼要耐心慢慢抛竿等的，\n 你酱纸先去冷静{time_str}后再来钓鱼！"
                ]
                return False, random.choice(messages)

            key = f"{room_id}_{user_id}_{action_type}"
            current_time = time.time()

            # 初始化记录
            if key not in self.action_records:
                self.action_records[key] = []

            # 清理过期记录
            window = self.action_limits[action_type]["window"]
            self.action_records[key] = [t for t in self.action_records[key]
                                        if current_time - t < window]

            # 检查次数限制
            if len(self.action_records[key]) >= self.action_limits[action_type]["count"]:
                # 记录违规
                self._record_violation(room_id, user_id)

                # 计算需要等待的时间
                if self.action_records[key]:
                    # 找到最早的操作时间
                    earliest_time = min(self.action_records[key])
                    # 计算这个操作何时过期
                    expire_time = earliest_time + window
                    # 计算还需要等待多长时间
                    wait_time = max(0, expire_time - current_time)

                    # 格式化等待时间
                    if wait_time >= 60:
                        minutes = int(wait_time) // 60
                        seconds = int(wait_time) % 60
                        wait_str = f"{minutes}分{seconds}秒"
                    else:
                        wait_str = f"{int(wait_time)}秒"

                    # 根据操作类型提供不同的提示
                    action_names = {
                        "fishing": "钓鱼",
                        "steal_fish": "偷鱼",
                        "sell": "卖鱼",
                        "upgrade": "升级",
                        "buy": "购买",
                        "search_dog": "找狗"
                    }

                    action_name = action_names.get(action_type, "操作")

                    return False, f"⏰ {action_name}冷却中，冷却时间{wait_str}"
                else:
                    # 如果没有记录（理论上不应该发生），使用默认提示
                    return False, f"操作太频繁了，请{window}秒后再试"

            # 记录本次操作
            self.action_records[key].append(current_time)
            return True, None

        except Exception as e:
            self._handle_exception(e, "检查操作限制错误")
            return True, None

    def _get_ban_remaining(self, room_id, user_id):
        """获取剩余封禁时间（秒）"""
        try:
            key = f"{room_id}_{user_id}"
            if key in self.banned_users:
                record = self.banned_users[key]
                ban_end_time = record["last_time"] + record.get("ban_duration", 0)
                remaining = ban_end_time - time.time()
                return max(0, int(remaining))
            return 0
        except Exception as e:
            self._handle_exception(e, "获取封禁时间错误")
            return 0

    def _record_violation(self, room_id, user_id):
        """记录违规操作"""
        try:
            key = f"{room_id}_{user_id}"
            current_time = time.time()

            if key not in self.banned_users:
                self.banned_users[key] = {
                    "count": 0,
                    "last_time": 0,
                    "warning_count": 0,
                    "last_warning": 0
                }

            record = self.banned_users[key]

            # 如果距离上次违规超过5分钟，重置计数
            if current_time - record["last_time"] > 300:
                record["count"] = 0
                record["warning_count"] = 0

            record["count"] += 1
            record["last_time"] = current_time

            # 发送警告消息
            if current_time - record["last_warning"] > 10:  # 10秒内不重复发送警告
                #warning_msg = random.choice(self.violation_messages)
                #self._send_message(room_id, warning_msg, user_id)
                record["warning_count"] += 1
                record["last_warning"] = current_time

            # 根据违规次数增加封禁时间
            if record["count"] >= 8:
                # 修改为固定2分钟封禁
                ban_duration = 120  # 2分钟 = 120秒
                self._ban_user(room_id, user_id, ban_duration)

                ban_messages = [
                    f"😅 这么频繁抛竿不累么？\n休息2分钟再钓鱼吧~",
                    f"👮 你练习一阳指么？ \n 先休息2分钟吧！",
                    f"⚠️ 系统怀疑你用外挂了，强制休息2分钟~",
                    f"🎣 让鱼儿们休息2分钟，一会儿再来钓吧~"
                ]

                self._send_message(room_id, random.choice(ban_messages), user_id)

                # 记录严重违规
                # self._log_serious_violation(room_id, user_id, record["count"])

        except Exception as e:
            self._handle_exception(e, "记录违规操作错误")

    def _is_banned(self, room_id, user_id):
        """检查是否被封禁"""
        key = f"{room_id}_{user_id}"
        if key in self.banned_users:
            record = self.banned_users[key]
            if time.time() - record["last_time"] < record.get("ban_duration", 0):
                return True
        return False

    def _ban_user(self, room_id, user_id, duration):
        """封禁用户"""
        key = f"{room_id}_{user_id}"
        if key not in self.banned_users:
            self.banned_users[key] = {}
        self.banned_users[key]["ban_duration"] = duration

    def _init_competition_timer(self):
        """初始化比赛定时器"""
        try:
            def check_competition():
                while True:
                    try:
                        # 获取所有需要结束的比赛
                        competitions = self.db.get_ending_competitions()
                        for comp in competitions:
                            self.end_competition(comp['room_id'])
                        time.sleep(30)  # 每30秒检查一次
                    except Exception as e:
                        self._handle_exception(e, "比赛定时器错误")
                        time.sleep(60)  # 出错后等待1分钟再试

            # 启动比赛检查线程
            Thread(target=check_competition, daemon=True).start()
        except Exception as e:
            self._handle_exception(e, "初始化比赛定时器错误")

    def admin_give_coins(self, room_id, admin_id, target_id, amount):
        """管理员送金币"""
        try:
            result = self.db.admin_give_coins(admin_id, room_id, target_id, amount)
            if result["success"]:
                self._send_message(room_id, f"🎁 {result['message']}", target_id)
            else:
                self._send_message(room_id, f"❌ {result['message']}", admin_id)
        except Exception as e:
            self._handle_exception(e, "送金币错误")

    def update_task_progress(self, room_id, user_id, task_type, progress):
        """更新任务进度"""
        try:
            # 获取当前任务
            tasks = self.db.get_daily_tasks(room_id, user_id)
            if not tasks:
                logging.info(f"用户没有进行中的任务 - 房间:{room_id} 用户:{user_id}")
                return False

            task = tasks[0]  # 获取当前唯一的任务

            # 验证任务类型
            if task['task_type'] != task_type:
                logging.info(f"任务类型不匹配 - 期望:{task['task_type']}, 实际:{task_type}")
                return False

            # 如果任务已完成且已领取奖励，不再更新进度
            if task['claimed']:
                logging.info(f"任务已完成并领取奖励 - 房间:{room_id} 用户:{user_id}")
                return False

            # 更新进度
            current_progress = task['progress']
            new_progress = min(current_progress + progress, task['target'])  # 确保不超过目标值

            # 记录进度更新
            logging.info(f"任务进度更新 - 房间:{room_id} 用户:{user_id} 类型:{task_type} "
                         f"原进度:{current_progress} 增加:{progress} 新进度:{new_progress}")

            # 更新数据库中的进度
            if self.db.update_task_progress_db(room_id, user_id, task_type, new_progress):
                # 检查任务是否完成
                if new_progress >= task['target'] and not task['claimed']:
                    self._handle_task_completion(room_id, user_id, task)
                return True
            return False

        except Exception as e:
            self._handle_exception(e, "更新任务进度错误")
            return False

    def _handle_task_completion(self, room_id, user_id, task):
        """处理任务完成"""
        try:
            # 标记任务完成并发放奖励
            if self.db.mark_task_completed(room_id, user_id, task['task_type']):
                # 根据奖励类型发放奖励
                reward_type = task['reward_type']
                reward_amount = task['reward_amount']

                if reward_type == 'coins':
                    self.db.add_coins(room_id, user_id, reward_amount)
                    # 更新收入任务进度（如果有），但避免递归调用
                    if task['task_type'] != "income":
                        self.db.update_task_progress(room_id, user_id, "income", reward_amount)
                elif reward_type == 'exp':
                    self.db.batch_add_exp(room_id, user_id, reward_amount)

                # 发送任务完成消息
                msg = (
                    "╭┄··┄┄┄┄┄┄┄┄┄┄╮\n"
                    "🎊 任务完成! 🎊\n\n"
                    f"📋 任务：{task['name']}\n"
                    f"🎁 奖励：{reward_amount}{'金币' if reward_type == 'coins' else '经验'}\n"
                    "╰┄┄┄┄┄┄┄┄┄┄··┄╯"
                )
                self._send_message(room_id, msg, user_id)

                logging.info(f"任务完成奖励发放 - 房间:{room_id} 用户:{user_id} "
                             f"类型:{task['task_type']} 奖励:{reward_amount}{reward_type}")
                return True
            return False

        except Exception as e:
            self._handle_exception(e, "处理任务完成错误")
            return False

    def get_task_progress(self, room_id, user_id):
        """查询任务进度"""
        """查询任务进度"""
        try:
            return self.db.get_task_progress(room_id, user_id)
        except Exception as e:
            self._handle_exception(e, "查询任务进度错误")
            return []

    def _calculate_farming_bonus(self, catch_time, base_price):
        """计算养殖奖励

        Args:
            catch_time: 捕获时间
            base_price: 基础价格

        Returns:
            int: 养殖奖励金额
        """
        try:
            if not catch_time:
                return 0

            # 将字符串时间转换为datetime对象
            try:
                catch_time = datetime.fromisoformat(catch_time)
            except (ValueError, TypeError):
                return 0

            # 计算养殖时间（秒）
            now = datetime.now()
            farming_seconds = (now - catch_time).total_seconds()

            # 获取养殖配置
            farming_config = self.config.get("fish_farming", {})

            # 确定养殖时间对应的奖励比率
            bonus_rate = 0
            for config_key, config_value in farming_config.items():
                time_limit, rate = config_value.split("-")
                time_limit = int(time_limit)
                rate = float(rate)

                if farming_seconds >= time_limit and rate > bonus_rate:
                    bonus_rate = rate

            # 计算奖励金额
            bonus_amount = int(base_price * bonus_rate)
            return bonus_amount

        except Exception as e:
            self._handle_exception(e, "计算养殖奖励错误")
            return 0

    def _invalidate_player_cache(self, room_id, user_id):
        """使玩家相关的缓存失效"""
        self.player_cache.invalidate(f"{room_id}_{user_id}_info")
        self.fish_pond_cache.invalidate(f"{room_id}_{user_id}_pond")
        self.inventory_cache.invalidate(f"{room_id}_{user_id}_inventory")
import Config.ConfigServer as Cs
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from Plugins import *
from loguru import logger
from threading import Thread
import os


class FishGame(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "西子江湖游戏"
        self.author = "凌封"
        self.version = '1.0.0'
        self.description = "以杭州西湖的历史文化为背景，融合白蛇传、雷峰塔等传统故事元素的文字RPG游戏。"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.RoleConfig= Cs.returnConfigData().get('RoleConfig')
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.GameSession = {}

    @on_group_at
    def handleRoomMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        wxName = msgData.get('finalFromNickName')
        atWxIdList = msgData.get('atWxIdList')
        content = msgData.get('message').strip()
        logger.warning(f"[-]钓鱼系统处理消息: {content}")

        #不是游戏群的，直接return掉
        if roomId not in self.RoleConfig['xizi']['roomIds']:
            return
        #判断 GameSession有没有sender记录
        #1.1如果有
        #1.1.1 调用游戏
        # 1.1.2 根据configData里面获到exitGame列表，判断content是不是在列表内，如果是则清理掉GameSession里面sender记录
        #1.2如果没有，根据configData里面获到enterGame列表，判断content是不是在列表内，则增加sender到GameSession里面，然后调用游戏



    @on_group_text
    def handleRoomTextMsg(self, message: dict):
        """ 群聊文本事件处理 """

        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        wxName = msgData.get('finalFromNickName')
        atWxIdList = msgData.get('atWxIdList')
        content = msgData.get('message').strip()
        logger.warning(f"[-]钓鱼系统处理消息: {content}")
        #不是钓鱼群的，直接return掉
        if roomId not in self.RoleConfig['xizi']['roomIds']:
            return

    #@on_group_red_packet
    def handleRoomRedPacketMsg(self, message: dict):
        """ 群聊红包事件处理 """

    @on_group_at_robot
    def handleRoomAtRobot(self, message: dict):
        """ 群聊@机器人事件处理 """

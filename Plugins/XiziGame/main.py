import Config.ConfigServer as Cs
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from Plugins import *
from loguru import logger
from threading import Thread
import os


class XiziGame(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "西子江湖游戏"
        self.author = "凌封"
        self.version = '1.0.0'
        self.description = "以杭州西湖的历史文化为背景，融合白蛇传、雷峰塔等传统故事元素的文字RPG游戏。"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.RoleConfig= Cs.returnConfigData().get('RoleConfig')
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.GameSession = {}

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_at
    def handleRoomMsg(self, message: dict):
        self._handleMsg(message)

    @on_group_text
    def handleRoomTextMsg(self, message: dict):
        self._handleMsg(message)

    def _handleMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        wxName = msgData.get('finalFromNickName')
        atWxIdList = msgData.get('atWxIdList')
        content = msgData.get('message').strip()
        logger.warning(f"[-]西子江湖游戏系统处理消息: {content}")

        # 不是游戏群的，直接return掉
        if roomId not in self.RoleConfig['xizi']['roomIds']:
            return

        # 获取配置中的进入和退出游戏关键词
        enterGameWords = self.configData.get('enterGame', [])
        exitGameWords = self.configData.get('exitGame', [])

        # 判断 GameSession有没有sender记录
        if sender in self.GameSession:
            # 1.1 如果有记录，说明用户已在游戏中
            # 1.1.2 判断是否要退出游戏
            if self.tools.judgeEqualListWord(content, exitGameWords):
                # 清理GameSession里面sender记录
                del self.GameSession[sender]
                self.bot.sendText(robotId=robotId, receive=roomId, message=f"@{wxName} 您已退出西子江湖游戏！", aters=f'{sender}')
                logger.info(f"用户 {wxName}({sender}) 退出游戏")
                return
            # 1.1.1 调用游戏逻辑处理
            self._handleGameLogic(robotId, roomId, sender, wxName, atWxIdList, content)

        else:
            # 1.2 如果没有记录，判断是否要进入游戏
            if self.tools.judgeEqualListWord(content, enterGameWords):
                # 增加sender到GameSession里面
                self.GameSession[sender] = {
                    'roomId': roomId,
                    'wxName': wxName,
                    'startTime': self.tools.getCurrentTime(),
                    'gameState': 'started'  # 游戏状态
                }
                logger.info(f"用户 {wxName}({sender}) 进入游戏")
                # 调用游戏初始化逻辑
                self._handleGameLogic(robotId, roomId, sender, wxName, atWxIdList, content)

    def _handleGameLogic(self, robotId, roomId, sender, wxName, atWxIdList, content):
        """
        处理游戏逻辑
        :param robotId: 机器人ID
        :param roomId: 群ID
        :param sender: 发送者ID
        :param wxName: 发送者昵称
        :param content: 消息内容
        """
        logger.info(f"处理游戏逻辑 - 用户: {wxName}, 指令: {content}")

        
        # TODO: 这里实现具体的游戏逻辑
        # POST请求，URL地址：http://127.0.0.1:8000/api/command/execute
        # body:
        """
        {
            "rawText": content,
            "userId": sender,
            "userName": wxName,
            "appId":0,
            "openId": roomId,
            "gameMode":true
        }
        """

        # 响应结果数据结构如下：
        """
        {
        	"success": true,
        	"code": "200",
        	"message": "操作成功",
        	"data": "⚡ 江湖风云突变，系统出现了一些问题！\n🙏 请大侠稍后再试，或联系客服小二\n💫 您的数据都很安全，请放心！"
        }
        """
        # 拿到data数据，发送给用户




    #@on_group_red_packet
    def handleRoomRedPacketMsg(self, message: dict):
        """ 群聊红包事件处理 """

    @on_group_at_robot
    def handleRoomAtRobot(self, message: dict):
        """ 群聊@机器人事件处理 """
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        wxName = msgData.get('finalFromNickName')
        atWxIdList = msgData.get('atWxIdList')
        content = msgData.get('message').strip()
        logger.warning(f"[-]西子江湖游戏系统处理@机器人消息: {content}")

        # 不是游戏群的，直接return掉
        if roomId not in self.RoleConfig['xizi']['roomIds']:
            return

        # 处理@消息，去掉@部分
        content = self.tools.returnNoAtMsg(atWxIdList, content, {})

        # 获取配置中的进入和退出游戏关键词
        enterGameWords = self.configData.get('enterGame', [])
        exitGameWords = self.configData.get('exitGame', [])

        # 如果不是游戏指令，提供帮助信息
        help_msg = f"@{wxName} 西子江湖游戏帮助：\n\n"
        help_msg += "🎮 进入游戏：" + "、".join(enterGameWords[:3]) + "...\n"
        help_msg += "🚪 退出游戏：" + "、".join(exitGameWords) + "\n\n"
        help_msg += "在游戏中，您可以体验以杭州西湖为背景的江湖世界！"
        self.bot.sendText(robotId=robotId, receive=roomId, message=help_msg, aters=f'{sender}')

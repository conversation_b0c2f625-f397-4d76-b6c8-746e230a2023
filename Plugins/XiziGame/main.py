import Config.ConfigServer as Cs
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from Plugins import *
from loguru import logger
from threading import Thread
import os
import requests
import json


class XiziGame(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "西子江湖游戏"
        self.author = "凌封"
        self.version = '1.0.0'
        self.description = "以杭州西湖的历史文化为背景，融合白蛇传、雷峰塔等传统故事元素的文字RPG游戏。"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.RoleConfig= Cs.returnConfigData().get('RoleConfig')
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.NGCBotConfig = Cs.returnConfigData().get('NGCBotConfig')
        self.Administrators = self.NGCBotConfig.get('Administrators')
        self.GameSession = {}
        self.Area = {}

        # 游戏API配置
        self.game_api_base_url = "http://localhost:8080/api"  # 根据实际情况修改
        self.game_api_timeout = 10  # 请求超时时间

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_at
    def handleRoomMsg(self, message: dict):
        self._handleMsg(message)

    @on_group_text
    def handleRoomTextMsg(self, message: dict):
        self._handleMsg(message)

    def _loadArea(self):

    def _handleMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        wxName = msgData.get('finalFromNickName')
        atWxIdList = msgData.get('atWxIdList')
        content = msgData.get('message').strip()
        logger.warning(f"[-]西子江湖游戏系统处理消息: {content}")

        # 是机器人本人，忽略消息
        if robotId ==sender:
            return

        if sender in self.Administrators and content=='加载分区':
            self._handleGameLogic(robotId, roomId, sender, wxName, content, atWxIdList)
            return
        # 不是游戏群的，直接return掉
        if roomId not in self.Area.keys():
            return

        #if roomId not in self.RoleConfig['xizi']['roomIds']:
        #    return

        # 获取配置中的进入和退出游戏关键词
        enterGameWords = self.configData.get('enterGame', [])
        exitGameWords = self.configData.get('exitGame', [])

        # 判断 GameSession有没有sender记录
        if sender in self.GameSession:
            # 1.1 如果有记录，说明用户已在游戏中
            # 1.1.2 判断是否要退出游戏
            if self.tools.judgeEqualListWord(content, exitGameWords):
                # 清理GameSession里面sender记录
                del self.GameSession[sender]
                self.bot.sendText(robotId=robotId, receive=roomId, message="\n 您已退出西子江湖游戏！", aters=sender)
                logger.info(f"用户 {wxName}({sender}) 退出游戏")
                return
            # 1.1.1 调用游戏逻辑处理
            self._handleGameLogic(robotId, roomId, sender, wxName, content, atWxIdList)

        else:
            # 1.2 如果没有记录，判断是否要进入游戏
            if self.tools.judgeEqualListWord(content, enterGameWords):
                # 增加sender到GameSession里面
                self.GameSession[sender] = {
                    'roomId': roomId,
                    'wxName': wxName,
                    'startTime': self.tools.getCurrentTime(),
                    'gameState': 'started'  # 游戏状态
                }
                self.bot.sendText(robotId=robotId, receive=roomId, message=f"\n 欢迎进入西子江湖！\n\n在这个以杭州西湖为背景的江湖世界中，您将体验白蛇传、雷峰塔等传统故事元素。\n\n游戏已开始，请发送您的指令...", aters=sender)
                logger.info(f"用户 {wxName}({sender}) 进入游戏")
                # 调用游戏初始化逻辑
                self._handleGameLogic(robotId, roomId, sender, wxName, content, atWxIdList)

    def _handleGameLogic(self, robotId, roomId, sender, wxName, content, atWxIdList):
        """
        处理游戏逻辑
        :param robotId: 机器人ID
        :param roomId: 群ID
        :param sender: 发送者ID
        :param wxName: 发送者昵称
        :param content: 消息内容
        """
        logger.info(f"处理游戏逻辑 - 用户: {wxName}, 指令: {content}")

        try:
            # 调用游戏API处理逻辑
            response = self._callGameApi(sender, wxName, content, atWxIdList, roomId)

            if response and response.get('success'):
                # 游戏API返回成功
                message = response.get('data', '')
                if content=='加载分区':
                    self.Area = JSON.loads(message)

                # 发送游戏响应消息
                if message:
                    self.bot.sendText(robotId=robotId, receive=roomId, message=f"\n{message}", aters=sender)

                logger.info(f"游戏API调用成功 - 用户: {wxName}")

            else:
                # 游戏API返回失败或无响应
                error_msg = response.get('message', '游戏服务暂时不可用') if response else '游戏服务连接失败'
                self.bot.sendText(robotId=robotId, receive=roomId, message=f"\n {error_msg}", aters=sender)
                logger.warning(f"游戏API调用失败 - 用户: {wxName}, 错误: {error_msg}")

        except Exception as e:
            logger.error(f"处理游戏逻辑出错: {e}")
            self.bot.sendText(robotId=robotId, receive=roomId, message=f"\n 游戏系统出现错误，请稍后再试", aters=sender)

    def _callGameApi(self, userId, userName, rawText, atWxIdList, openId):
        """
        调用游戏API
        :param userId: 用户ID
        :param userName: 用户名
        :param rawText: 原始文本指令
        :param openId: 群ID
        :return: API响应结果
        """
        try:
            # 构建请求数据，按照API文档格式
            request_data = {
                "rawText": rawText,
                "userId": userId,
                "userName": userName,
                "appId": 0,
                "openId": openId,
                "atUsers": atWxIdList,
                "gameMode": True
            }

            # 游戏API地址
            url = "http://127.0.0.1:8080/api/command/execute"
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'XiziGame-Bot/1.0'
            }

            logger.info(f"调用游戏API: {url}")
            logger.debug(f"请求数据: {request_data}")

            response = requests.post(
                url=url,
                json=request_data,
                headers=headers,
                timeout=10  # 10秒超时
            )

            if response.status_code == 200:
                result = response.json()
                logger.debug(f"游戏API响应: {result}")
                return result
            else:
                logger.error(f"游戏API请求失败: HTTP {response.status_code}")
                return {
                    "success": False,
                    "message": f"游戏服务响应异常 (HTTP {response.status_code})"
                }

        except requests.exceptions.Timeout:
            logger.error("游戏API请求超时")
            return {
                "success": False,
                "message": "游戏服务响应超时，请稍后再试"
            }
        except requests.exceptions.ConnectionError:
            logger.error("游戏API连接失败")
            return {
                "success": False,
                "message": "无法连接到游戏服务器，请确保游戏服务正在运行"
            }
        except json.JSONDecodeError as e:
            logger.error(f"游戏API响应格式错误: {e}")
            return {
                "success": False,
                "message": "游戏服务响应格式错误"
            }
        except Exception as e:
            logger.error(f"调用游戏API出错: {e}")
            return {
                "success": False,
                "message": "游戏服务调用失败"
            }
        # 拿到data数据，发送给用户


    #@on_group_red_packet
    def handleRoomRedPacketMsg(self, message: dict):
        """ 群聊红包事件处理 """

    @on_group_at_robot
    def handleRoomAtRobot(self, message: dict):
        """ 群聊@机器人事件处理 """
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        wxName = msgData.get('finalFromNickName')
        atWxIdList = msgData.get('atWxIdList')
        content = msgData.get('message').strip()
        logger.warning(f"[-]西子江湖游戏系统处理@机器人消息: {content}")

        # 不是游戏群的，直接return掉
        if roomId not in self.RoleConfig['xizi']['roomIds']:
            return

        # 处理@消息，去掉@部分
        content = self.tools.returnNoAtMsg(atWxIdList, content, {})

        # 获取配置中的进入和退出游戏关键词
        enterGameWords = self.configData.get('enterGame', [])
        exitGameWords = self.configData.get('exitGame', [])

        # 判断 GameSession有没有sender记录
        if sender in self.GameSession:
            # 如果有记录，说明用户已在游戏中
            # 判断是否要退出游戏
            if self.tools.judgeEqualListWord(content, exitGameWords):
                # 清理GameSession里面sender记录
                del self.GameSession[sender]
                self.bot.sendText(robotId=robotId, receive=roomId, message=f"@{wxName} 您已退出西子江湖游戏！", aters=sender)
                logger.info(f"用户 {wxName}({sender}) 退出游戏")
                return

            # 调用游戏逻辑处理
            self._handleGameLogic(robotId, roomId, sender, wxName, content, atWxIdList)

        else:
            # 如果没有记录，判断是否要进入游戏
            if self.tools.judgeEqualListWord(content, enterGameWords):
                # 增加sender到GameSession里面
                self.GameSession[sender] = {
                    'roomId': roomId,
                    'wxName': wxName,
                    'startTime': self.tools.getCurrentTime(),
                    'gameState': 'started'  # 游戏状态
                }
                self.bot.sendText(robotId=robotId, receive=roomId, message=f"@{wxName} 欢迎进入西子江湖！\n\n在这个以杭州西湖为背景的江湖世界中，您将体验白蛇传、雷峰塔等传统故事元素。\n\n游戏已开始，请发送您的指令...", aters=sender)
                logger.info(f"用户 {wxName}({sender}) 进入游戏")

                # 调用游戏初始化逻辑
                self._handleGameLogic(robotId, roomId, sender, wxName, "开始游戏",atWxIdList)
            else:
                # 如果不是游戏指令，提供帮助信息
                help_msg = f"@{wxName} 西子江湖游戏帮助：\n\n"
                help_msg += "🎮 进入游戏：" + "、".join(enterGameWords[:3]) + "...\n"
                help_msg += "🚪 退出游戏：" + "、".join(exitGameWords) + "\n\n"
                help_msg += "在游戏中，您可以体验以杭州西湖为背景的江湖世界！"
                self.bot.sendText(robotId=robotId, receive=roomId, message=help_msg, aters=sender)

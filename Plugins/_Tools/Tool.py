import tomlkit
import os
import yaml


class Tool:
    def __init__(self):
        pass

    def returnConfigData(self, pluginPath):
        """
        返回配置文件信息
        :param pluginPath:
        :return:
        """

        with open(os.path.join(pluginPath, 'config.toml'), mode='r', encoding='UTF-8') as f:
            data = tomlkit.load(f)
        return data
    def returnConfigDataWithYaml(self, pluginPath):
        """
        返回配置信息
        :param pluginPath:
        :return:
        """
        configData = yaml.load(open(os.path.join(pluginPath, 'config.yaml'), mode='r', encoding='UTF-8'), yaml.Loader)
        return configData

    def returnNoAtMsg(self, atWxIdList, content, groupMemberInfos):
        """ 处理@消息 返回没有@的消息 """
        for atWxId in atWxIdList:
            nickName = groupMemberInfos.get(atWxId, {}).get('nickname', '')
            if nickName:
                # 尝试替换多种可能的 @+昵称 组合
                patterns = [
                    f'@{nickName} ',  # 普通空格
                    f'@{nickName}\u3000',  # 全角空格
                    f'@{nickName}',  # 无空格
                ]
                for pattern in patterns:
                    content = content.replace(pattern, '')
            if '@' in content and ' ' in content:
                cList = content.replace('\u2005', ' ').replace('\u3000', '').split(' ')
                cList = [cl for cl in cList if not cl.startswith('@')]
                cList = [cl for cl in cList if cl]
                content = ' '.join(cList)
            print(content)
        return content.strip()

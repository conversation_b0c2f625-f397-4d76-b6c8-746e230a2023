class JudgeTools:
    def __init__(self):
        pass

    def judgeOneEqualListWord(self, recvWord, systemListWord):
        """
        判断接收消息前面几个字是否跟 触发关键词列表中的相匹配
        :param recvWord:
        :param systemListWord:
        :return:
        """
        for systemWord in systemListWord:
            if recvWord.startswith(systemWord):
                return True
        return False

    def judgeEqualWord(self, recvWord, systemWord):
        """
        判断接收消息和触发关键字完全相同则返回True
        接收消息 == 触发关键字
        :param recvWord: 接收消息
        :param systemWord: 触发关键字
        :return:
        """
        if recvWord.strip() == systemWord.strip():
            return True
        return False

    def judgeEqualListWord(self, recvWord, systemListWord):
        """
        判断接收消息在触发关键字列表中则返回True
        接收消息 in ['触发关键字列表']
        :param recvWord: 接收消息
        :param systemListWord: 触发关键字列表
        :return:
        """
        for listWord in systemListWord:
            if listWord.strip() == recvWord.strip():
                return True
        return False

    def judgeInWord(self, recvWord, systemListWord):
        """
        判断接收消息在触发关键字中则返回True
        接收消息 in 触发关键字
        :param recvWord:
        :param systemListWord:
        :return:
        """
        for systemWord in systemListWord:
            if systemWord in recvWord:
                return True
            return False

    def judgeInListWord(self, recvWord, systemListWord):
        """
        判断触发关键词列表中每一个关键字在接收消息中则返回True
        :param recvWord:
        :param systemListWord:
        :return:
        """
        for listWord in systemListWord:
            if listWord in recvWord:
                return True
        return False

    def judgeSplitAllEqualWord(self, recvWord, systemListWord):
        """
        接收消息以空格切割，判断第一个元素是否在触发关键字列表中则返回True
        :param recvWord:
        :param systemListWord:
        :return:
        """
        if ' ' in recvWord:
            recvWord = recvWord.split(' ')[0]
            for listWord in systemListWord:
                if recvWord == listWord:
                    return True
            return False
        return False

    def judgePointFunction(self, senderPoint, functionPoint):
        """
        判断用户积分是否大于功能积分
        :param senderPoint:
        :param functionPoint:
        :return:
        """
        if int(senderPoint) >= int(functionPoint):
            return True

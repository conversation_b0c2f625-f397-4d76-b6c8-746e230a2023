from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import os


class AutoAgreeFriend(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "自动同意好友"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "自动同意好友插件,可在配置文件中开关"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_friend_request
    def handleNewFriend(self, message: dict):
        """ 新朋友申请消息处理 """
        msgData = message.get('data')
        robotId = msgData.get('robotId')
        nickname = msgData.get('nickname')
        fromWxId = msgData.get('fromWxId')
        scene = msgData.get('scene')
        v3 = msgData.get('v3')
        v4 = msgData.get('v4')
        logger.info(f'收到 [{nickname}] 的好友申请, 已自动同意！')
        self.bot.acceptFriend(robotId=robotId, v3=v3, v4=v4, scene=scene)
        self.bot.sendText(robotId=robotId, receive=fromWxId, message="👋 大佬你好，很高兴认识你！😊 我是凌封的助理小机灵 🤖\n\n📚 进AI学习群回复我：AI全书 \n\n🎬 进电影派群回我：电影派\n\n⚠️ 注意：进电影派需要提供我厂工号哦~ 💼")

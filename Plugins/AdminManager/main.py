import Config.ConfigServer as Cs
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import os


class AdminManager(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "管理员管理"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "管理员管理插件"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.NGCBotConfig = Cs.returnConfigData().get('NGCBotConfig')
        self.Administrators = self.NGCBotConfig.get('Administrators')
        self.adminConfig = self.configData.get('AdminConfig')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_at
    def handleGroupAtMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        if sender == robotId:
            return
        if sender not in self.Administrators:
            return
        content = msgData.get('message').strip()
        atWxIdList = msgData.get('atWxIdList')
        groupMemberInfos = self.bot.getGroupMemberInfos(robotId=robotId, roomId=roomId).get('data')
        noAtMsg = self.tools.returnNoAtMsg(atWxIdList=atWxIdList, content=content, groupMemberInfos=groupMemberInfos)
        self.handleAdmin(robotId, roomId, atWxIdList, noAtMsg)

    def handleAdmin(self, robotId, roomId, atWxIdList, noAtMsg):
        try:
            for sender in atWxIdList:
                if noAtMsg in self.adminConfig.get('addAdminSymbol'):
                    if self.tools.queryAdmin(roomId=roomId, wxId=sender):
                        self.bot.sendText(robotId=robotId, receive=roomId, message=f'您当前已是管理员, 无需重复添加!',
                                          aters=f'{sender}')
                        return
                    if self.tools.addAdmin(roomId=roomId, wxId=sender):
                        self.bot.sendText(robotId=robotId, receive=roomId, message=f'已添加您为当前群聊管理员!',
                                          aters=f'{sender}')
                    else:
                        self.bot.sendText(robotId=robotId, receive=roomId,
                                          message=f'添加管理员出现错误, 请联系超管进行处理!', aters=f'{sender}')
                if noAtMsg in self.adminConfig.get('delAdminSymbol'):
                    if self.tools.queryAdmin(roomId=roomId, wxId=sender):
                        if self.tools.delAdmin(roomId=roomId, wxId=sender):
                            self.bot.sendText(robotId=robotId, receive=roomId,
                                              message=f'基于你的表现, 你已不适合担任管理员, 现将你移出!',
                                              aters=f'{sender}')
                            return
                        else:
                            self.bot.sendText(robotId=robotId, receive=roomId,
                                              message=f'删除管理员出现错误, 请联系超管进行处理!', aters=f'{sender}')
                    else:
                        self.bot.sendText(robotId=robotId, receive=roomId, message=f'你当前不是管理员, 无需重复移出!',
                                          aters=f'{sender}')
        except Exception as e:
            logger.error(f'管理插件出现错误, 错误信息: {e}')
            # self.bot.sendText(robotId=robotId, receive=roomId, message=f'积分管理插件出现错误, 错误信息: {e}')

from FileCache.FileCacheServer import returnVideoCacheFolder
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import requests
import random
import time
import os


class RandomGirlVideo(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "美女视频插件"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "随机发送美女视频"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.girlApis = self.configData.get('GrilVideoApi')
        self.girlVideoWords = self.configData.get('VideoWords')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_text
    def handleGroupMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        content = msgData.get('message')
        if content not in self.girlVideoWords:
            return
        girlVideoPath = self.downloadGrilVideo()
        if not girlVideoPath:
            self.bot.sendText(robotId=robotId, receive=roomId, message=f'{self.name} 下载美女视频出错, 请稍后重试~',
                              aters=f'{sender}')
            return
        else:
            self.bot.sendText(robotId=robotId, receive=roomId, message=f'🕒 正在发送视频中, 请稍等... ...',
                              aters=f'{sender}')
            self.bot.sendMedium(robotId=robotId, receive=roomId, mediumPath=girlVideoPath)

    def downloadGrilVideo(self, ):
        try:
            girlVideoApi = random.choice(self.girlApis)
            girlVideoContent = requests.get(girlVideoApi).content
            savePath = f'{returnVideoCacheFolder()}/{int(time.time())}.mp4'
            with open(savePath, mode='wb') as f:
                f.write(girlVideoContent)
            return savePath
        except Exception as e:
            logger.warning(f'{self.name} 下载美女视频出现错误, 错误信息: {e}')
            return ''

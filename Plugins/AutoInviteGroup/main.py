from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import os


class AutoInviteGroup(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "自动邀请进群"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "自动邀请进群, 可在配置文件设置开关"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.inviteGroupDicts = self.configData.get('inviteGroupWords')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_private_text(priority=999)
    def handleInviteGroup(self, message: dict):
        """ 自动邀请进群 """
        msgData = message.get('data')
        senderId = msgData.get('fromWxId')
        robotId = msgData.get('robotId')
        content = msgData.get('message').strip()
        for inviteGroupWord, roomLists in self.inviteGroupDicts.items():
            if inviteGroupWord in content:
                for roomId in roomLists:
                    groupInfo = self.bot.getGroupInfo(robotId=robotId, roomId=roomId)
                    groupData = groupInfo.get('data')
                    groupMemberNum = groupData.get('groupMemberNum')
                    chatRoomInfoDicts = groupData.get('chatRoomInfoDicts')
                    if senderId in chatRoomInfoDicts.keys():
                        self.bot.sendText(robotId=robotId, receive=senderId, message='你小子已经进群了[旺柴]  还想咋滴！')
                        logger.info(f"[AutoInviteGroup] 用户 {senderId} 已在群中，阻止事件传播")
                        return False
                    if groupMemberNum < 500:
                        self.bot.inviteMember(robotId=robotId, roomId=roomId, memberWxId=senderId)
                        logger.info(f"[AutoInviteGroup] 邀请用户 {senderId} 进群 {roomId} 成功，阻止事件传播")
                        return False
                continue
        return None

from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import os
import re
import Config.ConfigServer as Cs


class WelcomeToGroup(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "进群欢迎"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "进群欢迎"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.inviteGroupConfig = self.configData.get('InviteGroupConfig')
        self.RoleConfig= Cs.returnConfigData().get('RoleConfig')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_member_add
    def handleGroupMemberAdd(self, message: dict):
        msgData = message.get('data')
        content = msgData.get('message')
        roomId = msgData.get('fromWxId')
        robotId = msgData.get('robotId')
        inviteMsg = self.getInviteMsg(roomId)
        if inviteMsg is None and roomId not in self.inviteGroupConfig.get("whiteRooms"):
            return
        if inviteMsg is None:
            inviteMsg = self.inviteGroupConfig.get("inviteGroupMsg")

        inviteNames = self.getInviteGroupNames(content=content)
        if not inviteNames:
            return
        for name in inviteNames:
            self.bot.sendText(robotId=robotId, receive=roomId, message=f'@{name} {inviteMsg}')

    def getInviteMsg(self,roomId):
        for role in self.RoleConfig:
            if roomId in self.RoleConfig[role]['roomIds']:
                return self.RoleConfig[role]['joinRoomMsg']
        return None

    def getInviteGroupNames(self, content):
        pattern = r'(?<=邀请)"([^"]+)"|^"([^"]+)"(?=通过扫描)'
        match1 = re.search(pattern, content)
        names = match1.group(1) if match1 else None
        if not names:
            match2 = re.search(pattern, content)
            names = match2.group(2) if match2 else None
        names = names.split('、')
        return names

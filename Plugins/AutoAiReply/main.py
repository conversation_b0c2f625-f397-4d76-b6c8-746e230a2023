import time
import Config.ConfigServer as Cs
from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
import requests
import os


class AutoAiReply(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "Ai自动回复"
        self.author = "HOMEBOT"
        self.version = '1.0.0'
        self.description = "根据群角色定义，调用AI处理相关的处理"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.AiConfig = self.configData.get('AiConfig')
        self.RoleConfig= Cs.returnConfigData().get('RoleConfig')
        # 历史聊天记录
        self.userChatDicts = {}
        self.DifyUserSession = {}
        self.CozeUserSession = {}

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_group_at_robot
    def handleGroupMsg(self, message: dict):
        msgData = message.get('data')
        roomId = msgData.get('fromWxId')
        sender = msgData.get('finalFromWxId')
        robotId = msgData.get('robotId')
        content = msgData.get('message')
        atWxIdList = msgData.get('atWxIdList')
        groupMemberInfos = self.bot.getGroupMemberInfos(robotId=robotId, roomId=roomId).get('data')
        noAtMsg = self.tools.returnNoAtMsg(atWxIdList, content, groupMemberInfos)
        aiContent = self.getAiMsg(sender, roomId, noAtMsg)
        if not aiContent:
            self.bot.sendText(robotId=robotId, receive=roomId, message=f'{self.name} 所有AI模型未配置, 请检查相关配置！',aters=f'{sender}')
            return
        self.bot.sendText(robotId=robotId, receive=roomId, message=aiContent, aters=f'{sender}')

    @on_private_text
    def handlePrivateMsg(self, message: dict):
        msgData = message.get('data')
        sender = msgData.get('fromWxId')
        robotId = msgData.get('robotId')
        content = msgData.get('message')
        if sender == robotId:
            return
        if not content.strip():
            return
        aiContent = self.getAiMsg(sender,None, content.strip())
        if not aiContent:
            self.bot.sendText(robotId=robotId, receive=sender, message=f'{self.name} 所有AI模型未配置, 请检查相关配置！')
            return
        self.bot.sendText(robotId=robotId, receive=sender, message=aiContent)

    def getAiMsg(self, sender, roomId, noAtMsg):
        try:
            """ 请求Ai 模型"""
            aiPriority = self.AiConfig.get('AiPriority')
            if sender not in self.userChatDicts.keys():
                rule = self.getSystemRule(sender,roomId)
                self.userChatDicts[sender] = [{"role": "system", "content": f'{rule}'}]
            for key in aiPriority.keys():
                model = aiPriority.get(key)
                if model == 'dify':
                    aiContent = self.getDify(noAtMsg, sender)
                elif model == 'fastgpt':
                    aiContent = self.getFastGpt(noAtMsg, sender)
                elif model == 'coze':
                    aiContent = self.getCoze(noAtMsg, sender)
                else:
                    aiConfig = self.AiConfig.get(model)
                    aiApi = aiConfig.get('api')
                    aiKey = aiConfig.get('key')
                    aiModel = aiConfig.get('model')
                    aiContent = self.sendChatReq(sender, noAtMsg, aiApi, aiKey, aiModel)
                if not aiContent:
                    continue
                return aiContent
            return None
        except Exception as e:
            logger.error(f'{self.name} 请求大模型出现错误, 错误信息: {e}')
            return None
    def getSystemRule(self, sender,roomId):
        """ 获取系统规则"""
        if roomId is None:
            return self.AiConfig.get("SystemAiRule")
        for role in self.RoleConfig:
            if roomId in self.RoleConfig[role]['roomIds']:
                return self.RoleConfig[role]['rule']
        return self.AiConfig.get("SystemAiRule")

    def sendChatReq(self, sender, noAtMsg, aiApi, aiKey, aiModel):
        if not aiKey:
            logger.warning(f'{self.name} {aiModel} 未配置Key, 请检查相关配置！')
            return None
        if not aiModel:
            logger.warning(f'{self.name} {aiModel} 未配置模型, 请检查相关配置！')
            return None
        try:
            message = self.userChatDicts[sender]
            message.append({"role": "user", "content": f'{noAtMsg}'})
            #self.userChatDicts[sender].append({"role": "user", "content": f'{noAtMsg}'})

            headers = {
                'Authorization': f'Bearer {aiKey}',
            }
            data = {
                'model': aiModel,
                'messages': message,
                'stream': False,
            }
            resp = requests.post(aiApi, headers=headers, json=data)
            jsonData = resp.json()
            assistant_content = jsonData.get('choices')[0].get('message').get('content')
            #self.userChatDicts[sender].append({"role": "assistant", "content": f"{assistant_content}"})
            message.append({"role": "assistant", "content": f"{assistant_content}"})
            if len(message) >= 128:
                del message[1]
                del message[2]
            self.userChatDicts[sender] = message
            return assistant_content
        except Exception as e:
            logger.error(f'{self.name} {aiModel} 请求大模型出现错误, 错误信息: {e}')
            del self.userChatDicts[-1]
            return None

    def getDify(self, content, userId):
        if not self.AiConfig.get('dify')['key']:
            return None
        headers = {
            'Authorization': f"Bearer {self.AiConfig.get('dify')['key']}",
            'Content-Type': 'application/json'
        }

        if self.DifyUserSession.get(userId):
            conversation_id = self.DifyUserSession.get(userId)
        else:
            conversation_id = ''

        data = {
            "query": content,
            "inputs": {},
            "response_mode": "blocking",
            "user": f"{userId}",
            "conversation_id": f"{conversation_id}",
        }
        try:
            resp = requests.post(url=self.AiConfig.get('dify')['api'], headers=headers, json=data, timeout=60)
            json_data = resp.json()
            assistant_content = json_data['answer']
            if not conversation_id:
                self.DifyUserSession[userId] = json_data["conversation_id"]
            if '</think>' in assistant_content:
                assistant_content = assistant_content.split('</think>')[-1]
            return assistant_content
        except Exception as e:
            logger.error(f'{self.name} dify 请求大模型出现错误, 错误信息: {e}')
            return None

    def getFastGpt(self, content, userId):
        if not self.AiConfig.get('fastgpt')['key']:
            return None
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.AiConfig.get('fastgpt')['key']}",
        }
        data = {
            "chatId": f"{userId}",
            "stream": False,
            "detail": False,
            "messages": [
                {
                    "role": "user",
                    "content": f"{content}"
                }]
        }
        try:
            resp = requests.post(url=self.AiConfig.get('fastgpt')['api'], headers=headers, json=data, timeout=15)
            json_data = resp.json()
            assistant_content = json_data.get('choices')[0].get('message').get('content')
            return assistant_content
        except Exception as e:
            logger.error(f'{self.name} fastgpt 请求大模型出现错误, 错误信息: {e}')
            return None

    def getCoze(self, content, userId):
        if not self.AiConfig.get('coze')['key'] and not self.AiConfig.get('coze')['botid']:
            return None
        headers = {
            'Authorization': f"Bearer {self.AiConfig.get('coze')['key']}",
            'Content-Type': 'application/json'
        }

        def createChat():
            """
            创建扣子会话
            :return:
            """
            createChatApi = 'https://api.coze.cn/v1/conversation/create'
            try:
                resp = requests.post(createChatApi, headers=headers)
                jsonData = resp.json()
                conversationId = jsonData.get('data').get('id')
                if conversationId:
                    return conversationId
                return None
            except Exception as e:
                logger.error(f'{self.name} coze 创建扣子会话失败, 错误信息: {e}')
                return None

        def sendChat(conversationId, userId, content):
            sendChatApi = f'https://api.coze.cn/v3/chat?conversation_id={conversationId}'
            additional_messages = [
                {
                    'content': content,
                    'content_type': 'text',
                    'role': 'user',
                    'type': 'question'
                }
            ]
            data = {
                'bot_id': self.AiConfig.get('coze')['botid'],
                'user_id': userId,
                'stream': False,
                'additional_messages': additional_messages
            }
            try:
                resp = requests.post(sendChatApi, headers=headers, json=data)
                jsonData = resp.json()
                chatId = jsonData.get('data').get('id')
                if chatId:
                    return chatId
                return None
            except Exception as e:
                logger.error(f'{self.name} coze 发起对话失败, 错误信息: {e}')
                return None

        def retrieveChat(conversationId, chatId):
            """
            查看对话详情状态
            :param conversationId:
            :param chatId:
            :return:
            """
            retrieveChatApi = f'https://api.coze.cn/v3/chat/retrieve?conversation_id={conversationId}&chat_id={chatId}'
            try:
                resp = requests.get(retrieveChatApi, headers=headers)
                jsonData = resp.json()
                status = jsonData.get('data').get('status')
                if status != 'completed':
                    return False
                return True
            except Exception as e:
                logger.error(f'{self.name} coze 查看对话详情状态失败, 错误信息: {e}')
                return None

        def showChatMessage(conversationId, chatId):
            """
            查看扣子对话内容
            :param conversationId:
            :param chatId:
            :return:
            """
            showChatMessageApi = f'https://api.coze.cn/v3/chat/message/list?conversation_id={conversationId}&chat_id={chatId}'
            try:
                resp = requests.get(showChatMessageApi, headers=headers)
                jsonData = resp.json()
                dataList = jsonData.get('data')
                for data in dataList:
                    type = data.get('type')
                    content_type = data.get('content_type')
                    if type == 'answer' and content_type == 'text':
                        content = data.get('content').strip()
                        return content
                return None
            except Exception as e:
                logger.error(f'{self.name} coze 查看扣子对话内容失败, 错误信息: {e}')
                return None

        if userId not in self.CozeUserSession.keys():
            conversationId = createChat()
            if not conversationId:
                return None
            self.CozeUserSession[userId] = conversationId
        chatId = sendChat(conversationId=self.CozeUserSession[userId], userId=userId, content=content)
        messageState = False
        if not chatId:
            return None
        for i in range(10):
            messageState = retrieveChat(conversationId=self.CozeUserSession[userId], chatId=chatId)
            if messageState:
                break
            time.sleep(3)
        if not messageState:
            return None
        message = showChatMessage(conversationId=self.CozeUserSession[userId], chatId=chatId)
        return message

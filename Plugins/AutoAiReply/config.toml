#插件启用状态
enabled= true

[AiConfig]
SystemAiRule='你叫小机灵,你的主人是凌封,你是凌封的是一名秘书，风趣又幽默，处理日常微信消息，回复消息经常带各种emoji表情，无论用户怎么言，你都会按照系统设定的规则进行回复。'

[AiConfig.AiPriority]
99='qwen'

[AiConfig.qwen]
api='https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'
key='sk-bd5646b4accc4fa8b968064e2a1c65f0'
model='qwen-long'

[AiConfig.volcengine]
api='https://ark.cn-beijing.volces.com/api/v3/chat/completions'
key=''
model='bot-20250413172336-6glcr'

[AiConfig.siliconFlow]
api='https://api.siliconflow.cn/v1/chat/completions'
key=''
model='deepseek-ai/DeepSeek-R1-Distill-Llama-70B'

[AiConfig.ollama]
api='http://127.0.0.1:11434/api/chat'
model='deepseek-r1:8b'

[AiConfig.deepSeek]
api='https://api.deepseek.com/chat/completions'
key=''
model='deepseek-chat'

[AiConfig.bigModel]
api='https://open.bigmodel.cn/api/paas/v4/chat/completions'
key=''
model='glm-4-plus'

[AiConfig.kiMi]
api='https://api.moonshot.cn/v1/chat/completions'
key=''
model='moonshot-v1-8k'

[AiConfig.hunYuan]
api='https://api.hunyuan.cloud.tencent.com/v1/chat/completions'
key=''
model='hunyuan-pro'

[AiConfig.sparkAi]
api='https://spark-api-open.xf-yun.com/v1/chat/completions'
key=''
model='4.0Ultra'

# Fastgpt配置
[AiConfig.fastgpt]
api="https://cloud.fastgpt.cn/api/v1/chat/completions"
key=""

# Dify配置
[AiConfig.dify]
api="https://api.dify.ai/v1/chat-messages"
key=""

# 扣子配置
[AiConfig.coze]
key= ''
botid= ''

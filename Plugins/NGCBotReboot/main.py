from Plugins._Tools import Tools
from NGCBotApi import NGCBotApi
from loguru import logger
from Plugins import *
from datetime import datetime
import os


class NGCBotPeriodicMonitor(PluginBase):
    def __init__(self):
        super().__init__()
        self.name = "自动StarBot掉线重启"
        self.author = "NGC660Ai研究院"
        self.version = '1.0.0'
        self.description = "定时StarBot掉线重启"
        self.tools = Tools()
        self.bot = NGCBotApi()
        self.configData = self.tools.returnConfigData(os.path.dirname(__file__))
        self.sbConfig = self.configData.get('StarBotPathConfig')

    def on_load(self) -> None:
        super().on_load()
        logger.success(f"{self.name} {self.version} 已加载")

    @on_scheduled_task(interval=120)
    def periodicMonitor(self, ):
        """
        定时微信监测, 120s一次
        :return:
        """
        try:
            time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            botDicts = self.bot.getRobotList(1)
            status = botDicts.get('status')
            if status == -1:
                logger.success(f'监测到StarBot已掉线, 已自动重启！')
                os.system(f'cd {self.sbConfig.get("SbPath")} && start {self.sbConfig.get("sbExe")}')
                return
            botData = botDicts.get('data')
            if not botData:
                logger.error(f'微信被关闭, 请检查微信是否开启！！')
        except Exception as e:
            logger.error(f'定时微信重启监测出现错误, 错误信息: {e}')



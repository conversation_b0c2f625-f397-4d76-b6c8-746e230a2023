from .InterFaceApi import sendPostReq, sendGetReq
from loguru import logger


class BotApi:
    def __init__(self):
        pass

    def getRobotList(self, status: int = 1):
        """
        获取机器人列表
        :param status:
        :return:
        """
        try:
            params = {
                'status': status
            }
            jsonData = sendGetReq('getRobotList', params=params)
            return jsonData
        except Exception as e:
            logger.error(f'获取机器人列表出现错误, 错误信息: {e}')
            return {}

    def deleteOffRobot(self, instanceId: str = ""):
        """
        删除离线的机器人
        :param instanceId:
        :return:
        """
        try:
            params = {
                'instanceId': instanceId
            }
            jsonData = sendGetReq('deleteOffRobot', params=params)
            return jsonData
        except Exception as e:
            logger.error(f'删除离线的机器人出现错误, 错误信息: {e}')
            return {}

    def getRobotInfo(self, wxId: str = "", instanceId: str = "", cache: int = 1):
        """
        获取机器人信息
        :param wxId:
        :param instanceId:
        :param cache:
        :return:
        """
        try:
            params = {
                'wxId': wxId,
                'instanceId': instanceId,
                'cache': cache
            }
            jsonData = sendGetReq('getRobotInfo', params=params)
            return jsonData
        except Exception as e:
            logger.error(f'获取机器人信息出现错误, 错误信息: {e}')
            return {}


if __name__ == '__main__':
    ba = BotApi()
    print(ba.getRobotList(1))

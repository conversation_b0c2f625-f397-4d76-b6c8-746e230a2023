from .InterFaceApi import sendPostReq
from loguru import logger

class ToolsApi:
    def __init__(self):
        pass

    def decryptImg(self, robotId: str = "", instanceId: str = "", srcFile: str = "", saveFile: str = ""):
        """
        解密图片
        :param robotId:
        :param instanceId:
        :param srcFile:
        :param saveFile:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'srcFile': srcFile,
                'saveFile': saveFile
            }
            jsonData = sendPostReq('decryptImg', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'解密图片出现错误, 错误信息: {e}')
            return {}

    def getLocalPicBase64(self, imgPath: str = ""):
        """
        获取本地图片Base64编码
        :param imgPath:
        :return:
        """
        try:
            data = {
                'imgPath': imgPath,
            }
            jsonData = sendPostReq('getLocalPicBase64', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'获取本地图片Base64编码出现错误, 错误信息: {e}')
            return {}

    def confirmTrans(self, robotId: str = "", instanceId: str = "", wxId: str = "", transcationId: str = "",
                     transferId: str = ""):
        """
        接收转账
        :param robotId:
        :param instanceId:
        :param wxId:
        :param transcationId:
        :param transferId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'wxId': wxId,
                'transcationId': transcationId,
                'transferId': transferId
            }
            jsonData = sendPostReq('confirmTrans', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'接收转账出现错误, 错误信息: {e}')
            return {}

    def returnTrans(self, robotId: str = "", instanceId: str = "", wxId: str = "", transcationId: str = "",
                    transferId: str = ""):
        """
        退还转账
        :param robotId:
        :param instanceId:
        :param wxId:
        :param transcationId:
        :param transferId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'wxId': wxId,
                'transcationId': transcationId,
                'transferId': transferId
            }
            jsonData = sendPostReq('returnTrans', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'退还转账出现错误, 错误信息: {e}')
            return {}

from .InterFaceApi import sendPostReq
from loguru import logger


class ChatRoomApi:
    def __init__(self):
        pass

    def addFriendFromGroup(self, robotId: str = "", instanceId: str = "", friendWxId: str = "", roomId: str = "",
                           content: str = ""):
        """
        从群聊添加好友
        :param robotId:
        :param instanceId:
        :param friendWxId:
        :param roomId:
        :param content:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'friendWxId': friendWxId,
                'roomId': roomId,
                'content': content
            }
            jsonData = sendPostReq('addFriendFromGroup', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'从群聊添加好友出现错误, 错误信息: {e}')
            return {}

    def quitGroup(self, robotId: str = "", instanceId: str = "", roomId: str = ""):
        """
        退出群聊
        :param robotId:
        :param instanceId:
        :param roomId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'roomId': roomId
            }
            jsonData = sendPostReq('quitGroup', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'退出群聊出现错误, 错误信息: {e}')
            return {}

    def createGroup(self, robotId: str = "", instanceId: str = "", memberWxIdList: str = ""):
        """
        创建群聊
        :param robotId:
        :param instanceId:
        :param memberWxIdList:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'memberWxIdList': memberWxIdList
            }
            jsonData = sendPostReq('createGroup', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'创建群聊出现错误, 错误信息: {e}')
            return {}

    def getGroupInfo(self, robotId: str = "", instanceId: str = "", roomId: str = ""):
        """
        查询群聊信息(协议)
        :param robotId:
        :param instanceId:
        :param roomId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'roomId': roomId
            }
            jsonData = sendPostReq('getGroupInfo', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'查询群聊信息(协议)出现错误, 错误信息: {e}')
            return {}

    def getGroupUerInfo(self, robotId: str = "", instanceId: str = "", roomId: str = "", wxId: str = ""):
        """
        查询群成员信息
        :param robotId:
        :param instanceId:
        :param roomId:
        :param wxId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'roomId': roomId
            }
            membersData = sendPostReq('getGroupMemberInfos', data=data)
            if membersData.get('status') == 200:
                members = membersData.get('data')
                for key in members.keys():
                    if key == wxId:
                        return members[key]
            return {}
        except Exception as e:
            logger.error(f'查询群成员列表信息出现错误, 错误信息: {e}')
            return {}

    def getGroupMemberInfos(self, robotId: str = "", instanceId: str = "", roomId: str = ""):
        """
        查询群成员列表信息
        :param robotId:
        :param instanceId:
        :param roomId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'roomId': roomId
            }
            jsonData = sendPostReq('getGroupMemberInfos', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'查询群成员列表信息出现错误, 错误信息: {e}')
            return {}

    def getRoomList(self, robotId: str = "", instanceId: str = "", cache: int = 1):
        """
        获取群聊列表
        :param robotId:
        :param instanceId:
        :param cache:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'cache': cache
            }
            jsonData = sendPostReq('getRoomList', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'获取群聊列表出现错误, 错误信息: {e}')
            return {}

    def addMember(self, robotId: str = "", instanceId: str = "", roomId: str = "", memberWxId: str = ""):
        """
        添加群成员(40人以下)
        :param robotId:
        :param instanceId:
        :param roomId:
        :param memberWxId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'roomId': roomId,
                'memberWxId': memberWxId
            }
            jsonData = sendPostReq('addMember', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'添加群成员(40人以下)出现错误, 错误信息: {e}')
            return {}

    def inviteMember(self, robotId: str = "", instanceId: str = "", roomId: str = "", memberWxId: str = ""):
        """
        邀请群成员
        :param robotId:
        :param instanceId:
        :param roomId:
        :param memberWxId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'roomId': roomId,
                'memberWxId': memberWxId
            }
            jsonData = sendPostReq('inviteMember', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'邀请群成员出现错误, 错误信息: {e}')
            return {}

    def deleteMember(self, robotId: str = "", instanceId: str = "", roomId: str = "", memberWxId: str = ""):
        """
        删除群成员
        :param robotId:
        :param instanceId:
        :param roomId:
        :param memberWxId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'roomId': roomId,
                'memberWxId': memberWxId
            }
            jsonData = sendPostReq('deleteMember', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'删除群成员出现错误, 错误信息: {e}')
            return {}

    def changeBotRoomName(self, robotId: str = "", instanceId: str = "", roomId: str = "", nickname: str = ""):
        """
        修改机器人群聊昵称
        :param robotId:
        :param instanceId:
        :param roomId:
        :param nickname:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'roomId': roomId,
                'nickname': nickname
            }
            jsonData = sendPostReq('changeBotRoomName', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'修改机器人群聊昵称出现错误, 错误信息: {e}')
            return {}


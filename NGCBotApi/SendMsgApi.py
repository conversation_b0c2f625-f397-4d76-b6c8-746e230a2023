from .InterFaceApi import sendPostReq
from loguru import logger


class SendMsgApi:
    def __init__(self):
        pass

    def sendText(self, robotId: str = "", instanceId: str = "", receive: str = "", message: str = "", aters: str = ""):
        """
        发送文本消息
        :param robotId:
        :param instanceId:
        :param receive:
        :param message:
        :param aters:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'message': message,
                'aters': aters
            }
            jsonData = sendPostReq('sendText', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送文本消息出现错误, 错误信息: {e}')
            return {}

    def sendMedium(self, robotId: str = "", instanceId: str = "", receive: str = "", mediumPath: str = ""):
        """
        发送媒体消息 图片&视频
        :param robotId:
        :param instanceId:
        :param receive:
        :param mediumPath:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'mediumPath': mediumPath,
            }
            jsonData = sendPostReq('sendMedium', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送媒体消息 图片&视频出现错误, 错误信息: {e}')
            return {}

    def sendFile(self, robotId: str = "", instanceId: str = "", receive: str = "", filePath: str = ""):
        """
        发送文件
        :param robotId:
        :param instanceId:
        :param receive:
        :param filePath:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'filePath': filePath,
            }
            jsonData = sendPostReq('sendFile', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送文件出现错误, 错误信息: {e}')
            return {}

    def sendGif(self, robotId: str = "", instanceId: str = "", receive: str = "", gifPath: str = ""):
        """
        发送GIF表情消息
        :param robotId:
        :param instanceId:
        :param receive:
        :param gifPath:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'gifPath': gifPath,
            }
            jsonData = sendPostReq('sendGif', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送GIF表情消息出现错误, 错误信息: {e}')
            return {}

    def sendRich(self, robotId: str = "", instanceId: str = "", receive: str = "", title: str = "", content: str = "",
                 url: str = "", appCode: str = "", imgPath: str = ""):
        """
        发送卡片消息
        :param robotId:
        :param instanceId:
        :param receive:
        :param title:
        :param content:
        :param url:
        :param appCode:
        :param imgPath:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'title': title,
                'content': content,
                'url': url,
                'appCode': appCode,
                'imgPath': imgPath
            }
            jsonData = sendPostReq('sendRich', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送卡片消息出现错误, 错误信息: {e}')
            return {}

    def sendCardXml(self, robotId: str = "", instanceId: str = "", receive: str = "", xml: str = ""):
        """
        发送名片类型XML消息
        :param robotId:
        :param instanceId:
        :param receive:
        :param xml:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'xml': xml,
            }
            jsonData = sendPostReq('sendCardXml', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送名片类型XML消息出现错误, 错误信息: {e}')
            return {}

    def sendCard(self, robotId: str = "", instanceId: str = "", receive: str = "", friendWxId: str = "",
                 friendName: str = ""):
        """
        发送好友名片
        :param robotId:
        :param instanceId:
        :param receive:
        :param friendWxId:
        :param friendName:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'friendWxId': friendWxId,
                'friendName': friendName
            }
            jsonData = sendPostReq('sendCard', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送好友名片消息出现错误, 错误信息: {e}')
            return {}

    def sendXml(self, robotId: str = "", instanceId: str = "", receive: str = "", xml: str = ""):
        """
        发送XML消息
        :param robotId:
        :param instanceId:
        :param receive:
        :param xml:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'xml': xml,
            }
            jsonData = sendPostReq('sendXml', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送XML消息出现错误, 错误信息: {e}')
            return {}

    def sendApp(self, robotId: str = "", instanceId: str = "", receive: str = "", title: str = "", content: str = "",
                url: str = "", appCode: str = "", imgPath: str = ""):
        """
        发送小程序消息
        :param robotId:
        :param instanceId:
        :param receive:
        :param title:
        :param content:
        :param url:
        :param appCode:
        :param imgPath:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'title': title,
                'content': content,
                'url': url,
                'appCode': appCode,
                'imgPath': imgPath
            }
            jsonData = sendPostReq('sendApp', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送小程序消息出现错误, 错误信息: {e}')
            return {}

    def sendMusic(self, robotId: str = "", instanceId: str = "", receive: str = "", title: str = "", author: str = "",
                  jumpUrl: str = "", musicUrl: str = "", imgUrl: str = "", appCode: str = ""):
        """
        发送音乐卡片消息
        :param robotId:
        :param instanceId:
        :param receive:
        :param title:
        :param author:
        :param jumpUrl:  不传参即可
        :param musicUrl:
        :param imgUrl:
        :param appCode:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'title': title,
                'author': author,
                'jumpUrl': jumpUrl,
                'musicUrl': musicUrl,
                'imgUrl': imgUrl,
                'appCode': appCode
            }
            jsonData = sendPostReq('sendMusic', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送音乐卡片消息出现错误, 错误信息: {e}')
            return {}

    def sendQuoteMsg(self, robotId: str = "", instanceId: str = "", receive: str = "", message: str = "",
                     messageId: str = "", messageFromWxId: str = "", messageFromContent: str = "",
                     messageFromName: str = "", at: bool = True, atUserName: str = ""):
        """
        发送引用回复消息
        :param robotId:
        :param instanceId:
        :param receive:
        :param message:
        :param messageId:
        :param messageFromWxId:
        :param messageFromContent:
        :param messageFromName:
        :param at:
        :param atUserName:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'message': message,
                'messageId': messageId,
                'messageFromWxId': messageFromWxId,
                'messageFromContent': messageFromContent,
                'messageFromName': messageFromName,
                'at': at,
                'atUserName': atUserName
            }
            jsonData = sendPostReq('sendQuoteMsg', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送引用回复消息出现错误, 错误信息: {e}')
            return {}

    def withdrawMsg(self, robotId: str = "", instanceId: str = "", receive: str = "", clientMessageId: int = 0,
                    createTime: int = 0, newMessageId: str = ""):
        """
        撤回消息
        :param robotId:
        :param instanceId:
        :param receive:
        :param clientMessageId:
        :param createTime:
        :param newMessageId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'clientMessageId': clientMessageId,
                'createTime': createTime,
                'newMessageId': newMessageId,
            }
            jsonData = sendPostReq('withdrawMsg', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'撤回消息出现错误, 错误信息: {e}')
            return {}

    def sendChatLog(self, robotId: str = "", instanceId: str = "", receive: str = "", title: str = "",
                    dataList: str = ""):
        """
        发送聊天记录(未实现)
        :param robotId:
        :param instanceId:
        :param receive:
        :param title:
        :param dataList:
        :return:
        """
        try:
            raise ValueError('发送聊天记录消息接口未实现')
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'title': title,
                'dataList': dataList,
            }
            jsonData = sendPostReq('sendChatLog', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送聊天记录出现错误, 错误信息: {e}')
            return {}

    def sendVoice(self, robotId: str = "", instanceId: str = "", receive: str = "", voicePath: str = "", duration: int = 0):
        """
        发送语音消息
        :param robotId:
        :param instanceId:
        :param receive:
        :param voicePath:
        :param duration:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'voicePath': voicePath,
                'duration': duration,
            }
            jsonData = sendPostReq('sendVoice', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'发送语音消息出现错误, 错误信息: {e}')
            return {}



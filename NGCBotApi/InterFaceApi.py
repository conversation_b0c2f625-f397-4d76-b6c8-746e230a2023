from Config.ConfigServer import *

import requests

NGCBotConfig = returnConfigData().get('NGCBotConfig')
NGCBotApi = NGCBotConfig.get('NGCBotApi')
NGCBotPort = NGCBotConfig.get('NGCBotPort')
NGCBotKey = NGCBotConfig.get('NGCBotKey')


def sendPostReq(reqPath: str, data: dict):
    """
    发送通用POST请求
    :param reqPath:
    :param data:
    :return:
    """
    api = f'http://{NGCBotApi.replace("0.0.0.0", "127.0.0.1")}:{NGCBotPort}/{reqPath}'
    try:
        headers = {
            'NGCBotKey': NGCBotKey
        }
        resp = requests.post(api, headers=headers, json=data, timeout=10)
        jsonData = resp.json()
        return jsonData
    except Exception as e:
        raise e


def sendGetReq(reqPath: str, params: dict):
    """
    发送通用GET请求
    :param reqPath:
    :param params:
    :return:
    """
    api = f'http://{NGCBotApi.replace("0.0.0.0", "127.0.0.1")}:{NGCBotPort}/{reqPath}'
    try:
        headers = {
            'NGCBotKey': NGCBotKey
        }
        resp = requests.get(api, headers=headers, params=params, timeout=10)
        jsonData = resp.json()
        return jsonData
    except Exception as e:
        raise e

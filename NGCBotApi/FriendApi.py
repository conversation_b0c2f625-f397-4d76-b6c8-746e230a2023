from .InterFaceApi import sendPostReq
from loguru import logger


class FriendApi:
    def __init__(self):
        pass

    def acceptFriend(self, robotId: str = "", instanceId: str = "", v3: str = "", v4: str = "", scene: str = "13",
                     role: str = "0"):
        """
        同意好友申请
        :param robotId:
        :param instanceId:
        :param v3:
        :param v4:
        :param scene:
        :param role:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'v3': v3,
                'v4': v4,
                'scene': scene,
                'role': role
            }
            jsonData = sendPostReq('acceptFriend', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'同意好友申请出现错误, 错误信息: {e}')
            return {}

    def addFriend(self, robotId: str = "", instanceId: str = "", v3: str = "", v4: str = "", content: str = "",
                  scene: str = "13"):
        """
        添加好友 (搜索的微信用户)
        :param robotId: 
        :param instanceId: 
        :param v3: 
        :param v4: 
        :param content: 
        :param scene: 
        :return: 
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'v3': v3,
                'v4': v4,
                'content': content,
                'scene': scene
            }
            jsonData = sendPostReq('addFriend', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'添加好友 (搜索的微信用户)出现错误, 错误信息: {e}')
            return {}
    
    def deleteFriend(self, robotId: str = "", instanceId: str = "", friendWxId: str = ""):
        """
        删除好友
        :param robotId: 
        :param instanceId: 
        :param friendWxId: 
        :return: 
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'friendWxId': friendWxId,
            }
            jsonData = sendPostReq('deleteFriend', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'删除好友出现错误, 错误信息: {e}')
            return {}
    
    def changeRemark(self, robotId: str = "", instanceId: str = "", receive: str = "", remark: str = ""):
        """
        修改好友&群聊备注
        :param robotId:
        :param instanceId:
        :param receive:
        :param remark:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'receive': receive,
                'remark': remark,
            }
            jsonData = sendPostReq('changeRemark', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'修改好友&群聊备注出现错误, 错误信息: {e}')
            return {}

    def getAccountInfo(self, robotId: str = "", instanceId: str = "", account: str = ""):
        """
        查询陌生人信息
        :param robotId:
        :param instanceId:
        :param account:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'account': account,
            }
            jsonData = sendPostReq('getAccountInfo', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'查询陌生人信息出现错误, 错误信息: {e}')
            return {}

    def getFriendList(self, robotId: str = "", instanceId: str = "", cache: int = 1):
        """
        获取好友列表
        :param robotId:
        :param instanceId:
        :param cache:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'cache': cache,
            }
            jsonData = sendPostReq('getFriendList', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'获取好友列表出现错误, 错误信息: {e}')
            return {}

    def getFriendInfo(self, robotId: str = "", instanceId: str = "", friendWxId: str = ""):
        """
        获取好友信息
        :param robotId:
        :param instanceId:
        :param friendWxId:
        :return:
        """
        try:
            data = {
                'robotId': robotId,
                'instanceId': instanceId,
                'friendWxId': friendWxId,
            }
            jsonData = sendPostReq('getFriendInfo', data=data)
            return jsonData
        except Exception as e:
            logger.error(f'获取好友信息出现错误, 错误信息: {e}')
            return {}


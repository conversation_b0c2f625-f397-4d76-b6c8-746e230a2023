from .InterFaceApi import sendGetReq
from loguru import logger


class LoginApi:
    def __init__(self):
        pass

    def startWechat(self, instanceId: str = ""):
        """
        启动微信
        :param instanceId:
        :return:
        """
        try:
            params = {
                'instanceId': instanceId
            }
            jsonData = sendGetReq('startWechat', params=params)
            return jsonData
        except Exception as e:
            logger.error(f'启动微信出现错误, 错误信息: {e}')
            return {}

    def closeWechat(self, instanceId: str = ""):
        """关闭微信
        :param instanceId:
        :return:
        """
        try:
            params = {
                'instanceId': instanceId
            }
            jsonData = sendGetReq('closeWechat', params=params)
            return jsonData
        except Exception as e:
            logger.error(f'关闭微信出现错误, 错误信息: {e}')
            return {}

    def getLoginQrCode(self, instanceId: str = ""):
        """
        获取登录二维码
        :param instanceId:
        :return:
        """
        try:
            params = {
                'instanceId': instanceId
            }
            jsonData = sendGetReq('getLoginQrCode', params=params)
            return jsonData
        except Exception as e:
            logger.error(f'关闭微信出现错误, 错误信息: {e}')
            return {}
